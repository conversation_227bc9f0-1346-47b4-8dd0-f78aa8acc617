generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model Settings {
  id              String   @id @default(uuid())
  schoolName      String
  maintenanceMode Boolean  @default(false)
  defaultCurrency String   @default("USD")
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
}

model User {
  id        String   @id @default(uuid())
  name      String
  email     String   @unique
  password  String
  role      String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Course {
  id          String      @id @default(uuid())
  name        String
  description String
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  students    Student[]
  levels      Level[]
}

model Level {
  id          String   @id @default(uuid())
  name        String
  description String
  courseId    String
  course      Course   @relation(fields: [courseId], references: [id])
  students    Student[]
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model Student {
  id                   String   @id @default(uuid())
  studentId            String   @unique
  name                 String
  dateOfBirth         DateTime
  dateOfRegistration  DateTime @default(now())
  nationality         String
  gender              String
  address             String
  phoneNumber         String
  whatsappNumber      String
  parentName          String
  parentPhoneNumber   String
  parentWhatsappNumber String
  passportPicture     String?
  courseId            String
  course              Course   @relation(fields: [courseId], references: [id])
  levelId             String
  level               Level    @relation(fields: [levelId], references: [id])
  enrollmentStatus    String
  paymentStatus       String
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt
  payments            Payment[]
  academicRecords     AcademicRecord[]
}

model Payment {
  id              String   @id @default(uuid())
  studentId       String
  student         Student  @relation(fields: [studentId], references: [id])
  amount          Float
  datePaid        DateTime
  paymentDueDate  DateTime
  status          String
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
}

model Transaction {
  id          String   @id @default(uuid())
  date        DateTime
  description String
  category    String
  type        String
  amount      Float
  status      String
  notes       String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model AcademicRecord {
  id        String   @id @default(uuid())
  studentId String
  student   Student  @relation(fields: [studentId], references: [id])
  term      String
  subject   String
  grade     String
  status    String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}
