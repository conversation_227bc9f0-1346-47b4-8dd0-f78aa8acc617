-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create courses table
CREATE TABLE IF NOT EXISTS courses (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    name text NOT NULL,
    code text NOT NULL UNIQUE,
    description text,
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create levels table
CREATE TABLE IF NOT EXISTS levels (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    name text NOT NULL,
    code text NOT NULL,
    course_id uuid REFERENCES courses(id) ON DELETE CASCADE,
    description text,
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
    UNIQUE(code, course_id)
);

-- Create students table
CREATE TABLE IF NOT EXISTS students (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    name text NOT NULL,
    student_id text NOT NULL UNIQUE,
    address text,
    course_id uuid REFERENCES courses(id) ON DELETE RESTRICT,
    level_id uuid REFERENCES levels(id) ON DELETE RESTRICT,
    date_of_birth date NOT NULL,
    date_of_registration date DEFAULT CURRENT_DATE,
    enrollment_status text DEFAULT 'Active',
    gender text NOT NULL,
    mobile_number text,
    parent_email text,
    parent_mobile text,
    parent_name text,
    parent_occupation text,
    parent_whatsapp text,
    whatsapp_number text,
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = timezone('utc'::text, now());
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at columns
CREATE TRIGGER update_courses_updated_at
    BEFORE UPDATE ON courses
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_levels_updated_at
    BEFORE UPDATE ON levels
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_students_updated_at
    BEFORE UPDATE ON students
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_students_course_id ON students(course_id);
CREATE INDEX IF NOT EXISTS idx_students_level_id ON students(level_id);
CREATE INDEX IF NOT EXISTS idx_levels_course_id ON levels(course_id);

-- Enable Row Level Security (RLS)
ALTER TABLE courses ENABLE ROW LEVEL SECURITY;
ALTER TABLE levels ENABLE ROW LEVEL SECURITY;
ALTER TABLE students ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for courses
CREATE POLICY "Enable read access for authenticated users on courses"
    ON courses FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Enable insert for authenticated users on courses"
    ON courses FOR INSERT
    TO authenticated
    WITH CHECK (true);

CREATE POLICY "Enable update for authenticated users on courses"
    ON courses FOR UPDATE
    TO authenticated
    USING (true)
    WITH CHECK (true);

CREATE POLICY "Enable delete for authenticated users on courses"
    ON courses FOR DELETE
    TO authenticated
    USING (true);

-- Create RLS policies for levels
CREATE POLICY "Enable read access for authenticated users on levels"
    ON levels FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Enable insert for authenticated users on levels"
    ON levels FOR INSERT
    TO authenticated
    WITH CHECK (true);

CREATE POLICY "Enable update for authenticated users on levels"
    ON levels FOR UPDATE
    TO authenticated
    USING (true)
    WITH CHECK (true);

CREATE POLICY "Enable delete for authenticated users on levels"
    ON levels FOR DELETE
    TO authenticated
    USING (true);

-- Create RLS policies for students
CREATE POLICY "Enable read access for authenticated users on students"
    ON students FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Enable insert for authenticated users on students"
    ON students FOR INSERT
    TO authenticated
    WITH CHECK (true);

CREATE POLICY "Enable update for authenticated users on students"
    ON students FOR UPDATE
    TO authenticated
    USING (true)
    WITH CHECK (true);

CREATE POLICY "Enable delete for authenticated users on students"
    ON students FOR DELETE
    TO authenticated
    USING (true);
