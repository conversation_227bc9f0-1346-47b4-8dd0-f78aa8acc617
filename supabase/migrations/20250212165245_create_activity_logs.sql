-- Create activity_logs table
CREATE TABLE IF NOT EXISTS activity_logs (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id uuid REFERENCES auth.users(id),
    activity_type text NOT NULL CHECK (activity_type IN (
        'login',
        'logout',
        'student_created',
        'student_updated',
        'student_deleted',
        'attendance_marked',
        'attendance_updated',
        'course_created',
        'course_updated',
        'course_deleted',
        'level_created',
        'level_updated',
        'level_deleted',
        'settings_updated'
    )),
    description text NOT NULL,
    metadata jsonb DEFAULT '{}'::jsonb,
    ip_address text,
    user_agent text,
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create index for faster queries
CREATE INDEX IF NOT EXISTS idx_activity_logs_user_id ON activity_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_activity_logs_activity_type ON activity_logs(activity_type);
CREATE INDEX IF NOT EXISTS idx_activity_logs_created_at ON activity_logs(created_at);

-- Create RLS policies for activity_logs
ALTER TABLE activity_logs ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Enable read access for authenticated users"
    ON activity_logs FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Enable insert for authenticated users"
    ON activity_logs FOR INSERT
    TO authenticated
    WITH CHECK (true); 