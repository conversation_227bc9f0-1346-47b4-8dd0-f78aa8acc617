-- Create courses table if it doesn't exist
CREATE TABLE IF NOT EXISTS courses (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    name text NOT NULL,
    code text NOT NULL UNIQUE,
    description text,
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create levels table if it doesn't exist
CREATE TABLE IF NOT EXISTS levels (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    name text NOT NULL,
    code text NOT NULL,
    course_id uuid REFERENCES courses(id) ON DELETE CASCADE,
    description text,
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
    UNIQUE(code, course_id)
);

-- Update students table to reference both course and level
ALTER TABLE students
DROP CONSTRAINT IF EXISTS students_course_id_fkey,
DROP CONSTRAINT IF EXISTS students_level_id_fkey,
ADD CONSTRAINT students_course_id_fkey 
    FOREIGN KEY (course_id) 
    REFERENCES courses(id) 
    ON DELETE RESTRICT,
ADD CONSTRAINT students_level_id_fkey 
    FOREIGN KEY (level_id) 
    REFERENCES levels(id) 
    ON DELETE RESTRICT;

-- Create RLS policies for courses
ALTER TABLE courses ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Enable read access for authenticated users on courses"
    ON courses FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Enable insert for authenticated users on courses"
    ON courses FOR INSERT
    TO authenticated
    WITH CHECK (true);

CREATE POLICY "Enable update for authenticated users on courses"
    ON courses FOR UPDATE
    TO authenticated
    USING (true)
    WITH CHECK (true);

CREATE POLICY "Enable delete for authenticated users on courses"
    ON courses FOR DELETE
    TO authenticated
    USING (true);

-- Create RLS policies for levels
ALTER TABLE levels ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Enable read access for authenticated users on levels"
    ON levels FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Enable insert for authenticated users on levels"
    ON levels FOR INSERT
    TO authenticated
    WITH CHECK (true);

CREATE POLICY "Enable update for authenticated users on levels"
    ON levels FOR UPDATE
    TO authenticated
    USING (true)
    WITH CHECK (true);

CREATE POLICY "Enable delete for authenticated users on levels"
    ON levels FOR DELETE
    TO authenticated
    USING (true);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_levels_course_id ON levels(course_id);
CREATE INDEX IF NOT EXISTS idx_students_course_level ON students(course_id, level_id);