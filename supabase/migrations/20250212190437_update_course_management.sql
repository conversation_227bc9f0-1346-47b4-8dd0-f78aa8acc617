-- Add new columns to courses table
ALTER TABLE courses
ADD COLUMN start_date DATE,
ADD COLUMN end_date DATE,
ADD COLUMN duration_months INTEGER,
ADD COLUMN auto_renewal BOOLEAN DEFAULT false,
ADD COLUMN renewal_period_months INTEGER,
ADD COLUMN parent_course_id UUID REFERENCES courses(id),
ADD COLUMN course_type TEXT CHECK (course_type IN ('main', 'section', 'subsection')),
ADD COLUMN section_order INTEGER;

-- Create course_notifications table
CREATE TABLE IF NOT EXISTS course_notifications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    course_id UUID REFERENCES courses(id) ON DELETE CASCADE,
    notification_type TEXT CHECK (notification_type IN ('expiry_warning', 'renewal_notice', 'course_expired')),
    message TEXT NOT NULL,
    sent_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    sent_to UUID REFERENCES auth.users(id),
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'failed')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create course_renewals table to track renewal history
CREATE TABLE IF NOT EXISTS course_renewals (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    course_id UUID REFERENCES courses(id) ON DELETE CASCADE,
    previous_end_date DATE NOT NULL,
    new_end_date DATE NOT NULL,
    renewal_type TEXT CHECK (renewal_type IN ('automatic', 'manual')),
    renewed_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_courses_parent_id ON courses(parent_course_id);
CREATE INDEX IF NOT EXISTS idx_courses_type ON courses(course_type);
CREATE INDEX IF NOT EXISTS idx_course_notifications_course ON course_notifications(course_id);
CREATE INDEX IF NOT EXISTS idx_course_renewals_course ON course_renewals(course_id);

-- Add RLS policies for new tables
ALTER TABLE course_notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE course_renewals ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Enable read access for authenticated users on course_notifications"
    ON course_notifications FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Enable insert for authenticated users on course_notifications"
    ON course_notifications FOR INSERT
    TO authenticated
    WITH CHECK (true);

CREATE POLICY "Enable read access for authenticated users on course_renewals"
    ON course_renewals FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Enable insert for authenticated users on course_renewals"
    ON course_renewals FOR INSERT
    TO authenticated
    WITH CHECK (true);

-- Function to check for expiring courses and create notifications
CREATE OR REPLACE FUNCTION check_expiring_courses()
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
    -- Create notifications for courses expiring in 30 days
    INSERT INTO course_notifications (course_id, notification_type, message)
    SELECT 
        id,
        'expiry_warning',
        'Course "' || name || '" is expiring in 30 days'
    FROM courses
    WHERE end_date = CURRENT_DATE + INTERVAL '30 days'
    AND NOT EXISTS (
        SELECT 1 FROM course_notifications cn
        WHERE cn.course_id = courses.id
        AND cn.notification_type = 'expiry_warning'
        AND cn.created_at > CURRENT_DATE - INTERVAL '30 days'
    );

    -- Handle automatic renewals for expired courses
    INSERT INTO course_renewals (course_id, previous_end_date, new_end_date, renewal_type)
    SELECT 
        id,
        end_date,
        end_date + (renewal_period_months || ' months')::INTERVAL,
        'automatic'
    FROM courses
    WHERE auto_renewal = true
    AND end_date <= CURRENT_DATE
    AND NOT EXISTS (
        SELECT 1 FROM course_renewals cr
        WHERE cr.course_id = courses.id
        AND cr.created_at > CURRENT_DATE - INTERVAL '1 day'
    );

    -- Update course end dates for automatic renewals
    UPDATE courses
    SET end_date = cr.new_end_date
    FROM course_renewals cr
    WHERE courses.id = cr.course_id
    AND cr.created_at > CURRENT_DATE - INTERVAL '1 day'
    AND cr.renewal_type = 'automatic';
END;
$$; 