# Google Drive Integration Setup

This document outlines the steps to set up Google Drive API integration for file storage in the Academic Dashboard.

## Overview

The application uses Google Drive for storing:
- Student profile photos
- Educational materials
- Assignment submissions

## Prerequisites

1. A Google Cloud Platform (GCP) account
2. A GCP project with the Google Drive API enabled
3. A service account with appropriate permissions

## Setup Steps

### 1. Create a Google Cloud Platform Project

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Note your Project ID for later use

### 2. Enable the Google Drive API

1. In your GCP project, navigate to "APIs & Services" > "Library"
2. Search for "Google Drive API" and enable it

### 3. Create a Service Account

1. Go to "IAM & Admin" > "Service Accounts"
2. Click "Create Service Account"
3. Enter a name and description for your service account
4. Grant the service account the following roles:
   - "Drive File Creator" (for creating files)
   - "Drive File Organizer" (for organizing files in folders)
   - "Drive Viewer" (for viewing files)
5. Click "Create" and then "Done"

### 4. Generate Service Account Key

1. Find your service account in the list and click on it
2. Go to the "Keys" tab
3. Click "Add Key" > "Create new key"
4. Select "JSON" as the key type and click "Create"
5. The key file will be downloaded to your computer

### 5. Configure Environment Variables

Add the following environment variables to your `.env` file:

```
VITE_GOOGLE_SERVICE_ACCOUNT_EMAIL="<EMAIL>"
VITE_GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour private key here\n-----END PRIVATE KEY-----"
```

Notes:
- Replace `<EMAIL>` with your service account email
- For the private key, copy the entire private key from the JSON file, including the `-----BEGIN PRIVATE KEY-----` and `-----END PRIVATE KEY-----` parts
- Make sure to replace newlines with `\n` in the private key

### 6. Folder Structure

The application organizes files in Google Drive using the following folder structure:

- `profile_photos/` - For student profile photos
- `materials/` - For educational materials
- `submissions/` - For assignment submissions
  - `{student_id}/` - Folders for each student
    - `{assignment_id}/` - Folders for each assignment

## Usage

The integration is used in the following components:

1. `StudentImageUpload.tsx` - For uploading student profile photos
2. `materials.ts` - For uploading and managing educational materials
3. `Assignments.tsx` - For uploading and managing assignment submissions

## Troubleshooting

### Common Issues

1. **Authentication Errors**
   - Verify that your service account email and private key are correctly set in the environment variables
   - Ensure the private key is properly formatted with `\n` for newlines

2. **Permission Errors**
   - Check that your service account has the necessary permissions
   - Verify that the Google Drive API is enabled in your GCP project

3. **File Access Issues**
   - Files are set to be publicly accessible for viewing by default
   - If files are not accessible, check the permissions settings in the Google Drive API

### Logging

The application logs all Google Drive operations to help with debugging:
- File uploads
- Folder creation
- File deletion

Check the application logs for any errors related to Google Drive operations. 