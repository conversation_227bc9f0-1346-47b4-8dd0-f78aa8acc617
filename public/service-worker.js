// Service Worker for PTECH Academic Management System
const CACHE_NAME = 'bey-center-cache-v1';
const urlsToCache = [
  '/',
  '/index.html',
  '/manifest.json',
  '/logo.svg',
  '/logo192.png',
  '/logo512.png',
  '/assets/index.css'
];

console.log('Service Worker: Script loaded!');

// Install event - cache assets
self.addEventListener('install', event => {
  console.log('Service Worker: Installing...', event);
  
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => {
        console.log('Service Worker: Opened cache', CACHE_NAME);
        return cache.addAll(urlsToCache)
          .then(() => {
            console.log('Service Worker: All resources cached');
            return self.skipWaiting(); // Force activation
          })
          .catch(error => {
            console.error('Service Worker: Failed to cache resources', error);
          });
      })
  );
});

// Fetch event - serve from cache if available
self.addEventListener('fetch', event => {
  console.log('Service Worker: Fetch event for ', event.request.url);
  
  event.respondWith(
    caches.match(event.request)
      .then(response => {
        // Cache hit - return response
        if (response) {
          console.log('Service Worker: Serving from cache', event.request.url);
          return response;
        }
        
        console.log('Service Worker: Fetching resource', event.request.url);
        return fetch(event.request).then(
          response => {
            // Check if we received a valid response
            if (!response || response.status !== 200 || response.type !== 'basic') {
              console.log('Service Worker: Not caching the response', response.status);
              return response;
            }

            // Clone the response
            const responseToCache = response.clone();

            caches.open(CACHE_NAME)
              .then(cache => {
                console.log('Service Worker: Caching new resource', event.request.url);
                cache.put(event.request, responseToCache);
              });

            return response;
          }
        ).catch(error => {
          console.error('Service Worker: Fetch failed', error);
          // You could return a custom offline page here
        });
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
  console.log('Service Worker: Activating...', event);
  
  const cacheWhitelist = [CACHE_NAME];
  event.waitUntil(
    caches.keys().then(cacheNames => {
      console.log('Service Worker: Caches found', cacheNames);
      return Promise.all(
        cacheNames.map(cacheName => {
          if (cacheWhitelist.indexOf(cacheName) === -1) {
            console.log('Service Worker: Deleting old cache', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    }).then(() => {
      console.log('Service Worker: Claiming clients');
      return self.clients.claim(); // Take control of all clients
    })
  );
});

// Listen for messages from the client
self.addEventListener('message', (event) => {
  console.log('Service Worker: Message received', event.data);
  
  if (event.data && event.data.type === 'SKIP_WAITING') {
    console.log('Service Worker: Skip waiting message received');
    self.skipWaiting();
  }
}); 