<?php
class Response {
    public static function success($data = null, $message = 'Success', $code = 200) {
        // Clean any unwanted output before sending response
        if (ob_get_level()) ob_clean();
        http_response_code($code);
        echo json_encode([
            'success' => true,
            'message' => $message,
            'data' => $data,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
        exit();
    }

    public static function error($message = 'Error', $code = 400, $errors = null) {
        // Clean any unwanted output before sending response
        if (ob_get_level()) ob_clean();
        http_response_code($code);
        echo json_encode([
            'success' => false,
            'message' => $message,
            'errors' => $errors,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
        exit();
    }

    public static function unauthorized($message = 'Unauthorized') {
        self::error($message, 401);
    }

    public static function forbidden($message = 'Forbidden') {
        self::error($message, 403);
    }

    public static function notFound($message = 'Not found') {
        self::error($message, 404);
    }

    public static function validationError($errors, $message = 'Validation failed') {
        self::error($message, 422, $errors);
    }

    public static function serverError($message = 'Internal server error') {
        self::error($message, 500);
    }

    public static function paginated($data, $total, $page, $limit, $message = 'Success') {
        $totalPages = ceil($total / $limit);
        
        // Clean any unwanted output before sending response
        if (ob_get_level()) ob_clean();
        http_response_code(200);
        echo json_encode([
            'success' => true,
            'message' => $message,
            'data' => $data,
            'pagination' => [
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'total_pages' => $totalPages,
                'has_next' => $page < $totalPages,
                'has_prev' => $page > 1
            ],
            'timestamp' => date('Y-m-d H:i:s')
        ]);
        exit();
    }
}
?> 