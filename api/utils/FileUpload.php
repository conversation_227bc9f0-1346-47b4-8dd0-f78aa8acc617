<?php
class FileUpload {
    private $uploadDir = '../uploads/';
    private $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    private $maxFileSize = 5 * 1024 * 1024; // 5MB
    private $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];

    public function __construct($uploadDir = null) {
        if ($uploadDir) {
            $this->uploadDir = $uploadDir;
        }
        
        // Create upload directory if it doesn't exist
        if (!file_exists($this->uploadDir)) {
            mkdir($this->uploadDir, 0755, true);
        }
    }

    public function uploadImage($file, $subfolder = '', $customName = null) {
        try {
            // Validate file
            $validation = $this->validateFile($file);
            if (!$validation['success']) {
                return $validation;
            }

            // Create subfolder if specified
            $targetDir = $this->uploadDir;
            if ($subfolder) {
                $targetDir .= $subfolder . '/';
                if (!file_exists($targetDir)) {
                    mkdir($targetDir, 0755, true);
                }
            }

            // Generate filename
            $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
            $fileName = $customName ? $customName . '.' . $fileExtension : $this->generateUniqueFileName($fileExtension);
            $targetFile = $targetDir . $fileName;

            // Move uploaded file
            if (move_uploaded_file($file['tmp_name'], $targetFile)) {
                // Optimize image
                $this->optimizeImage($targetFile, $fileExtension);
                
                $relativePath = '/uploads/' . ($subfolder ? $subfolder . '/' : '') . $fileName;
                
                return [
                    'success' => true,
                    'message' => 'File uploaded successfully',
                    'file_path' => $relativePath,
                    'file_name' => $fileName,
                    'file_size' => filesize($targetFile)
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Failed to move uploaded file'
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Upload error: ' . $e->getMessage()
            ];
        }
    }

    public function uploadMultiple($files, $subfolder = '') {
        $results = [];
        
        foreach ($files as $key => $file) {
            if (is_array($file['name'])) {
                // Handle multiple files with same input name
                for ($i = 0; $i < count($file['name']); $i++) {
                    $singleFile = [
                        'name' => $file['name'][$i],
                        'type' => $file['type'][$i],
                        'tmp_name' => $file['tmp_name'][$i],
                        'error' => $file['error'][$i],
                        'size' => $file['size'][$i]
                    ];
                    $results[] = $this->uploadImage($singleFile, $subfolder);
                }
            } else {
                $results[] = $this->uploadImage($file, $subfolder);
            }
        }
        
        return $results;
    }

    public function deleteFile($filePath) {
        $fullPath = $_SERVER['DOCUMENT_ROOT'] . $filePath;
        
        if (file_exists($fullPath)) {
            return unlink($fullPath);
        }
        
        return false;
    }

    private function validateFile($file) {
        // Check for upload errors
        if ($file['error'] !== UPLOAD_ERR_OK) {
            return [
                'success' => false,
                'message' => $this->getUploadErrorMessage($file['error'])
            ];
        }

        // Check file size
        if ($file['size'] > $this->maxFileSize) {
            return [
                'success' => false,
                'message' => 'File size exceeds maximum allowed size of ' . ($this->maxFileSize / 1024 / 1024) . 'MB'
            ];
        }

        // Check file type
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);

        if (!in_array($mimeType, $this->allowedTypes)) {
            return [
                'success' => false,
                'message' => 'Invalid file type. Allowed types: ' . implode(', ', $this->allowedExtensions)
            ];
        }

        // Check file extension
        $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($fileExtension, $this->allowedExtensions)) {
            return [
                'success' => false,
                'message' => 'Invalid file extension. Allowed extensions: ' . implode(', ', $this->allowedExtensions)
            ];
        }

        return ['success' => true];
    }

    private function generateUniqueFileName($extension) {
        return uniqid('upload_', true) . '.' . $extension;
    }

    private function optimizeImage($filePath, $extension) {
        // Basic image optimization
        switch ($extension) {
            case 'jpg':
            case 'jpeg':
                $image = imagecreatefromjpeg($filePath);
                if ($image) {
                    imagejpeg($image, $filePath, 85); // 85% quality
                    imagedestroy($image);
                }
                break;
            case 'png':
                $image = imagecreatefrompng($filePath);
                if ($image) {
                    imagepng($image, $filePath, 6); // Compression level 6
                    imagedestroy($image);
                }
                break;
        }
    }

    private function getUploadErrorMessage($errorCode) {
        switch ($errorCode) {
            case UPLOAD_ERR_INI_SIZE:
                return 'File exceeds upload_max_filesize directive in php.ini';
            case UPLOAD_ERR_FORM_SIZE:
                return 'File exceeds MAX_FILE_SIZE directive in HTML form';
            case UPLOAD_ERR_PARTIAL:
                return 'File was only partially uploaded';
            case UPLOAD_ERR_NO_FILE:
                return 'No file was uploaded';
            case UPLOAD_ERR_NO_TMP_DIR:
                return 'Missing temporary folder';
            case UPLOAD_ERR_CANT_WRITE:
                return 'Failed to write file to disk';
            case UPLOAD_ERR_EXTENSION:
                return 'File upload stopped by extension';
            default:
                return 'Unknown upload error';
        }
    }

    // Resize image to specific dimensions
    public function resizeImage($sourcePath, $targetPath, $maxWidth, $maxHeight) {
        $imageInfo = getimagesize($sourcePath);
        if (!$imageInfo) {
            return false;
        }

        $sourceWidth = $imageInfo[0];
        $sourceHeight = $imageInfo[1];
        $mimeType = $imageInfo['mime'];

        // Calculate new dimensions
        $ratio = min($maxWidth / $sourceWidth, $maxHeight / $sourceHeight);
        $newWidth = intval($sourceWidth * $ratio);
        $newHeight = intval($sourceHeight * $ratio);

        // Create source image
        switch ($mimeType) {
            case 'image/jpeg':
                $sourceImage = imagecreatefromjpeg($sourcePath);
                break;
            case 'image/png':
                $sourceImage = imagecreatefrompng($sourcePath);
                break;
            case 'image/gif':
                $sourceImage = imagecreatefromgif($sourcePath);
                break;
            default:
                return false;
        }

        // Create target image
        $targetImage = imagecreatetruecolor($newWidth, $newHeight);
        
        // Preserve transparency for PNG and GIF
        if ($mimeType === 'image/png' || $mimeType === 'image/gif') {
            imagealphablending($targetImage, false);
            imagesavealpha($targetImage, true);
        }

        // Resize
        imagecopyresampled($targetImage, $sourceImage, 0, 0, 0, 0, $newWidth, $newHeight, $sourceWidth, $sourceHeight);

        // Save resized image
        switch ($mimeType) {
            case 'image/jpeg':
                $result = imagejpeg($targetImage, $targetPath, 85);
                break;
            case 'image/png':
                $result = imagepng($targetImage, $targetPath, 6);
                break;
            case 'image/gif':
                $result = imagegif($targetImage, $targetPath);
                break;
        }

        // Clean up
        imagedestroy($sourceImage);
        imagedestroy($targetImage);

        return $result;
    }
}
?> 