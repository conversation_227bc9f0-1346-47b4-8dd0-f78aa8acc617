<?php
class Validator {
    private $errors = [];
    private $data = [];

    public function __construct($data = []) {
        $this->data = $data;
    }

    public function required($field, $message = null) {
        if (!isset($this->data[$field]) || empty(trim($this->data[$field]))) {
            $this->errors[$field] = $message ?: ucfirst($field) . ' is required';
        }
        return $this;
    }

    public function email($field, $message = null) {
        if (isset($this->data[$field]) && !filter_var($this->data[$field], FILTER_VALIDATE_EMAIL)) {
            $this->errors[$field] = $message ?: ucfirst($field) . ' must be a valid email address';
        }
        return $this;
    }

    public function minLength($field, $length, $message = null) {
        if (isset($this->data[$field]) && strlen($this->data[$field]) < $length) {
            $this->errors[$field] = $message ?: ucfirst($field) . " must be at least {$length} characters";
        }
        return $this;
    }

    public function maxLength($field, $length, $message = null) {
        if (isset($this->data[$field]) && strlen($this->data[$field]) > $length) {
            $this->errors[$field] = $message ?: ucfirst($field) . " must not exceed {$length} characters";
        }
        return $this;
    }

    public function numeric($field, $message = null) {
        if (isset($this->data[$field]) && !is_numeric($this->data[$field])) {
            $this->errors[$field] = $message ?: ucfirst($field) . ' must be a number';
        }
        return $this;
    }

    public function date($field, $format = 'Y-m-d', $message = null) {
        if (isset($this->data[$field])) {
            $d = DateTime::createFromFormat($format, $this->data[$field]);
            if (!$d || $d->format($format) !== $this->data[$field]) {
                $this->errors[$field] = $message ?: ucfirst($field) . " must be a valid date in format {$format}";
            }
        }
        return $this;
    }

    public function in($field, $values, $message = null) {
        if (isset($this->data[$field]) && !in_array($this->data[$field], $values)) {
            $this->errors[$field] = $message ?: ucfirst($field) . ' must be one of: ' . implode(', ', $values);
        }
        return $this;
    }

    public function unique($field, $table, $column, $db, $excludeId = null, $message = null) {
        if (isset($this->data[$field])) {
            $query = "SELECT COUNT(*) as count FROM {$table} WHERE {$column} = :value";
            $params = [':value' => $this->data[$field]];
            
            if ($excludeId) {
                $query .= " AND id != :exclude_id";
                $params[':exclude_id'] = $excludeId;
            }
            
            $stmt = $db->prepare($query);
            $stmt->execute($params);
            $result = $stmt->fetch();
            
            if ($result['count'] > 0) {
                $this->errors[$field] = $message ?: ucfirst($field) . ' already exists';
            }
        }
        return $this;
    }

    public function phone($field, $message = null) {
        if (isset($this->data[$field])) {
            $phone = preg_replace('/[^0-9+]/', '', $this->data[$field]);
            if (strlen($phone) < 10 || strlen($phone) > 15) {
                $this->errors[$field] = $message ?: ucfirst($field) . ' must be a valid phone number';
            }
        }
        return $this;
    }

    public function custom($field, $callback, $message = null) {
        if (isset($this->data[$field])) {
            if (!$callback($this->data[$field])) {
                $this->errors[$field] = $message ?: ucfirst($field) . ' is invalid';
            }
        }
        return $this;
    }

    public function fails() {
        return !empty($this->errors);
    }

    public function passes() {
        return empty($this->errors);
    }

    public function getErrors() {
        return $this->errors;
    }

    public function getFirstError() {
        return !empty($this->errors) ? reset($this->errors) : null;
    }

    public static function sanitizeInput($data) {
        if (is_array($data)) {
            return array_map([self::class, 'sanitizeInput'], $data);
        }
        return htmlspecialchars(strip_tags(trim($data)), ENT_QUOTES, 'UTF-8');
    }

    public static function validateStudentData($data, $db, $excludeId = null) {
        $validator = new self($data);
        
        $validator->required('name')
                 ->required('student_id')
                 ->required('date_of_birth')
                 ->required('gender')
                 ->required('course_id')
                 ->required('level_id')
                 ->email('email')
                 ->date('date_of_birth')
                 ->date('date_of_registration')
                 ->in('gender', ['Male', 'Female', 'Other'])
                 ->in('enrollment_status', ['Active', 'Inactive', 'Suspended', 'Graduated'])
                 ->phone('mobile_number')
                 ->phone('whatsapp_number')
                 ->phone('parent_mobile')
                 ->phone('parent_whatsapp')
                 ->unique('student_id', 'students', 'student_id', $db, $excludeId);

        return $validator;
    }
}
?> 