<?php
require_once __DIR__ . '/../middleware/AuthMiddleware.php';

class Course {
    private $conn;
    private $table = 'courses';

    public function __construct($db) {
        $this->conn = $db;
    }

    public function create($data) {
        $query = "INSERT INTO " . $this->table . " 
                  (id, name, code, description, duration, fee, status, created_at, updated_at) 
                  VALUES (:id, :name, :code, :description, :duration, :fee, :status, NOW(), NOW())";

        $stmt = $this->conn->prepare($query);

        // Generate UUID
        $data['id'] = AuthMiddleware::generateUUID();
        $data['status'] = $data['status'] ?? 'Active';

        $stmt->bindParam(':id', $data['id']);
        $stmt->bindParam(':name', $data['name']);
        $stmt->bindParam(':code', $data['code']);
        $stmt->bindParam(':description', $data['description']);
        $stmt->bindParam(':duration', $data['duration']);
        $stmt->bindParam(':fee', $data['fee']);
        $stmt->bindParam(':status', $data['status']);

        if ($stmt->execute()) {
            return $this->getById($data['id']);
        }

        return false;
    }

    public function getAll($limit = 50, $offset = 0) {
        $query = "SELECT c.*, 
                         (SELECT COUNT(*) FROM students s WHERE s.course_id = c.id) as student_count,
                         (SELECT COUNT(*) FROM levels l WHERE l.course_id = c.id) as level_count
                  FROM " . $this->table . " c
                  ORDER BY c.name ASC
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    public function getById($id) {
        $query = "SELECT c.*, 
                         (SELECT COUNT(*) FROM students s WHERE s.course_id = c.id) as student_count,
                         (SELECT COUNT(*) FROM levels l WHERE l.course_id = c.id) as level_count
                  FROM " . $this->table . " c
                  WHERE c.id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        return $stmt->fetch();
    }

    public function getByCode($code) {
        $query = "SELECT * FROM " . $this->table . " WHERE code = :code";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':code', $code);
        $stmt->execute();

        return $stmt->fetch();
    }

    public function getActive() {
        $query = "SELECT c.*, 
                         (SELECT COUNT(*) FROM students s WHERE s.course_id = c.id) as student_count,
                         (SELECT COUNT(*) FROM levels l WHERE l.course_id = c.id) as level_count
                  FROM " . $this->table . " c
                  WHERE c.status = 'Active'
                  ORDER BY c.name ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    public function update($id, $data) {
        $fields = [];
        $params = [':id' => $id];

        $allowedFields = ['name', 'code', 'description', 'duration', 'fee', 'status'];

        foreach ($data as $key => $value) {
            if (in_array($key, $allowedFields)) {
                $fields[] = "$key = :$key";
                $params[":$key"] = $value;
            }
        }

        if (empty($fields)) {
            return false;
        }

        $query = "UPDATE " . $this->table . " 
                  SET " . implode(', ', $fields) . ", updated_at = NOW() 
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);
        return $stmt->execute($params);
    }

    public function delete($id) {
        // Check if course has students
        $studentCount = $this->getStudentCount($id);
        if ($studentCount > 0) {
            return ['success' => false, 'message' => 'Cannot delete course with enrolled students'];
        }

        $query = "DELETE FROM " . $this->table . " WHERE id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);

        if ($stmt->execute()) {
            return ['success' => true, 'message' => 'Course deleted successfully'];
        }

        return ['success' => false, 'message' => 'Failed to delete course'];
    }

    public function search($searchTerm, $limit = 50, $offset = 0) {
        $query = "SELECT c.*, 
                         (SELECT COUNT(*) FROM students s WHERE s.course_id = c.id) as student_count,
                         (SELECT COUNT(*) FROM levels l WHERE l.course_id = c.id) as level_count
                  FROM " . $this->table . " c
                  WHERE c.name LIKE :search 
                     OR c.code LIKE :search 
                     OR c.description LIKE :search
                  ORDER BY c.name ASC
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        $searchParam = "%$searchTerm%";
        $stmt->bindParam(':search', $searchParam);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    public function getTotalCount() {
        $query = "SELECT COUNT(*) as total FROM " . $this->table;
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch();
        return $result['total'];
    }

    public function getStudentCount($courseId) {
        $query = "SELECT COUNT(*) as total FROM students WHERE course_id = :course_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':course_id', $courseId);
        $stmt->execute();
        $result = $stmt->fetch();
        return $result['total'];
    }

    public function codeExists($code, $excludeId = null) {
        $query = "SELECT COUNT(*) as count FROM " . $this->table . " WHERE code = :code";
        $params = [':code' => $code];

        if ($excludeId) {
            $query .= " AND id != :exclude_id";
            $params[':exclude_id'] = $excludeId;
        }

        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        $result = $stmt->fetch();

        return $result['count'] > 0;
    }

    public function getLevels($courseId) {
        $query = "SELECT * FROM levels WHERE course_id = :course_id ORDER BY name ASC";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':course_id', $courseId);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    public function getWithLevels($courseId) {
        $course = $this->getById($courseId);
        if ($course) {
            $course['levels'] = $this->getLevels($courseId);
        }
        return $course;
    }

    public function getPopularCourses($limit = 5) {
        $query = "SELECT c.*, COUNT(s.id) as student_count
                  FROM " . $this->table . " c
                  LEFT JOIN students s ON c.id = s.course_id
                  WHERE c.status = 'Active'
                  GROUP BY c.id
                  ORDER BY student_count DESC, c.name ASC
                  LIMIT :limit";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    public function getRevenueStats($courseId = null) {
        $query = "SELECT 
                    c.id,
                    c.name,
                    c.code,
                    COUNT(DISTINCT s.id) as total_students,
                    SUM(CASE WHEN s.payment_status = 'Paid' THEN c.fee ELSE 0 END) as total_revenue,
                    SUM(CASE WHEN s.payment_status = 'Pending' THEN c.fee ELSE 0 END) as pending_revenue
                  FROM " . $this->table . " c
                  LEFT JOIN students s ON c.id = s.course_id";

        if ($courseId) {
            $query .= " WHERE c.id = :course_id";
        }

        $query .= " GROUP BY c.id, c.name, c.code ORDER BY total_revenue DESC";

        $stmt = $this->conn->prepare($query);
        
        if ($courseId) {
            $stmt->bindParam(':course_id', $courseId);
        }
        
        $stmt->execute();

        return $courseId ? $stmt->fetch() : $stmt->fetchAll();
    }
}

// Level model for course levels
class Level {
    private $conn;
    private $table = 'levels';

    public function __construct($db) {
        $this->conn = $db;
    }

    public function create($data) {
        $query = "INSERT INTO " . $this->table . " 
                  (id, name, code, description, course_id, order_index, created_at, updated_at) 
                  VALUES (:id, :name, :code, :description, :course_id, :order_index, NOW(), NOW())";

        $stmt = $this->conn->prepare($query);

        $data['id'] = AuthMiddleware::generateUUID();
        $data['order_index'] = $data['order_index'] ?? 1;

        $stmt->bindParam(':id', $data['id']);
        $stmt->bindParam(':name', $data['name']);
        $stmt->bindParam(':code', $data['code']);
        $stmt->bindParam(':description', $data['description']);
        $stmt->bindParam(':course_id', $data['course_id']);
        $stmt->bindParam(':order_index', $data['order_index']);

        if ($stmt->execute()) {
            return $this->getById($data['id']);
        }

        return false;
    }

    public function getAll($limit = 50, $offset = 0) {
        $query = "SELECT l.*, c.name as course_name, c.code as course_code,
                         (SELECT COUNT(*) FROM students s WHERE s.level_id = l.id) as student_count
                  FROM " . $this->table . " l
                  LEFT JOIN courses c ON l.course_id = c.id
                  ORDER BY c.name ASC, l.order_index ASC
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    public function getById($id) {
        $query = "SELECT l.*, c.name as course_name, c.code as course_code
                  FROM " . $this->table . " l
                  LEFT JOIN courses c ON l.course_id = c.id
                  WHERE l.id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        return $stmt->fetch();
    }

    public function getByCourse($courseId) {
        $query = "SELECT * FROM " . $this->table . " 
                  WHERE course_id = :course_id 
                  ORDER BY order_index ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':course_id', $courseId);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    public function update($id, $data) {
        $fields = [];
        $params = [':id' => $id];

        $allowedFields = ['name', 'code', 'description', 'order_index'];

        foreach ($data as $key => $value) {
            if (in_array($key, $allowedFields)) {
                $fields[] = "$key = :$key";
                $params[":$key"] = $value;
            }
        }

        if (empty($fields)) {
            return false;
        }

        $query = "UPDATE " . $this->table . " 
                  SET " . implode(', ', $fields) . ", updated_at = NOW() 
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);
        return $stmt->execute($params);
    }

    public function delete($id) {
        // Check if level has students
        $studentCount = $this->getStudentCount($id);
        if ($studentCount > 0) {
            return ['success' => false, 'message' => 'Cannot delete level with enrolled students'];
        }

        $query = "DELETE FROM " . $this->table . " WHERE id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);

        if ($stmt->execute()) {
            return ['success' => true, 'message' => 'Level deleted successfully'];
        }

        return ['success' => false, 'message' => 'Failed to delete level'];
    }

    public function getStudentCount($levelId) {
        $query = "SELECT COUNT(*) as total FROM students WHERE level_id = :level_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':level_id', $levelId);
        $stmt->execute();
        $result = $stmt->fetch();
        return $result['total'];
    }

    public function getTotalCount() {
        $query = "SELECT COUNT(*) as total FROM " . $this->table;
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch();
        return $result['total'];
    }
}
?> 