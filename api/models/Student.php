<?php
require_once __DIR__ . '/../middleware/AuthMiddleware.php';

class Student {
    private $conn;
    private $table = 'students';

    public function __construct($db) {
        $this->conn = $db;
    }

    public function create($data) {
        $query = "INSERT INTO " . $this->table . " 
                  (id, student_id, name, email, date_of_birth, date_of_registration, 
                   nationality, gender, address, mobile_number, whatsapp_number, 
                   parent_name, parent_mobile, parent_whatsapp, parent_email, 
                   parent_occupation, passport_picture, course_id, level_id, 
                   enrollment_status, payment_status, created_at, updated_at) 
                  VALUES (:id, :student_id, :name, :email, :date_of_birth, :date_of_registration,
                          :nationality, :gender, :address, :mobile_number, :whatsapp_number,
                          :parent_name, :parent_mobile, :parent_whatsapp, :parent_email,
                          :parent_occupation, :passport_picture, :course_id, :level_id,
                          :enrollment_status, :payment_status, NOW(), NOW())";

        $stmt = $this->conn->prepare($query);

        // Generate UUID
        $data['id'] = AuthMiddleware::generateUUID();
        
        // Set defaults
        $data['date_of_registration'] = $data['date_of_registration'] ?? date('Y-m-d');
        $data['enrollment_status'] = $data['enrollment_status'] ?? 'Active';
        $data['payment_status'] = $data['payment_status'] ?? 'Pending';

        $stmt->bindParam(':id', $data['id']);
        $stmt->bindParam(':student_id', $data['student_id']);
        $stmt->bindParam(':name', $data['name']);
        $stmt->bindParam(':email', $data['email']);
        $stmt->bindParam(':date_of_birth', $data['date_of_birth']);
        $stmt->bindParam(':date_of_registration', $data['date_of_registration']);
        $stmt->bindParam(':nationality', $data['nationality']);
        $stmt->bindParam(':gender', $data['gender']);
        $stmt->bindParam(':address', $data['address']);
        $stmt->bindParam(':mobile_number', $data['mobile_number']);
        $stmt->bindParam(':whatsapp_number', $data['whatsapp_number']);
        $stmt->bindParam(':parent_name', $data['parent_name']);
        $stmt->bindParam(':parent_mobile', $data['parent_mobile']);
        $stmt->bindParam(':parent_whatsapp', $data['parent_whatsapp']);
        $stmt->bindParam(':parent_email', $data['parent_email']);
        $stmt->bindParam(':parent_occupation', $data['parent_occupation']);
        $stmt->bindParam(':passport_picture', $data['passport_picture']);
        $stmt->bindParam(':course_id', $data['course_id']);
        $stmt->bindParam(':level_id', $data['level_id']);
        $stmt->bindParam(':enrollment_status', $data['enrollment_status']);
        $stmt->bindParam(':payment_status', $data['payment_status']);

        if ($stmt->execute()) {
            return $this->getById($data['id']);
        }

        return false;
    }

    public function getAll($limit = 50, $offset = 0) {
        $query = "SELECT s.*, c.name as course_name, c.code as course_code,
                         l.name as level_name, l.code as level_code
                  FROM " . $this->table . " s
                  LEFT JOIN courses c ON s.course_id = c.id
                  LEFT JOIN levels l ON s.level_id = l.id
                  ORDER BY s.created_at DESC
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    public function getById($id) {
        $query = "SELECT s.*, c.name as course_name, c.code as course_code,
                         l.name as level_name, l.code as level_code
                  FROM " . $this->table . " s
                  LEFT JOIN courses c ON s.course_id = c.id
                  LEFT JOIN levels l ON s.level_id = l.id
                  WHERE s.id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        return $stmt->fetch();
    }

    public function getByStudentId($studentId) {
        $query = "SELECT s.*, c.name as course_name, c.code as course_code,
                         l.name as level_name, l.code as level_code
                  FROM " . $this->table . " s
                  LEFT JOIN courses c ON s.course_id = c.id
                  LEFT JOIN levels l ON s.level_id = l.id
                  WHERE s.student_id = :student_id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':student_id', $studentId);
        $stmt->execute();

        return $stmt->fetch();
    }

    public function getByEmail($email) {
        $query = "SELECT s.*, c.name as course_name, c.code as course_code,
                         l.name as level_name, l.code as level_code
                  FROM " . $this->table . " s
                  LEFT JOIN courses c ON s.course_id = c.id
                  LEFT JOIN levels l ON s.level_id = l.id
                  WHERE s.email = :email";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':email', $email);
        $stmt->execute();

        return $stmt->fetch();
    }

    public function getByCourse($courseId, $limit = 50, $offset = 0) {
        $query = "SELECT s.*, c.name as course_name, c.code as course_code,
                         l.name as level_name, l.code as level_code
                  FROM " . $this->table . " s
                  LEFT JOIN courses c ON s.course_id = c.id
                  LEFT JOIN levels l ON s.level_id = l.id
                  WHERE s.course_id = :course_id
                  ORDER BY s.name ASC
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':course_id', $courseId);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    public function getByLevel($levelId, $limit = 50, $offset = 0) {
        $query = "SELECT s.*, c.name as course_name, c.code as course_code,
                         l.name as level_name, l.code as level_code
                  FROM " . $this->table . " s
                  LEFT JOIN courses c ON s.course_id = c.id
                  LEFT JOIN levels l ON s.level_id = l.id
                  WHERE s.level_id = :level_id
                  ORDER BY s.name ASC
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':level_id', $levelId);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    public function update($id, $data) {
        $fields = [];
        $params = [':id' => $id];

        $allowedFields = [
            'name', 'email', 'date_of_birth', 'nationality', 'gender', 'address',
            'mobile_number', 'whatsapp_number', 'parent_name', 'parent_mobile',
            'parent_whatsapp', 'parent_email', 'parent_occupation', 'passport_picture',
            'course_id', 'level_id', 'enrollment_status', 'payment_status'
        ];

        foreach ($data as $key => $value) {
            if (in_array($key, $allowedFields)) {
                $fields[] = "$key = :$key";
                $params[":$key"] = $value;
            }
        }

        if (empty($fields)) {
            return false;
        }

        $query = "UPDATE " . $this->table . " 
                  SET " . implode(', ', $fields) . ", updated_at = NOW() 
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);
        return $stmt->execute($params);
    }

    public function delete($id) {
        $query = "DELETE FROM " . $this->table . " WHERE id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);

        return $stmt->execute();
    }

    public function search($searchTerm, $limit = 50, $offset = 0) {
        $query = "SELECT s.*, c.name as course_name, c.code as course_code,
                         l.name as level_name, l.code as level_code
                  FROM " . $this->table . " s
                  LEFT JOIN courses c ON s.course_id = c.id
                  LEFT JOIN levels l ON s.level_id = l.id
                  WHERE s.name LIKE :search 
                     OR s.student_id LIKE :search 
                     OR s.email LIKE :search
                     OR c.name LIKE :search
                     OR l.name LIKE :search
                  ORDER BY s.name ASC
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        $searchParam = "%$searchTerm%";
        $stmt->bindParam(':search', $searchParam);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    public function getTotalCount() {
        $query = "SELECT COUNT(*) as total FROM " . $this->table;
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch();
        return $result['total'];
    }

    public function getCountByStatus($status) {
        $query = "SELECT COUNT(*) as total FROM " . $this->table . " WHERE enrollment_status = :status";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':status', $status);
        $stmt->execute();
        $result = $stmt->fetch();
        return $result['total'];
    }

    public function getCountByCourse($courseId) {
        $query = "SELECT COUNT(*) as total FROM " . $this->table . " WHERE course_id = :course_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':course_id', $courseId);
        $stmt->execute();
        $result = $stmt->fetch();
        return $result['total'];
    }

    public function getCountByLevel($levelId) {
        $query = "SELECT COUNT(*) as total FROM " . $this->table . " WHERE level_id = :level_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':level_id', $levelId);
        $stmt->execute();
        $result = $stmt->fetch();
        return $result['total'];
    }

    public function studentIdExists($studentId, $excludeId = null) {
        $query = "SELECT COUNT(*) as count FROM " . $this->table . " WHERE student_id = :student_id";
        $params = [':student_id' => $studentId];

        if ($excludeId) {
            $query .= " AND id != :exclude_id";
            $params[':exclude_id'] = $excludeId;
        }

        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        $result = $stmt->fetch();

        return $result['count'] > 0;
    }

    public function emailExists($email, $excludeId = null) {
        $query = "SELECT COUNT(*) as count FROM " . $this->table . " WHERE email = :email";
        $params = [':email' => $email];

        if ($excludeId) {
            $query .= " AND id != :exclude_id";
            $params[':exclude_id'] = $excludeId;
        }

        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        $result = $stmt->fetch();

        return $result['count'] > 0;
    }

    public function getByEnrollmentStatus($status, $limit = 50, $offset = 0) {
        $query = "SELECT s.*, c.name as course_name, c.code as course_code,
                         l.name as level_name, l.code as level_code
                  FROM " . $this->table . " s
                  LEFT JOIN courses c ON s.course_id = c.id
                  LEFT JOIN levels l ON s.level_id = l.id
                  WHERE s.enrollment_status = :status
                  ORDER BY s.name ASC
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':status', $status);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    public function getByPaymentStatus($status, $limit = 50, $offset = 0) {
        $query = "SELECT s.*, c.name as course_name, c.code as course_code,
                         l.name as level_name, l.code as level_code
                  FROM " . $this->table . " s
                  LEFT JOIN courses c ON s.course_id = c.id
                  LEFT JOIN levels l ON s.level_id = l.id
                  WHERE s.payment_status = :status
                  ORDER BY s.name ASC
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':status', $status);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    public function getRecentlyRegistered($days = 30, $limit = 10) {
        $query = "SELECT s.*, c.name as course_name, l.name as level_name
                  FROM " . $this->table . " s
                  LEFT JOIN courses c ON s.course_id = c.id
                  LEFT JOIN levels l ON s.level_id = l.id
                  WHERE s.date_of_registration >= DATE_SUB(NOW(), INTERVAL :days DAY)
                  ORDER BY s.date_of_registration DESC
                  LIMIT :limit";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':days', $days, PDO::PARAM_INT);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll();
    }
}
?> 