<?php
require_once __DIR__ . '/../middleware/AuthMiddleware.php';

class Payment {
    private $conn;
    private $table = 'payments';

    public function __construct($db) {
        $this->conn = $db;
    }

    public function create($data) {
        $query = "INSERT INTO " . $this->table . " 
                  (id, student_id, amount, payment_method, payment_date, reference_number, 
                   status, notes, processed_by, created_at, updated_at) 
                  VALUES (:id, :student_id, :amount, :payment_method, :payment_date, :reference_number,
                          :status, :notes, :processed_by, NOW(), NOW())";

        $stmt = $this->conn->prepare($query);

        // Generate UUID and reference number
        $data['id'] = AuthMiddleware::generateUUID();
        $data['reference_number'] = $data['reference_number'] ?? $this->generateReferenceNumber();
        $data['payment_date'] = $data['payment_date'] ?? date('Y-m-d H:i:s');
        $data['status'] = $data['status'] ?? 'Completed';

        $stmt->bindParam(':id', $data['id']);
        $stmt->bindParam(':student_id', $data['student_id']);
        $stmt->bindParam(':amount', $data['amount']);
        $stmt->bindParam(':payment_method', $data['payment_method']);
        $stmt->bindParam(':payment_date', $data['payment_date']);
        $stmt->bindParam(':reference_number', $data['reference_number']);
        $stmt->bindParam(':status', $data['status']);
        $stmt->bindParam(':notes', $data['notes']);
        $stmt->bindParam(':processed_by', $data['processed_by']);

        if ($stmt->execute()) {
            return $this->getById($data['id']);
        }

        return false;
    }

    public function getAll($limit = 50, $offset = 0) {
        $query = "SELECT p.*, s.name as student_name, s.student_id as student_number,
                         c.name as course_name, u.name as processed_by_name
                  FROM " . $this->table . " p
                  LEFT JOIN students s ON p.student_id = s.id
                  LEFT JOIN courses c ON s.course_id = c.id
                  LEFT JOIN users u ON p.processed_by = u.id
                  ORDER BY p.payment_date DESC
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    public function getById($id) {
        $query = "SELECT p.*, s.name as student_name, s.student_id as student_number,
                         c.name as course_name, u.name as processed_by_name
                  FROM " . $this->table . " p
                  LEFT JOIN students s ON p.student_id = s.id
                  LEFT JOIN courses c ON s.course_id = c.id
                  LEFT JOIN users u ON p.processed_by = u.id
                  WHERE p.id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        return $stmt->fetch();
    }

    public function getByStudent($studentId, $limit = 50, $offset = 0) {
        $query = "SELECT p.*, u.name as processed_by_name
                  FROM " . $this->table . " p
                  LEFT JOIN users u ON p.processed_by = u.id
                  WHERE p.student_id = :student_id
                  ORDER BY p.payment_date DESC
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':student_id', $studentId);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    public function getByReferenceNumber($referenceNumber) {
        $query = "SELECT p.*, s.name as student_name, s.student_id as student_number
                  FROM " . $this->table . " p
                  LEFT JOIN students s ON p.student_id = s.id
                  WHERE p.reference_number = :reference_number";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':reference_number', $referenceNumber);
        $stmt->execute();

        return $stmt->fetch();
    }

    public function update($id, $data) {
        $fields = [];
        $params = [':id' => $id];

        $allowedFields = ['amount', 'payment_method', 'payment_date', 'status', 'notes'];

        foreach ($data as $key => $value) {
            if (in_array($key, $allowedFields)) {
                $fields[] = "$key = :$key";
                $params[":$key"] = $value;
            }
        }

        if (empty($fields)) {
            return false;
        }

        $query = "UPDATE " . $this->table . " 
                  SET " . implode(', ', $fields) . ", updated_at = NOW() 
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);
        return $stmt->execute($params);
    }

    public function delete($id) {
        $query = "DELETE FROM " . $this->table . " WHERE id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);

        return $stmt->execute();
    }

    public function search($searchTerm, $limit = 50, $offset = 0) {
        $query = "SELECT p.*, s.name as student_name, s.student_id as student_number,
                         c.name as course_name
                  FROM " . $this->table . " p
                  LEFT JOIN students s ON p.student_id = s.id
                  LEFT JOIN courses c ON s.course_id = c.id
                  WHERE s.name LIKE :search 
                     OR s.student_id LIKE :search 
                     OR p.reference_number LIKE :search
                     OR c.name LIKE :search
                  ORDER BY p.payment_date DESC
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        $searchParam = "%$searchTerm%";
        $stmt->bindParam(':search', $searchParam);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    public function getTotalCount() {
        $query = "SELECT COUNT(*) as total FROM " . $this->table;
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch();
        return $result['total'];
    }

    public function getTotalAmount($startDate = null, $endDate = null) {
        $query = "SELECT SUM(amount) as total FROM " . $this->table . " WHERE status = 'Completed'";
        $params = [];

        if ($startDate && $endDate) {
            $query .= " AND payment_date BETWEEN :start_date AND :end_date";
            $params[':start_date'] = $startDate;
            $params[':end_date'] = $endDate;
        }

        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        $result = $stmt->fetch();
        return $result['total'] ?? 0;
    }

    public function getByDateRange($startDate, $endDate, $limit = 50, $offset = 0) {
        $query = "SELECT p.*, s.name as student_name, s.student_id as student_number,
                         c.name as course_name
                  FROM " . $this->table . " p
                  LEFT JOIN students s ON p.student_id = s.id
                  LEFT JOIN courses c ON s.course_id = c.id
                  WHERE p.payment_date BETWEEN :start_date AND :end_date
                  ORDER BY p.payment_date DESC
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':start_date', $startDate);
        $stmt->bindParam(':end_date', $endDate);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    public function getByStatus($status, $limit = 50, $offset = 0) {
        $query = "SELECT p.*, s.name as student_name, s.student_id as student_number,
                         c.name as course_name
                  FROM " . $this->table . " p
                  LEFT JOIN students s ON p.student_id = s.id
                  LEFT JOIN courses c ON s.course_id = c.id
                  WHERE p.status = :status
                  ORDER BY p.payment_date DESC
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':status', $status);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    public function getStudentPaymentSummary($studentId) {
        $query = "SELECT 
                    COUNT(*) as total_payments,
                    SUM(CASE WHEN status = 'Completed' THEN amount ELSE 0 END) as total_paid,
                    SUM(CASE WHEN status = 'Pending' THEN amount ELSE 0 END) as total_pending,
                    MAX(payment_date) as last_payment_date
                  FROM " . $this->table . " 
                  WHERE student_id = :student_id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':student_id', $studentId);
        $stmt->execute();

        return $stmt->fetch();
    }

    public function getDailyRevenue($days = 30) {
        $query = "SELECT 
                    DATE(payment_date) as payment_date,
                    COUNT(*) as payment_count,
                    SUM(amount) as daily_revenue
                  FROM " . $this->table . " 
                  WHERE status = 'Completed' 
                    AND payment_date >= DATE_SUB(NOW(), INTERVAL :days DAY)
                  GROUP BY DATE(payment_date)
                  ORDER BY payment_date DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':days', $days, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    public function getMonthlyRevenue($year = null) {
        $year = $year ?? date('Y');
        
        $query = "SELECT 
                    MONTH(payment_date) as month,
                    MONTHNAME(payment_date) as month_name,
                    COUNT(*) as payment_count,
                    SUM(amount) as monthly_revenue
                  FROM " . $this->table . " 
                  WHERE status = 'Completed' 
                    AND YEAR(payment_date) = :year
                  GROUP BY MONTH(payment_date), MONTHNAME(payment_date)
                  ORDER BY MONTH(payment_date)";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':year', $year, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    public function getPaymentMethodStats($startDate = null, $endDate = null) {
        $query = "SELECT 
                    payment_method,
                    COUNT(*) as payment_count,
                    SUM(amount) as total_amount
                  FROM " . $this->table . " 
                  WHERE status = 'Completed'";
        
        $params = [];
        
        if ($startDate && $endDate) {
            $query .= " AND payment_date BETWEEN :start_date AND :end_date";
            $params[':start_date'] = $startDate;
            $params[':end_date'] = $endDate;
        }
        
        $query .= " GROUP BY payment_method ORDER BY total_amount DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);

        return $stmt->fetchAll();
    }

    public function getRecentPayments($limit = 10) {
        $query = "SELECT p.*, s.name as student_name, s.student_id as student_number
                  FROM " . $this->table . " p
                  LEFT JOIN students s ON p.student_id = s.id
                  ORDER BY p.payment_date DESC
                  LIMIT :limit";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    private function generateReferenceNumber() {
        $prefix = 'PAY';
        $timestamp = date('YmdHis');
        $random = str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
        return $prefix . $timestamp . $random;
    }

    public function referenceNumberExists($referenceNumber, $excludeId = null) {
        $query = "SELECT COUNT(*) as count FROM " . $this->table . " WHERE reference_number = :reference_number";
        $params = [':reference_number' => $referenceNumber];

        if ($excludeId) {
            $query .= " AND id != :exclude_id";
            $params[':exclude_id'] = $excludeId;
        }

        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        $result = $stmt->fetch();

        return $result['count'] > 0;
    }
}

// Transaction model for detailed financial tracking
class Transaction {
    private $conn;
    private $table = 'transactions';

    public function __construct($db) {
        $this->conn = $db;
    }

    public function create($data) {
        $query = "INSERT INTO " . $this->table . " 
                  (id, payment_id, student_id, type, amount, description, 
                   transaction_date, created_by, created_at, updated_at) 
                  VALUES (:id, :payment_id, :student_id, :type, :amount, :description,
                          :transaction_date, :created_by, NOW(), NOW())";

        $stmt = $this->conn->prepare($query);

        $data['id'] = AuthMiddleware::generateUUID();
        $data['transaction_date'] = $data['transaction_date'] ?? date('Y-m-d H:i:s');

        $stmt->bindParam(':id', $data['id']);
        $stmt->bindParam(':payment_id', $data['payment_id']);
        $stmt->bindParam(':student_id', $data['student_id']);
        $stmt->bindParam(':type', $data['type']);
        $stmt->bindParam(':amount', $data['amount']);
        $stmt->bindParam(':description', $data['description']);
        $stmt->bindParam(':transaction_date', $data['transaction_date']);
        $stmt->bindParam(':created_by', $data['created_by']);

        if ($stmt->execute()) {
            return $this->getById($data['id']);
        }

        return false;
    }

    public function getAll($limit = 50, $offset = 0) {
        $query = "SELECT t.*, s.name as student_name, s.student_id as student_number,
                         p.reference_number as payment_reference, u.name as created_by_name
                  FROM " . $this->table . " t
                  LEFT JOIN students s ON t.student_id = s.id
                  LEFT JOIN payments p ON t.payment_id = p.id
                  LEFT JOIN users u ON t.created_by = u.id
                  ORDER BY t.transaction_date DESC
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    public function getById($id) {
        $query = "SELECT t.*, s.name as student_name, s.student_id as student_number,
                         p.reference_number as payment_reference, u.name as created_by_name
                  FROM " . $this->table . " t
                  LEFT JOIN students s ON t.student_id = s.id
                  LEFT JOIN payments p ON t.payment_id = p.id
                  LEFT JOIN users u ON t.created_by = u.id
                  WHERE t.id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        return $stmt->fetch();
    }

    public function getByStudent($studentId, $limit = 50, $offset = 0) {
        $query = "SELECT t.*, p.reference_number as payment_reference, u.name as created_by_name
                  FROM " . $this->table . " t
                  LEFT JOIN payments p ON t.payment_id = p.id
                  LEFT JOIN users u ON t.created_by = u.id
                  WHERE t.student_id = :student_id
                  ORDER BY t.transaction_date DESC
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':student_id', $studentId);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    public function getByPayment($paymentId) {
        $query = "SELECT t.*, u.name as created_by_name
                  FROM " . $this->table . " t
                  LEFT JOIN users u ON t.created_by = u.id
                  WHERE t.payment_id = :payment_id
                  ORDER BY t.transaction_date DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':payment_id', $paymentId);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    public function getTotalCount() {
        $query = "SELECT COUNT(*) as total FROM " . $this->table;
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch();
        return $result['total'];
    }
}
?> 