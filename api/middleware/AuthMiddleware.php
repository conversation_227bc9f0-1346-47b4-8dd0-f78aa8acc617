<?php
require_once __DIR__ . '/../config/auth.php';
require_once __DIR__ . '/../utils/Response.php';

class AuthMiddleware {
    public static function authenticate() {
        $headers = getallheaders();
        $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? '';
        
        if (empty($authHeader)) {
            Response::unauthorized('Authorization header missing');
        }
        
        // Extract token from "Bearer <token>"
        if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            Response::unauthorized('Invalid authorization header format');
        }
        
        $token = $matches[1];
        
        try {
            $decoded = self::verifyToken($token);
            return $decoded;
        } catch (Exception $e) {
            Response::unauthorized('Invalid or expired token');
        }
    }
    
    public static function authorize($allowedRoles = []) {
        $user = self::authenticate();
        
        if (!empty($allowedRoles) && !in_array($user->role, $allowedRoles)) {
            Response::forbidden('Insufficient permissions');
        }
        
        return $user;
    }
    
    public static function generateToken($payload) {
        $header = json_encode(['typ' => 'JWT', 'alg' => AuthConfig::getJwtAlgorithm()]);
        
        $payload['iat'] = time();
        $payload['exp'] = time() + AuthConfig::getJwtExpiration();
        $payload = json_encode($payload);
        
        $base64Header = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
        $base64Payload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));
        
        $signature = hash_hmac('sha256', $base64Header . "." . $base64Payload, AuthConfig::getJwtSecret(), true);
        $base64Signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));
        
        return $base64Header . "." . $base64Payload . "." . $base64Signature;
    }
    
    public static function verifyToken($token) {
        $parts = explode('.', $token);
        
        if (count($parts) !== 3) {
            throw new Exception('Invalid token format');
        }
        
        list($base64Header, $base64Payload, $base64Signature) = $parts;
        
        // Verify signature
        $signature = hash_hmac('sha256', $base64Header . "." . $base64Payload, AuthConfig::getJwtSecret(), true);
        $expectedSignature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));
        
        if (!hash_equals($base64Signature, $expectedSignature)) {
            throw new Exception('Invalid token signature');
        }
        
        // Decode payload
        $payload = json_decode(base64_decode(str_replace(['-', '_'], ['+', '/'], $base64Payload)));
        
        if (!$payload) {
            throw new Exception('Invalid token payload');
        }
        
        // Check expiration
        if (isset($payload->exp) && $payload->exp < time()) {
            throw new Exception('Token has expired');
        }
        
        return $payload;
    }
    
    public static function hashPassword($password) {
        return password_hash($password, PASSWORD_DEFAULT, AuthConfig::getPasswordOptions());
    }
    
    public static function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }
    
    public static function generateUUID() {
        return sprintf(
            '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }
    
    public static function isValidRole($role) {
        return in_array($role, AuthConfig::getUserRoles());
    }
    
    public static function canAccessResource($userRole, $requiredRoles) {
        if (empty($requiredRoles)) {
            return true;
        }
        
        // Super admin can access everything
        if ($userRole === 'super_admin') {
            return true;
        }
        
        return in_array($userRole, $requiredRoles);
    }
}
?> 