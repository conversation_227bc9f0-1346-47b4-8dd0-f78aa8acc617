<?php
require_once __DIR__ . '/../config/cors.php';

class CorsMiddleware {
    public static function handle() {
        $origin = $_SERVER['HTTP_ORIGIN'] ?? '';
        $allowedOrigins = CorsConfig::getAllowedOrigins();
        
        // Check if origin is allowed
        if (in_array($origin, $allowedOrigins) || in_array('*', $allowedOrigins)) {
            header('Access-Control-Allow-Origin: ' . $origin);
        }
        
        header('Access-Control-Allow-Methods: ' . implode(', ', CorsConfig::getAllowedMethods()));
        header('Access-Control-Allow-Headers: ' . implode(', ', CorsConfig::getAllowedHeaders()));
        header('Access-Control-Allow-Credentials: true');
        header('Access-Control-Max-Age: ' . CorsConfig::getMaxAge());
        
        // Handle preflight requests
        if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            http_response_code(200);
            exit();
        }
    }
    
    public static function isOriginAllowed($origin) {
        $allowedOrigins = CorsConfig::getAllowedOrigins();
        return in_array($origin, $allowedOrigins) || in_array('*', $allowedOrigins);
    }
}
?> 