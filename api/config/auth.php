<?php
class AuthConfig {
    // JWT Configuration
    public static function getJwtSecret() {
        return 'your-super-secret-jwt-key-change-this-in-production';
    }

    public static function getJwtAlgorithm() {
        return 'HS256';
    }

    public static function getJwtExpiration() {
        return 24 * 60 * 60; // 24 hours in seconds
    }

    // Password Configuration
    public static function getPasswordMinLength() {
        return 6;
    }

    public static function getPasswordOptions() {
        return [
            'cost' => 12
        ];
    }

    // Session Configuration
    public static function getSessionTimeout() {
        return 30 * 60; // 30 minutes in seconds
    }

    // Rate Limiting
    public static function getMaxLoginAttempts() {
        return 5;
    }

    public static function getLockoutDuration() {
        return 15 * 60; // 15 minutes in seconds
    }

    // User Roles
    public static function getUserRoles() {
        return [
            'super_admin',
            'admin', 
            'teacher',
            'student',
            'enrollment_admin',
            'finance_admin'
        ];
    }

    // Default role for new users
    public static function getDefaultRole() {
        return 'student';
    }
}
?> 