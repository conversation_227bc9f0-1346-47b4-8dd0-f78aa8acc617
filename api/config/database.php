<?php
// Database Configuration for cPanel
// Update these values with your actual cPanel database details

// Database constants
define('DB_HOST', 'localhost');
define('DB_NAME', 'ptechinc_university_portal'); // Correct database name that's working
define('DB_USER', 'ptechinc_Jmsandi99'); // Your actual database username from cPanel  
define('DB_PASS', 'Jm$535301'); // Your actual database password

class Database {
    private $host = DB_HOST;
    private $db_name = DB_NAME;
    private $username = DB_USER;
    private $password = DB_PASS;
    private $conn;

    public function getConnection() {
        $this->conn = null;
        
        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=utf8mb4",
                $this->username,
                $this->password,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false,
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
                ]
            );
        } catch(PDOException $exception) {
            error_log("Database connection error: " . $exception->getMessage());
            return null;
        }
        
        return $this->conn;
    }

    public function closeConnection() {
        $this->conn = null;
    }

    // Test database connection
    public function testConnection() {
        $conn = $this->getConnection();
        if ($conn) {
            return [
                'success' => true,
                'message' => 'Database connection successful',
                'database' => $this->db_name,
                'host' => $this->host
            ];
        } else {
            return [
                'success' => false,
                'message' => 'Database connection failed',
                'database' => $this->db_name,
                'host' => $this->host
            ];
        }
    }
}
?> 