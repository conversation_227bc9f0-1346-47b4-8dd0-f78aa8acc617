<?php
class CorsConfig {
    public static function getAllowedOrigins() {
        return [
            'http://localhost:3000',
            'http://localhost:5173',
            'https://unitest.ptechin.com',
            'https://ptechin.com'
        ];
    }

    public static function getAllowedMethods() {
        return ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'];
    }

    public static function getAllowedHeaders() {
        return [
            'Content-Type',
            'Authorization',
            'X-Requested-With',
            'Accept',
            'Origin'
        ];
    }

    public static function getMaxAge() {
        return 86400; // 24 hours
    }
}
?> 