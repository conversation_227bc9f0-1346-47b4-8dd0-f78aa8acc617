<?php
// Start output buffering to capture any unwanted debug output
ob_start();

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once 'config/database.php';
require_once 'middleware/CorsMiddleware.php';
require_once 'middleware/AuthMiddleware.php';
require_once 'routes/api.php';

// Initialize CORS
CorsMiddleware::handle();

// Get the request URI and method
$request_uri = $_SERVER['REQUEST_URI'];
$request_method = $_SERVER['REQUEST_METHOD'];

// Remove query string and base path
$path = parse_url($request_uri, PHP_URL_PATH);
$path = str_replace('/api', '', $path);

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

if (!$db) {
    // Clean any unwanted output before sending response
    ob_clean();
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database connection failed']);
    exit();
}

// Route the request
try {
    // Clean any unwanted output before routing
    ob_clean();
    ApiRouter::route($path, $request_method, $db);
} catch (Exception $e) {
    // Clean any unwanted output before sending error response
    ob_clean();
    http_response_code(500);
    echo json_encode([
        'success' => false, 
        'message' => 'Internal server error',
        'error' => $e->getMessage()
    ]);
}
?> 