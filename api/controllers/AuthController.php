<?php
require_once __DIR__ . '/../models/User.php';
require_once __DIR__ . '/../middleware/AuthMiddleware.php';
require_once __DIR__ . '/../utils/Response.php';
require_once __DIR__ . '/../utils/Validator.php';
require_once __DIR__ . '/../config/auth.php';

class AuthController {
    private $userModel;
    private $db;

    public function __construct($db) {
        $this->db = $db;
        $this->userModel = new User($db);
    }

    public function login() {
        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!$data) {
            Response::error('Invalid JSON data', 400);
        }

        $validator = new Validator($data);
        $validator->required('email')->required('password');

        if ($validator->fails()) {
            Response::validationError($validator->getErrors());
        }

        $email = Validator::sanitizeInput($data['email']);
        $password = $data['password'];

        // Authenticate user
        $user = $this->userModel->authenticate($email, $password);

        if (!$user) {
            Response::unauthorized('Invalid email or password');
        }

        // Generate JWT token
        $tokenPayload = [
            'user_id' => $user['id'],
            'email' => $user['email'],
            'role' => $user['role']
        ];

        $token = AuthMiddleware::generateToken($tokenPayload);

        // Update last login
        $this->userModel->updateLastLogin($user['id']);

        Response::success([
            'user' => $user,
            'token' => $token,
            'expires_in' => AuthConfig::getJwtExpiration()
        ], 'Login successful');
    }

    public function register() {
        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!$data) {
            Response::error('Invalid JSON data', 400);
        }

        $validator = new Validator($data);
        $validator->required('name')
                 ->required('email')
                 ->required('password')
                 ->email('email')
                 ->minLength('password', AuthConfig::getPasswordMinLength())
                 ->unique('email', 'users', 'email', $this->db);

        if (isset($data['role']) && !AuthMiddleware::isValidRole($data['role'])) {
            $validator->getErrors()['role'] = 'Invalid role specified';
        }

        if ($validator->fails()) {
            Response::validationError($validator->getErrors());
        }

        // Sanitize input
        $userData = [
            'name' => Validator::sanitizeInput($data['name']),
            'email' => Validator::sanitizeInput($data['email']),
            'password' => $data['password'],
            'role' => $data['role'] ?? AuthConfig::getDefaultRole()
        ];

        $user = $this->userModel->create($userData);

        if ($user) {
            // Generate JWT token
            $tokenPayload = [
                'user_id' => $user['id'],
                'email' => $user['email'],
                'role' => $user['role']
            ];

            $token = AuthMiddleware::generateToken($tokenPayload);

            Response::success([
                'user' => $user,
                'token' => $token,
                'expires_in' => AuthConfig::getJwtExpiration()
            ], 'Registration successful', 201);
        } else {
            Response::serverError('Failed to create user');
        }
    }

    public function getCurrentUser() {
        $user = AuthMiddleware::authenticate();
        
        $userData = $this->userModel->getById($user->user_id);
        
        if ($userData) {
            Response::success($userData, 'User data retrieved successfully');
        } else {
            Response::notFound('User not found');
        }
    }

    public function refreshToken() {
        $user = AuthMiddleware::authenticate();
        
        // Generate new token
        $tokenPayload = [
            'user_id' => $user->user_id,
            'email' => $user->email,
            'role' => $user->role
        ];

        $token = AuthMiddleware::generateToken($tokenPayload);

        Response::success([
            'token' => $token,
            'expires_in' => AuthConfig::getJwtExpiration()
        ], 'Token refreshed successfully');
    }

    public function logout() {
        // For JWT tokens, logout is typically handled client-side
        // by removing the token from storage
        Response::success(null, 'Logout successful');
    }

    public function changePassword() {
        $user = AuthMiddleware::authenticate();
        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!$data) {
            Response::error('Invalid JSON data', 400);
        }

        $validator = new Validator($data);
        $validator->required('current_password')
                 ->required('new_password')
                 ->minLength('new_password', AuthConfig::getPasswordMinLength());

        if ($validator->fails()) {
            Response::validationError($validator->getErrors());
        }

        // Verify current password
        $userData = $this->userModel->getById($user->user_id);
        if (!AuthMiddleware::verifyPassword($data['current_password'], $userData['password'])) {
            Response::error('Current password is incorrect', 400);
        }

        // Update password
        if ($this->userModel->updatePassword($user->user_id, $data['new_password'])) {
            Response::success(null, 'Password changed successfully');
        } else {
            Response::serverError('Failed to change password');
        }
    }

    public function updateProfile() {
        $user = AuthMiddleware::authenticate();
        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!$data) {
            Response::error('Invalid JSON data', 400);
        }

        $validator = new Validator($data);
        
        if (isset($data['email'])) {
            $validator->email('email')
                     ->unique('email', 'users', 'email', $this->db, $user->user_id);
        }

        if ($validator->fails()) {
            Response::validationError($validator->getErrors());
        }

        // Sanitize input
        $updateData = [];
        $allowedFields = ['name', 'email'];
        
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $updateData[$field] = Validator::sanitizeInput($data[$field]);
            }
        }

        if (empty($updateData)) {
            Response::error('No valid fields to update', 400);
        }

        if ($this->userModel->update($user->user_id, $updateData)) {
            $updatedUser = $this->userModel->getById($user->user_id);
            Response::success($updatedUser, 'Profile updated successfully');
        } else {
            Response::serverError('Failed to update profile');
        }
    }
}
?> 