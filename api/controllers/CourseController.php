<?php
require_once __DIR__ . '/../models/Course.php';
require_once __DIR__ . '/../models/Student.php';
require_once __DIR__ . '/../middleware/AuthMiddleware.php';
require_once __DIR__ . '/../utils/Response.php';
require_once __DIR__ . '/../utils/Validator.php';

class CourseController {
    private $courseModel;
    private $levelModel;
    private $studentModel;
    private $db;

    public function __construct($db) {
        $this->db = $db;
        $this->courseModel = new Course($db);
        $this->levelModel = new Level($db);
        $this->studentModel = new Student($db);
    }

    // Course methods
    public function getAll() {
        AuthMiddleware::authorize(['super_admin', 'admin', 'enrollment_admin', 'teacher']);

        $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
        $limit = isset($_GET['limit']) ? min(100, max(1, intval($_GET['limit']))) : 50;
        $offset = ($page - 1) * $limit;

        $search = $_GET['search'] ?? '';
        $status = $_GET['status'] ?? '';

        if ($search) {
            $courses = $this->courseModel->search($search, $limit, $offset);
            $total = count($courses); // Simplified for search
        } elseif ($status === 'active') {
            $courses = $this->courseModel->getActive();
            $total = count($courses);
        } else {
            $courses = $this->courseModel->getAll($limit, $offset);
            $total = $this->courseModel->getTotalCount();
        }

        Response::paginated($courses, $total, $page, $limit, 'Courses retrieved successfully');
    }

    public function getById($id) {
        AuthMiddleware::authorize(['super_admin', 'admin', 'enrollment_admin', 'teacher']);

        $course = $this->courseModel->getById($id);

        if ($course) {
            Response::success($course, 'Course retrieved successfully');
        } else {
            Response::notFound('Course not found');
        }
    }

    public function create() {
        AuthMiddleware::authorize(['super_admin', 'admin']);

        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!$data) {
            Response::error('Invalid JSON data', 400);
        }

        $validator = new Validator($data);
        $validator->required('name')
                 ->required('code')
                 ->required('duration')
                 ->required('fee')
                 ->numeric('fee')
                 ->unique('code', 'courses', 'code', $this->db);

        if ($validator->fails()) {
            Response::validationError($validator->getErrors());
        }

        // Sanitize input
        $courseData = [
            'name' => Validator::sanitizeInput($data['name']),
            'code' => Validator::sanitizeInput($data['code']),
            'description' => Validator::sanitizeInput($data['description'] ?? ''),
            'duration' => Validator::sanitizeInput($data['duration']),
            'fee' => floatval($data['fee']),
            'status' => $data['status'] ?? 'Active'
        ];

        $course = $this->courseModel->create($courseData);

        if ($course) {
            Response::success($course, 'Course created successfully', 201);
        } else {
            Response::serverError('Failed to create course');
        }
    }

    public function update($id) {
        AuthMiddleware::authorize(['super_admin', 'admin']);

        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!$data) {
            Response::error('Invalid JSON data', 400);
        }

        // Check if course exists
        $existingCourse = $this->courseModel->getById($id);
        if (!$existingCourse) {
            Response::notFound('Course not found');
        }

        $validator = new Validator($data);
        
        if (isset($data['code'])) {
            $validator->unique('code', 'courses', 'code', $this->db, $id);
        }
        
        if (isset($data['fee'])) {
            $validator->numeric('fee');
        }

        if ($validator->fails()) {
            Response::validationError($validator->getErrors());
        }

        // Sanitize input
        $updateData = [];
        $fields = ['name', 'code', 'description', 'duration', 'status'];

        foreach ($fields as $field) {
            if (isset($data[$field])) {
                $updateData[$field] = Validator::sanitizeInput($data[$field]);
            }
        }

        if (isset($data['fee'])) {
            $updateData['fee'] = floatval($data['fee']);
        }

        if (empty($updateData)) {
            Response::error('No valid fields to update', 400);
        }

        if ($this->courseModel->update($id, $updateData)) {
            $updatedCourse = $this->courseModel->getById($id);
            Response::success($updatedCourse, 'Course updated successfully');
        } else {
            Response::serverError('Failed to update course');
        }
    }

    public function delete($id) {
        AuthMiddleware::authorize(['super_admin', 'admin']);

        // Check if course exists
        $course = $this->courseModel->getById($id);
        if (!$course) {
            Response::notFound('Course not found');
        }

        $result = $this->courseModel->delete($id);

        if ($result['success']) {
            Response::success(null, $result['message']);
        } else {
            Response::error($result['message'], 400);
        }
    }

    public function getCourseStudents($id) {
        AuthMiddleware::authorize(['super_admin', 'admin', 'enrollment_admin', 'teacher']);

        // Check if course exists
        $course = $this->courseModel->getById($id);
        if (!$course) {
            Response::notFound('Course not found');
        }

        $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
        $limit = isset($_GET['limit']) ? min(100, max(1, intval($_GET['limit']))) : 50;
        $offset = ($page - 1) * $limit;

        $students = $this->studentModel->getByCourse($id, $limit, $offset);
        $total = $this->studentModel->getCountByCourse($id);

        Response::paginated($students, $total, $page, $limit, 'Course students retrieved successfully');
    }

    public function getCourseLevels($id) {
        AuthMiddleware::authorize(['super_admin', 'admin', 'enrollment_admin', 'teacher']);

        // Check if course exists
        $course = $this->courseModel->getById($id);
        if (!$course) {
            Response::notFound('Course not found');
        }

        $levels = $this->levelModel->getByCourse($id);

        Response::success([
            'course' => $course,
            'levels' => $levels
        ], 'Course levels retrieved successfully');
    }

    public function getCourseStats($id) {
        AuthMiddleware::authorize(['super_admin', 'admin', 'enrollment_admin']);

        // Check if course exists
        $course = $this->courseModel->getById($id);
        if (!$course) {
            Response::notFound('Course not found');
        }

        $revenueStats = $this->courseModel->getRevenueStats($id);

        Response::success([
            'course' => $course,
            'revenue_stats' => $revenueStats
        ], 'Course statistics retrieved successfully');
    }

    // Level methods
    public function getAllLevels() {
        AuthMiddleware::authorize(['super_admin', 'admin', 'enrollment_admin', 'teacher']);

        $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
        $limit = isset($_GET['limit']) ? min(100, max(1, intval($_GET['limit']))) : 50;
        $offset = ($page - 1) * $limit;

        $course_id = $_GET['course_id'] ?? '';

        if ($course_id) {
            $levels = $this->levelModel->getByCourse($course_id);
            $total = count($levels);
        } else {
            $levels = $this->levelModel->getAll($limit, $offset);
            $total = $this->levelModel->getTotalCount();
        }

        Response::paginated($levels, $total, $page, $limit, 'Levels retrieved successfully');
    }

    public function getLevelById($id) {
        AuthMiddleware::authorize(['super_admin', 'admin', 'enrollment_admin', 'teacher']);

        $level = $this->levelModel->getById($id);

        if ($level) {
            Response::success($level, 'Level retrieved successfully');
        } else {
            Response::notFound('Level not found');
        }
    }

    public function createLevel() {
        AuthMiddleware::authorize(['super_admin', 'admin']);

        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!$data) {
            Response::error('Invalid JSON data', 400);
        }

        $validator = new Validator($data);
        $validator->required('name')
                 ->required('code')
                 ->required('course_id');

        if ($validator->fails()) {
            Response::validationError($validator->getErrors());
        }

        // Check if course exists
        $course = $this->courseModel->getById($data['course_id']);
        if (!$course) {
            Response::error('Invalid course ID', 400);
        }

        // Sanitize input
        $levelData = [
            'name' => Validator::sanitizeInput($data['name']),
            'code' => Validator::sanitizeInput($data['code']),
            'description' => Validator::sanitizeInput($data['description'] ?? ''),
            'course_id' => $data['course_id'],
            'order_index' => intval($data['order_index'] ?? 1)
        ];

        $level = $this->levelModel->create($levelData);

        if ($level) {
            Response::success($level, 'Level created successfully', 201);
        } else {
            Response::serverError('Failed to create level');
        }
    }

    public function updateLevel($id) {
        AuthMiddleware::authorize(['super_admin', 'admin']);

        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!$data) {
            Response::error('Invalid JSON data', 400);
        }

        // Check if level exists
        $existingLevel = $this->levelModel->getById($id);
        if (!$existingLevel) {
            Response::notFound('Level not found');
        }

        // Sanitize input
        $updateData = [];
        $fields = ['name', 'code', 'description'];

        foreach ($fields as $field) {
            if (isset($data[$field])) {
                $updateData[$field] = Validator::sanitizeInput($data[$field]);
            }
        }

        if (isset($data['order_index'])) {
            $updateData['order_index'] = intval($data['order_index']);
        }

        if (empty($updateData)) {
            Response::error('No valid fields to update', 400);
        }

        if ($this->levelModel->update($id, $updateData)) {
            $updatedLevel = $this->levelModel->getById($id);
            Response::success($updatedLevel, 'Level updated successfully');
        } else {
            Response::serverError('Failed to update level');
        }
    }

    public function deleteLevel($id) {
        AuthMiddleware::authorize(['super_admin', 'admin']);

        // Check if level exists
        $level = $this->levelModel->getById($id);
        if (!$level) {
            Response::notFound('Level not found');
        }

        $result = $this->levelModel->delete($id);

        if ($result['success']) {
            Response::success(null, $result['message']);
        } else {
            Response::error($result['message'], 400);
        }
    }

    public function getLevelStudents($id) {
        AuthMiddleware::authorize(['super_admin', 'admin', 'enrollment_admin', 'teacher']);

        // Check if level exists
        $level = $this->levelModel->getById($id);
        if (!$level) {
            Response::notFound('Level not found');
        }

        $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
        $limit = isset($_GET['limit']) ? min(100, max(1, intval($_GET['limit']))) : 50;
        $offset = ($page - 1) * $limit;

        $students = $this->studentModel->getByLevel($id, $limit, $offset);
        $total = $this->studentModel->getCountByLevel($id);

        Response::paginated($students, $total, $page, $limit, 'Level students retrieved successfully');
    }

    public function getPopularCourses() {
        AuthMiddleware::authorize(['super_admin', 'admin', 'enrollment_admin']);

        $limit = isset($_GET['limit']) ? min(20, max(1, intval($_GET['limit']))) : 5;

        $courses = $this->courseModel->getPopularCourses($limit);

        Response::success($courses, 'Popular courses retrieved successfully');
    }

    public function getRevenueStats() {
        AuthMiddleware::authorize(['super_admin', 'admin', 'finance_admin']);

        $stats = $this->courseModel->getRevenueStats();

        Response::success($stats, 'Course revenue statistics retrieved successfully');
    }
}
?> 