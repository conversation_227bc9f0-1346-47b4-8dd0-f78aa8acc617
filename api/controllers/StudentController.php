<?php
require_once __DIR__ . '/../models/Student.php';
require_once __DIR__ . '/../models/Payment.php';
require_once __DIR__ . '/../middleware/AuthMiddleware.php';
require_once __DIR__ . '/../utils/Response.php';
require_once __DIR__ . '/../utils/Validator.php';
require_once __DIR__ . '/../utils/FileUpload.php';

class StudentController {
    private $studentModel;
    private $paymentModel;
    private $db;

    public function __construct($db) {
        $this->db = $db;
        $this->studentModel = new Student($db);
        $this->paymentModel = new Payment($db);
    }

    public function getAll() {
        // Check authentication
        AuthMiddleware::authorize(['super_admin', 'admin', 'enrollment_admin']);

        $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
        $limit = isset($_GET['limit']) ? min(100, max(1, intval($_GET['limit']))) : 50;
        $offset = ($page - 1) * $limit;

        $search = $_GET['search'] ?? '';
        $course_id = $_GET['course_id'] ?? '';
        $level_id = $_GET['level_id'] ?? '';
        $status = $_GET['status'] ?? '';

        if ($search) {
            $students = $this->studentModel->search($search, $limit, $offset);
            $total = count($students); // Simplified for search
        } elseif ($course_id) {
            $students = $this->studentModel->getByCourse($course_id, $limit, $offset);
            $total = $this->studentModel->getCountByCourse($course_id);
        } elseif ($level_id) {
            $students = $this->studentModel->getByLevel($level_id, $limit, $offset);
            $total = $this->studentModel->getCountByLevel($level_id);
        } elseif ($status) {
            $students = $this->studentModel->getByEnrollmentStatus($status, $limit, $offset);
            $total = $this->studentModel->getCountByStatus($status);
        } else {
            $students = $this->studentModel->getAll($limit, $offset);
            $total = $this->studentModel->getTotalCount();
        }

        Response::paginated($students, $total, $page, $limit, 'Students retrieved successfully');
    }

    public function getById($id) {
        AuthMiddleware::authorize(['super_admin', 'admin', 'enrollment_admin']);

        $student = $this->studentModel->getById($id);

        if ($student) {
            Response::success($student, 'Student retrieved successfully');
        } else {
            Response::notFound('Student not found');
        }
    }

    public function create() {
        AuthMiddleware::authorize(['super_admin', 'admin', 'enrollment_admin']);

        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!$data) {
            Response::error('Invalid JSON data', 400);
        }

        $validator = Validator::validateStudentData($data, $this->db);

        if ($validator->fails()) {
            Response::validationError($validator->getErrors());
        }

        // Sanitize input
        $studentData = [];
        $fields = [
            'student_id', 'name', 'email', 'date_of_birth', 'date_of_registration',
            'nationality', 'gender', 'address', 'mobile_number', 'whatsapp_number',
            'parent_name', 'parent_mobile', 'parent_whatsapp', 'parent_email',
            'parent_occupation', 'passport_picture', 'course_id', 'level_id',
            'enrollment_status', 'payment_status'
        ];

        foreach ($fields as $field) {
            if (isset($data[$field])) {
                $studentData[$field] = Validator::sanitizeInput($data[$field]);
            }
        }

        $student = $this->studentModel->create($studentData);

        if ($student) {
            Response::success($student, 'Student created successfully', 201);
        } else {
            Response::serverError('Failed to create student');
        }
    }

    public function update($id) {
        AuthMiddleware::authorize(['super_admin', 'admin', 'enrollment_admin']);

        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!$data) {
            Response::error('Invalid JSON data', 400);
        }

        // Check if student exists
        $existingStudent = $this->studentModel->getById($id);
        if (!$existingStudent) {
            Response::notFound('Student not found');
        }

        $validator = Validator::validateStudentData($data, $this->db, $id);

        if ($validator->fails()) {
            Response::validationError($validator->getErrors());
        }

        // Sanitize input
        $updateData = [];
        $fields = [
            'name', 'email', 'date_of_birth', 'nationality', 'gender', 'address',
            'mobile_number', 'whatsapp_number', 'parent_name', 'parent_mobile',
            'parent_whatsapp', 'parent_email', 'parent_occupation', 'passport_picture',
            'course_id', 'level_id', 'enrollment_status', 'payment_status'
        ];

        foreach ($fields as $field) {
            if (isset($data[$field])) {
                $updateData[$field] = Validator::sanitizeInput($data[$field]);
            }
        }

        if (empty($updateData)) {
            Response::error('No valid fields to update', 400);
        }

        if ($this->studentModel->update($id, $updateData)) {
            $updatedStudent = $this->studentModel->getById($id);
            Response::success($updatedStudent, 'Student updated successfully');
        } else {
            Response::serverError('Failed to update student');
        }
    }

    public function delete($id) {
        AuthMiddleware::authorize(['super_admin', 'admin']);

        // Check if student exists
        $student = $this->studentModel->getById($id);
        if (!$student) {
            Response::notFound('Student not found');
        }

        // Check if student has payments
        $payments = $this->paymentModel->getByStudent($id, 1, 0);
        if (!empty($payments)) {
            Response::error('Cannot delete student with payment records', 400);
        }

        if ($this->studentModel->delete($id)) {
            Response::success(null, 'Student deleted successfully');
        } else {
            Response::serverError('Failed to delete student');
        }
    }

    public function uploadPhoto($id) {
        AuthMiddleware::authorize(['super_admin', 'admin', 'enrollment_admin']);

        // Check if student exists
        $student = $this->studentModel->getById($id);
        if (!$student) {
            Response::notFound('Student not found');
        }

        if (!isset($_FILES['photo'])) {
            Response::error('No photo file uploaded', 400);
        }

        $uploader = new FileUpload();
        $result = $uploader->uploadImage($_FILES['photo'], 'students', 'student_' . $student['student_id']);

        if ($result['success']) {
            // Update student record with photo path
            $updateData = ['passport_picture' => $result['file_path']];
            
            if ($this->studentModel->update($id, $updateData)) {
                $updatedStudent = $this->studentModel->getById($id);
                Response::success([
                    'student' => $updatedStudent,
                    'upload_result' => $result
                ], 'Photo uploaded successfully');
            } else {
                Response::serverError('Photo uploaded but failed to update student record');
            }
        } else {
            Response::error($result['message'], 400);
        }
    }

    public function getStudentPayments($id) {
        AuthMiddleware::authorize(['super_admin', 'admin', 'finance_admin']);

        // Check if student exists
        $student = $this->studentModel->getById($id);
        if (!$student) {
            Response::notFound('Student not found');
        }

        $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
        $limit = isset($_GET['limit']) ? min(100, max(1, intval($_GET['limit']))) : 50;
        $offset = ($page - 1) * $limit;

        $payments = $this->paymentModel->getByStudent($id, $limit, $offset);
        $summary = $this->paymentModel->getStudentPaymentSummary($id);

        Response::success([
            'student' => $student,
            'payments' => $payments,
            'summary' => $summary
        ], 'Student payments retrieved successfully');
    }

    public function getStudentTransactions($id) {
        AuthMiddleware::authorize(['super_admin', 'admin', 'finance_admin']);

        // Check if student exists
        $student = $this->studentModel->getById($id);
        if (!$student) {
            Response::notFound('Student not found');
        }

        require_once __DIR__ . '/../models/Payment.php';
        $transactionModel = new Transaction($this->db);

        $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
        $limit = isset($_GET['limit']) ? min(100, max(1, intval($_GET['limit']))) : 50;
        $offset = ($page - 1) * $limit;

        $transactions = $transactionModel->getByStudent($id, $limit, $offset);

        Response::success([
            'student' => $student,
            'transactions' => $transactions
        ], 'Student transactions retrieved successfully');
    }

    public function getByStudentId() {
        AuthMiddleware::authorize(['super_admin', 'admin', 'enrollment_admin']);

        $studentId = $_GET['student_id'] ?? '';
        
        if (empty($studentId)) {
            Response::error('Student ID parameter is required', 400);
        }

        $student = $this->studentModel->getByStudentId($studentId);

        if ($student) {
            Response::success($student, 'Student retrieved successfully');
        } else {
            Response::notFound('Student not found');
        }
    }

    public function getByEmail() {
        AuthMiddleware::authorize(['super_admin', 'admin', 'enrollment_admin']);

        $email = $_GET['email'] ?? '';
        
        if (empty($email)) {
            Response::error('Email parameter is required', 400);
        }

        $student = $this->studentModel->getByEmail($email);

        if ($student) {
            Response::success($student, 'Student retrieved successfully');
        } else {
            Response::notFound('Student not found');
        }
    }

    public function getRecentlyRegistered() {
        AuthMiddleware::authorize(['super_admin', 'admin', 'enrollment_admin']);

        $days = isset($_GET['days']) ? max(1, intval($_GET['days'])) : 30;
        $limit = isset($_GET['limit']) ? min(100, max(1, intval($_GET['limit']))) : 10;

        $students = $this->studentModel->getRecentlyRegistered($days, $limit);

        Response::success($students, 'Recently registered students retrieved successfully');
    }
}
?> 