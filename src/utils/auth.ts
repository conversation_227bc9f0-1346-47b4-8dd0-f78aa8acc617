
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

export const fetchUserProfile = async (userId: string) => {
  console.log('Fetching profile for user:', userId);
  
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('id, username, full_name, role, avatar_url')
      .eq('id', userId)
      .single();

    if (error) {
      console.error('Error fetching user profile:', error);
      throw error;
    }

    console.log('Profile fetched successfully:', data);
    return data;
  } catch (error) {
    console.error('Error in fetchUserProfile:', error);
    throw error;
  }
};

export const handleLogout = async (
  setSession: (session: null) => void,
  setUser: (user: null) => void,
  setUserProfile: (profile: null) => void,
  navigate: (path: string) => void
) => {
  try {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
    
    setSession(null);
    setUser(null);
    setUserProfile(null);
    navigate('/login');
    toast.success('Logged out successfully');
  } catch (error) {
    console.error('Error in logout:', error);
    toast.error('Error logging out');
  }
};
