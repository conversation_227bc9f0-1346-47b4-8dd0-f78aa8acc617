/**
 * Utility functions for handling errors in a way that doesn't expose 
 * database technology to end users.
 */

/**
 * Parses Firebase auth error codes into user-friendly messages
 * without revealing the underlying technology
 */
export const getAuthErrorMessage = (error: any): string => {
  // Log for debugging but don't expose to users
  console.error('Auth error:', error);
  
  // Default error message
  let errorMessage = 'Authentication error. Please try again.';
  
  if (!error) return errorMessage;
  
  // If we have an error code, map it to a user-friendly message
  if (error.code) {
    switch(error.code) {
      case 'auth/invalid-credential':
        errorMessage = 'Invalid credentials. Please check your email and password.';
        break;
      case 'auth/user-not-found':
        errorMessage = 'No account found with this email.';
        break;
      case 'auth/wrong-password':
        errorMessage = 'Incorrect password. Please try again.';
        break;
      case 'auth/invalid-email':
        errorMessage = 'Invalid email format.';
        break;
      case 'auth/user-disabled':
        errorMessage = 'This account has been disabled.';
        break;
      case 'auth/email-already-in-use':
        errorMessage = 'This email is already in use.';
        break;
      case 'auth/weak-password':
        errorMessage = 'Password is too weak. Please use a stronger password.';
        break;
      case 'auth/too-many-requests':
        errorMessage = 'Too many failed attempts. Please try again later.';
        break;
      case 'auth/requires-recent-login':
        errorMessage = 'This action requires you to re-authenticate. Please log out and log in again.';
        break;
      default:
        errorMessage = 'Authentication error. Please try again.';
    }
  }
  
  return errorMessage;
};

/**
 * Parses database error codes into user-friendly messages
 * without revealing the underlying technology
 */
export const getDatabaseErrorMessage = (error: any, defaultMessage: string = 'Database operation failed'): string => {
  // Log for debugging but don't expose to users
  console.error('Database error:', error);
  
  if (!error) return defaultMessage;
  
  // If we have a permission-denied error
  if (error.code === 'permission-denied') {
    return 'You do not have permission to perform this action.';
  }
  
  // If we have a not-found error
  if (error.code === 'not-found') {
    return 'The requested data was not found.';
  }
  
  return defaultMessage;
};

/**
 * General error handler that doesn't expose underlying technology
 */
export const handleError = (error: any, defaultMessage: string = 'An unexpected error occurred'): string => {
  // Log for debugging but don't expose to users
  console.error('Error:', error);
  
  if (!error) return defaultMessage;
  
  // Check if it's an auth error
  if (error.code && error.code.startsWith('auth/')) {
    return getAuthErrorMessage(error);
  }
  
  // Check if it's a database error
  if (error.code && (
    error.code === 'permission-denied' || 
    error.code === 'not-found' || 
    error.code === 'already-exists'
  )) {
    return getDatabaseErrorMessage(error, defaultMessage);
  }
  
  // Return generic error message for all other cases
  return defaultMessage;
}; 