/**
 * Safely capitalizes the first letter of a string.
 * Returns the original string if it's null, undefined, or empty.
 */
export const capitalize = (str?: string | null): string => {
  if (!str) return '';
  return str.charAt(0).toUpperCase() + str.slice(1);
};

/**
 * Safely formats a string to title case (capitalizes each word).
 * Returns the original string if it's null, undefined, or empty.
 */
export const titleCase = (str?: string | null): string => {
  if (!str) return '';
  return str
    .split(' ')
    .map(word => capitalize(word))
    .join(' ');
};

/**
 * Truncates a string to the specified length and adds an ellipsis if needed.
 * Returns the original string if it's null, undefined, or shorter than maxLength.
 */
export const truncate = (str?: string | null, maxLength: number = 50): string => {
  if (!str) return '';
  if (str.length <= maxLength) return str;
  return str.slice(0, maxLength) + '...';
}; 