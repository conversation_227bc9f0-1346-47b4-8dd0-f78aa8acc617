import { useState, useEffect } from "react";
import { Student } from "@/types/student";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { BookOpen, Calendar, Clock, FileText, GraduationCap, Users, Loader2, AlertCircle } from "lucide-react";
import { apiClient } from "@/lib/api-client";

interface CourseOverviewProps {
  student: Student | null;
}

interface CourseModule {
  id: string;
  title: string;
  description: string;
  progress: number;
  status: string;
  duration: string;
  instructor: string;
  instructorAvatar: string;
  courseId: string;
}

interface UpcomingClass {
  id: string;
  title: string;
  date: Timestamp;
  time: string;
  location: string;
  instructor: string;
}

const CourseOverview = ({ student }: CourseOverviewProps) => {
  const [activeTab, setActiveTab] = useState("overview");
  const [courseModules, setCourseModules] = useState<CourseModule[]>([]);
  const [upcomingClasses, setUpcomingClasses] = useState<UpcomingClass[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [courseDetails, setCourseDetails] = useState({
    loading: false,
    name: '',
    code: '',
    description: ''
  });
  const [levelDetails, setLevelDetails] = useState({
    loading: false,
    name: '',
    code: '',
    description: ''
  });

  useEffect(() => {
    const fetchCourseData = async () => {
      if (!student?.id) return;
      
      try {
        setLoading(true);
        
        // First, get the student's courses
        const studentCoursesQuery = query(
          collection(db, 'student_courses'),
          where('student_id', '==', student.id)
        );
        
        const studentCoursesSnapshot = await getDocs(studentCoursesQuery);
        const courseIds = studentCoursesSnapshot.docs.map(doc => doc.data().course_id);
        
        if (courseIds.length === 0) {
          setCourseModules([]);
          setUpcomingClasses([]);
          setLoading(false);
          return;
        }
        
        // Fetch course modules
        const fetchModules = async () => {
          // Get all modules for these courses
          const modulesQuery = query(
            collection(db, 'course_modules'),
            where('courseId', 'in', courseIds),
            orderBy('order', 'asc')
          );
          
          const modulesSnapshot = await getDocs(modulesQuery);
          const modulesData = modulesSnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          } as CourseModule));
          
          // Get student progress for each module
          const progressPromises = modulesData.map(async (module) => {
            const progressQuery = query(
              collection(db, 'module_progress'),
              where('module_id', '==', module.id),
              where('student_id', '==', student.id)
            );
            
            const progressSnapshot = await getDocs(progressQuery);
            const progressData = progressSnapshot.docs[0]?.data();
            
            return {
              ...module,
              progress: progressData?.progress || 0,
              status: progressData?.status || 'not-started'
            };
          });
          
          const modulesWithProgress = await Promise.all(progressPromises);
          setCourseModules(modulesWithProgress);
        };
        
        // Fetch upcoming classes
        const fetchUpcomingClasses = async () => {
          const now = Timestamp.now();
          
          // Get upcoming classes for the student's courses
          const classesQuery = query(
            collection(db, 'class_sessions'),
            where('courseId', 'in', courseIds),
            where('date', '>=', now),
            orderBy('date', 'asc'),
            limit(5)
          );
          
          const classesSnapshot = await getDocs(classesQuery);
          const classesData = classesSnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          } as UpcomingClass));
          
          setUpcomingClasses(classesData);
        };
        
        // Execute all fetch operations
        await Promise.all([
          fetchModules(),
          fetchUpcomingClasses()
        ]);
        
        setError(null);
      } catch (err) {
        console.error('Error fetching course data:', err);
        setError('Failed to load course data. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchCourseData();
  }, [student?.id]);

  useEffect(() => {
    if (!student) return;

    // Fetch course details if we only have the ID
    if (student.course_id && (!student.course || !student.course.name)) {
      setCourseDetails(prev => ({ ...prev, loading: true }));
      const fetchCourse = async () => {
        try {
          const courseDoc = await getDoc(doc(db, 'courses', student.course_id));
          if (courseDoc.exists()) {
            const courseData = courseDoc.data();
            setCourseDetails({
              loading: false,
              name: courseData.name || 'Unknown Course',
              code: courseData.code || '',
              description: courseData.description || ''
            });
          } else {
            setCourseDetails({
              loading: false,
              name: `Course ID: ${student.course_id.substring(0, 6)}...`,
              code: '',
              description: 'Course details not found'
            });
          }
        } catch (error) {
          console.error('Error fetching course:', error);
          setCourseDetails({
            loading: false,
            name: `Course ID: ${student.course_id.substring(0, 6)}...`,
            code: '',
            description: 'Error loading course details'
          });
        }
      };
      fetchCourse();
    }

    // Fetch level details if we only have the ID
    if (student.level_id && (!student.level || !student.level.name)) {
      setLevelDetails(prev => ({ ...prev, loading: true }));
      const fetchLevel = async () => {
        try {
          const levelDoc = await getDoc(doc(db, 'levels', student.level_id));
          if (levelDoc.exists()) {
            const levelData = levelDoc.data();
            setLevelDetails({
              loading: false,
              name: levelData.name || 'Unknown Level',
              code: levelData.code || '',
              description: levelData.description || ''
            });
          } else {
            setLevelDetails({
              loading: false,
              name: `Level ID: ${student.level_id.substring(0, 6)}...`,
              code: '',
              description: 'Level details not found'
            });
          }
        } catch (error) {
          console.error('Error fetching level:', error);
          setLevelDetails({
            loading: false,
            name: `Level ID: ${student.level_id.substring(0, 6)}...`,
            code: '',
            description: 'Error loading level details'
          });
        }
      };
      fetchLevel();
    }
  }, [student]);

  // Helper function to get course name
  const getCourseName = () => {
    if (!student) return "Not Available";
    
    if (student.course?.name) {
      return student.course.name;
    } else if (courseDetails.loading) {
      return "Loading...";
    } else if (courseDetails.name) {
      return courseDetails.name;
    } else if (student.course_id) {
      return `Course ID: ${student.course_id.substring(0, 6)}...`;
    } else {
      return "Not Assigned";
    }
  };

  // Helper function to get course code
  const getCourseCode = () => {
    if (!student) return "N/A";
    
    if (student.course?.code) {
      return student.course.code;
    } else if (courseDetails.loading) {
      return "Loading...";
    } else if (courseDetails.code) {
      return courseDetails.code;
    } else {
      return "No course code";
    }
  };

  // Helper function to get level name
  const getLevelName = () => {
    if (!student) return "Not Available";
    
    if (student.level?.name) {
      return student.level.name;
    } else if (levelDetails.loading) {
      return "Loading...";
    } else if (levelDetails.name) {
      return levelDetails.name;
    } else if (student.level_id) {
      return `Level ID: ${student.level_id.substring(0, 6)}...`;
    } else {
      return "Not Assigned";
    }
  };

  if (!student) {
    return (
      <div className="p-6 text-center">
        <h2 className="text-2xl font-bold text-red-600">Student Information Not Found</h2>
        <p className="text-gray-600 mt-2">Your student profile could not be loaded. Please contact support.</p>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin text-green-600" />
        <span className="ml-2 text-lg text-gray-600">Loading course data...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 text-center">
        <AlertCircle className="h-12 w-12 text-red-600 mx-auto mb-2" />
        <h2 className="text-xl font-bold text-red-600">Error Loading Courses</h2>
        <p className="text-gray-600 mt-2">{error}</p>
        <Button 
          className="mt-4"
          onClick={() => window.location.reload()}
        >
          Retry
        </Button>
      </div>
    );
  }

  const formatDate = (timestamp: Timestamp) => {
    const date = timestamp.toDate();
    return new Intl.DateTimeFormat('en-US', { 
      month: 'long', 
      day: 'numeric', 
      year: 'numeric' 
    }).format(date);
  };

  // Calculate overall course progress
  const overallProgress = courseModules.reduce((sum, module) => sum + module.progress, 0) / courseModules.length;

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Course Overview</h1>
          <p className="text-muted-foreground">View your current course details and progress.</p>
        </div>
      </div>

      {/* Course Header */}
      <Card>
        <CardHeader className="pb-2">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
              <CardTitle className="text-xl">
                {courseDetails.loading ? (
                  <span className="text-gray-400">Loading...</span>
                ) : (
                  getCourseName()
                )}
              </CardTitle>
              <CardDescription>
                {courseDetails.loading ? (
                  <span className="text-gray-400">Loading...</span>
                ) : (
                  getCourseCode()
                )}
              </CardDescription>
            </div>
            <div className="mt-4 md:mt-0">
              <Badge variant="outline" className="bg-green-50 text-green-700 hover:bg-green-50 border-green-200">
                {levelDetails.loading ? (
                  <span className="text-gray-400">Loading...</span>
                ) : (
                  getLevelName()
                )} Level
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <p className="text-sm text-gray-500 mb-1">Course Progress</p>
              <div className="flex items-center gap-2">
                <Progress value={overallProgress} className="h-2" />
                <span className="text-sm font-medium">{Math.round(overallProgress)}%</span>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4">
              <div className="flex items-center">
                <Calendar className="h-5 w-5 text-green-600 mr-2" />
                <div>
                  <p className="text-sm font-medium">Start Date</p>
                  <p className="text-sm text-gray-500">September 1, 2022</p>
                </div>
              </div>
              <div className="flex items-center">
                <Clock className="h-5 w-5 text-blue-600 mr-2" />
                <div>
                  <p className="text-sm font-medium">Duration</p>
                  <p className="text-sm text-gray-500">6 months</p>
                </div>
              </div>
              <div className="flex items-center">
                <GraduationCap className="h-5 w-5 text-purple-600 mr-2" />
                <div>
                  <p className="text-sm font-medium">Expected Completion</p>
                  <p className="text-sm text-gray-500">March 1, 2023</p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Course Content Tabs */}
      <Tabs defaultValue="modules" className="w-full">
        <TabsList className="grid w-full md:w-[400px] grid-cols-3">
          <TabsTrigger value="modules">Modules</TabsTrigger>
          <TabsTrigger value="schedule">Schedule</TabsTrigger>
          <TabsTrigger value="resources">Resources</TabsTrigger>
        </TabsList>
        
        {/* Modules Tab */}
        <TabsContent value="modules" className="mt-6">
          <div className="space-y-4">
            {courseModules.map((module) => (
              <Card key={module.id} className={`
                ${module.status === 'completed' ? 'border-l-4 border-l-green-500' : 
                  module.status === 'in-progress' ? 'border-l-4 border-l-blue-500' : 
                  'border-l-4 border-l-gray-300'}
              `}>
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-lg">{module.title}</CardTitle>
                      <CardDescription>{module.description}</CardDescription>
                    </div>
                    <Badge className={`
                      ${module.status === 'completed' ? 'bg-green-100 text-green-800 hover:bg-green-100 border-green-200' : 
                        module.status === 'in-progress' ? 'bg-blue-100 text-blue-800 hover:bg-blue-100 border-blue-200' : 
                        'bg-gray-100 text-gray-800 hover:bg-gray-100 border-gray-200'}
                    `}>
                      {module.status === 'completed' ? 'Completed' : 
                        module.status === 'in-progress' ? 'In Progress' : 
                        'Upcoming'}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <p className="text-sm text-gray-500 mb-1">Module Progress</p>
                      <div className="flex items-center gap-2">
                        <Progress value={module.progress} className="h-2" />
                        <span className="text-sm font-medium">{module.progress}%</span>
                      </div>
                    </div>
                    <div className="flex flex-col sm:flex-row sm:items-center justify-between pt-2">
                      <div className="flex items-center mb-2 sm:mb-0">
                        <Avatar className="h-8 w-8 mr-2">
                          <AvatarImage src={module.instructorAvatar} alt={module.instructor} />
                          <AvatarFallback>{module.instructor.substring(0, 2)}</AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="text-sm font-medium">{module.instructor}</p>
                          <p className="text-xs text-gray-500">Instructor</p>
                        </div>
                      </div>
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 text-gray-500 mr-1" />
                        <p className="text-sm text-gray-500">{module.duration}</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="pt-0">
                  <Button variant="outline" size="sm" className="w-full sm:w-auto">
                    {module.status === 'completed' ? 'Review Module' : 
                      module.status === 'in-progress' ? 'Continue Learning' : 
                      'Preview Module'}
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </TabsContent>
        
        {/* Schedule Tab */}
        <TabsContent value="schedule" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Upcoming Classes</CardTitle>
              <CardDescription>Your scheduled classes for the next week</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {upcomingClasses.map((classItem) => (
                  <div key={classItem.id} className="flex flex-col sm:flex-row border-b pb-4 last:border-0 last:pb-0">
                    <div className="bg-blue-100 text-blue-800 p-2 rounded-md mr-4 text-center min-w-[80px] mb-2 sm:mb-0">
                      <div className="text-xs font-medium">
                        {formatDate(classItem.date)}
                      </div>
                      <div className="text-lg font-bold">
                        {classItem.time}
                      </div>
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium">{classItem.title}</h4>
                      <div className="flex flex-col sm:flex-row sm:items-center text-sm text-gray-500 mt-1">
                        <div className="flex items-center mr-4">
                          <Clock className="h-4 w-4 mr-1" />
                          {classItem.time}
                        </div>
                        <div className="flex items-center mr-4">
                          <Users className="h-4 w-4 mr-1" />
                          {classItem.instructor}
                        </div>
                        <div className="flex items-center">
                          <BookOpen className="h-4 w-4 mr-1" />
                          {classItem.location}
                        </div>
                      </div>
                    </div>
                    <div className="mt-2 sm:mt-0 sm:ml-4 flex items-center">
                      <Button variant="outline" size="sm">Join Class</Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full">View Full Schedule</Button>
            </CardFooter>
          </Card>
        </TabsContent>
        
        {/* Resources Tab */}
        <TabsContent value="resources" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Course Resources</CardTitle>
              <CardDescription>Access textbooks, reference materials, and additional resources</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="border rounded-md p-4">
                  <h3 className="font-medium mb-2">Textbooks</h3>
                  <ul className="space-y-2">
                    <li className="flex items-start">
                      <FileText className="h-5 w-5 text-blue-600 mr-2 mt-0.5" />
                      <div>
                        <p className="font-medium">English Grammar in Use</p>
                        <p className="text-sm text-gray-500">Raymond Murphy, Cambridge University Press</p>
                      </div>
                    </li>
                    <li className="flex items-start">
                      <FileText className="h-5 w-5 text-blue-600 mr-2 mt-0.5" />
                      <div>
                        <p className="font-medium">Oxford English Vocabulary in Use</p>
                        <p className="text-sm text-gray-500">Michael McCarthy, Oxford University Press</p>
                      </div>
                    </li>
                  </ul>
                </div>
                
                <div className="border rounded-md p-4">
                  <h3 className="font-medium mb-2">Online Resources</h3>
                  <ul className="space-y-2">
                    <li className="flex items-start">
                      <FileText className="h-5 w-5 text-green-600 mr-2 mt-0.5" />
                      <div>
                        <p className="font-medium">Language Learning Platform</p>
                        <p className="text-sm text-gray-500">Access interactive exercises and practice tests</p>
                        <Button variant="link" className="h-auto p-0 text-green-600">Access Platform</Button>
                      </div>
                    </li>
                    <li className="flex items-start">
                      <FileText className="h-5 w-5 text-green-600 mr-2 mt-0.5" />
                      <div>
                        <p className="font-medium">Pronunciation Guide</p>
                        <p className="text-sm text-gray-500">Audio resources for improving pronunciation</p>
                        <Button variant="link" className="h-auto p-0 text-green-600">Access Guide</Button>
                      </div>
                    </li>
                  </ul>
                </div>
                
                <div className="border rounded-md p-4">
                  <h3 className="font-medium mb-2">Supplementary Materials</h3>
                  <ul className="space-y-2">
                    <li className="flex items-start">
                      <FileText className="h-5 w-5 text-purple-600 mr-2 mt-0.5" />
                      <div>
                        <p className="font-medium">Writing Style Guide</p>
                        <p className="text-sm text-gray-500">Guidelines for academic writing and citations</p>
                        <Button variant="link" className="h-auto p-0 text-purple-600">Download PDF</Button>
                      </div>
                    </li>
                    <li className="flex items-start">
                      <FileText className="h-5 w-5 text-purple-600 mr-2 mt-0.5" />
                      <div>
                        <p className="font-medium">Presentation Skills Handbook</p>
                        <p className="text-sm text-gray-500">Tips and techniques for effective presentations</p>
                        <Button variant="link" className="h-auto p-0 text-purple-600">Download PDF</Button>
                      </div>
                    </li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default CourseOverview; 