import { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  Calendar as CalendarIcon, 
  ChevronLeft, 
  ChevronRight, 
  Clock, 
  MapPin, 
  Users,
  GraduationCap,
  BookOpen,
  Music,
  Trophy,
  Heart,
  Flag,
  Loader2,
  AlertCircle
} from "lucide-react";
import { Student } from "@/types/student";
import { useQuery } from "@tanstack/react-query";
import { getEvents } from "@/api/events";
import type { Event } from "@/api/events";
import { format, parse } from "date-fns";

interface EventCalendarProps {
  student: Student | null;
}

const EventCalendar = ({ student }: EventCalendarProps) => {
  const currentDate = new Date();
  const [currentMonth, setCurrentMonth] = useState<string>(format(currentDate, "MMMM"));
  const [currentYear, setCurrentYear] = useState<string>(format(currentDate, "yyyy"));
  const [view, setView] = useState<string>("month");
  const [filter, setFilter] = useState<string>("all");
  
  // Get the student's level ID, handling different data structures
  const getStudentLevelId = () => {
    if (student?.level?.id) {
      return student.level.id;
    } else if (student?.level_id) {
      return student.level_id;
    }
    return "";
  };

  const studentLevelId = getStudentLevelId();
  
  // Fetch events from Firestore
  const { data: events = [], isLoading, error } = useQuery({
    queryKey: ["events", studentLevelId],
    queryFn: getEvents,
  });
  
  if (!student) {
    return (
      <div className="p-6 text-center">
        <h2 className="text-2xl font-bold text-red-600">Student Information Not Found</h2>
        <p className="text-gray-600 mt-2">Your student profile could not be loaded. Please contact support.</p>
      </div>
    );
  }

  // Calendar data
  const months = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
  const years = [
    (parseInt(format(currentDate, "yyyy")) - 1).toString(),
    format(currentDate, "yyyy"),
    (parseInt(format(currentDate, "yyyy")) + 1).toString()
  ];
  
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', { 
        weekday: 'short',
        month: 'short', 
        day: 'numeric', 
        year: 'numeric' 
      });
    } catch (e) {
      return dateString;
    }
  };

  const getDaysInMonth = (month: number, year: number) => {
    return new Date(year, month + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (month: number, year: number) => {
    return new Date(year, month, 1).getDay();
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "exam":
        return <GraduationCap className="h-5 w-5 text-red-600" />;
      case "academic":
        return <BookOpen className="h-5 w-5 text-blue-600" />;
      case "meeting":
        return <Users className="h-5 w-5 text-purple-600" />;
      case "holiday":
        return <Flag className="h-5 w-5 text-green-600" />;
      case "cultural":
        return <Music className="h-5 w-5 text-pink-600" />;
      case "sports":
        return <Trophy className="h-5 w-5 text-orange-600" />;
      default:
        return <CalendarIcon className="h-5 w-5 text-gray-600" />;
    }
  };

  const getCategoryBadge = (category: string) => {
    switch (category) {
      case "exam":
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">Exam</Badge>;
      case "academic":
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Academic</Badge>;
      case "meeting":
        return <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">Meeting</Badge>;
      case "holiday":
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Holiday</Badge>;
      case "cultural":
        return <Badge variant="outline" className="bg-pink-50 text-pink-700 border-pink-200">Cultural</Badge>;
      case "sports":
        return <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">Sports</Badge>;
      default:
        return <Badge variant="outline">Other</Badge>;
    }
  };

  const filteredEvents = events.filter(event => {
    const eventDate = new Date(event.date);
    const isCurrentMonth = eventDate.getMonth() === months.indexOf(currentMonth) && 
                           eventDate.getFullYear().toString() === currentYear;
    
    const matchesFilter = filter === "all" || event.category === filter;
    
    return isCurrentMonth && matchesFilter;
  });

  // Sort events by date
  const sortedEvents = [...filteredEvents].sort((a, b) => {
    return new Date(a.date).getTime() - new Date(b.date).getTime();
  });

  // Group events by date for list view
  const eventsByDate: Record<string, Event[]> = {};
  sortedEvents.forEach(event => {
    if (!eventsByDate[event.date]) {
      eventsByDate[event.date] = [];
    }
    eventsByDate[event.date].push(event);
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 text-center">
        <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
        <h2 className="text-2xl font-bold text-red-600">Error Loading Events</h2>
        <p className="text-gray-600 mt-2">There was a problem loading the events. Please try again later.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">Event Calendar</h1>
        <p className="text-muted-foreground">View upcoming school events, exams, and important dates.</p>
      </div>

      <div className="flex flex-col md:flex-row justify-between gap-4">
        <div className="flex items-center space-x-2">
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => {
              const currentIndex = months.indexOf(currentMonth);
              if (currentIndex > 0) {
                setCurrentMonth(months[currentIndex - 1]);
              } else {
                setCurrentMonth(months[11]);
                setCurrentYear((parseInt(currentYear) - 1).toString());
              }
            }}
          >
            <ChevronLeft className="h-4 w-4 mr-1" />
            Previous
          </Button>
          <div className="flex space-x-2">
            <Select value={currentMonth} onValueChange={setCurrentMonth}>
              <SelectTrigger className="w-[120px]">
                <SelectValue placeholder="Month" />
              </SelectTrigger>
              <SelectContent>
                {months.map((month) => (
                  <SelectItem key={month} value={month}>{month}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={currentYear} onValueChange={setCurrentYear}>
              <SelectTrigger className="w-[100px]">
                <SelectValue placeholder="Year" />
              </SelectTrigger>
              <SelectContent>
                {years.map((year) => (
                  <SelectItem key={year} value={year}>{year}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => {
              const currentIndex = months.indexOf(currentMonth);
              if (currentIndex < 11) {
                setCurrentMonth(months[currentIndex + 1]);
              } else {
                setCurrentMonth(months[0]);
                setCurrentYear((parseInt(currentYear) + 1).toString());
              }
            }}
          >
            Next
            <ChevronRight className="h-4 w-4 ml-1" />
          </Button>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={filter} onValueChange={setFilter}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Filter Events" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Events</SelectItem>
              <SelectItem value="exam">Exams</SelectItem>
              <SelectItem value="academic">Academic</SelectItem>
              <SelectItem value="meeting">Meetings</SelectItem>
              <SelectItem value="holiday">Holidays</SelectItem>
              <SelectItem value="cultural">Cultural</SelectItem>
              <SelectItem value="sports">Sports</SelectItem>
            </SelectContent>
          </Select>
          <Tabs value={view} onValueChange={setView} className="w-[200px]">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="month">Month</TabsTrigger>
              <TabsTrigger value="list">List</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </div>

      {view === "month" ? (
        <Card>
          <CardHeader>
            <CardTitle>{currentMonth} {currentYear}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-7 gap-1 text-center">
              {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map((day) => (
                <div key={day} className="font-medium text-sm py-2">{day}</div>
              ))}
              
              {Array.from({ length: 42 }).map((_, index) => {
                const monthIndex = months.indexOf(currentMonth);
                const year = parseInt(currentYear);
                const daysInMonth = getDaysInMonth(monthIndex, year);
                const firstDay = getFirstDayOfMonth(monthIndex, year);
                
                const day = index - firstDay + 1;
                const isCurrentMonth = day > 0 && day <= daysInMonth;
                
                if (!isCurrentMonth) {
                  return <div key={index} className="h-24 p-1 border border-gray-100"></div>;
                }
                
                const date = `${year}-${(monthIndex + 1).toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
                const dayEvents = events.filter(event => event.date === date && (filter === "all" || event.category === filter));
                
                return (
                  <div 
                    key={index} 
                    className={`h-24 p-1 border border-gray-200 overflow-hidden ${
                      dayEvents.length > 0 ? 'bg-gray-50' : ''
                    }`}
                  >
                    <div className="text-sm font-medium mb-1">{day}</div>
                    <div className="space-y-1">
                      {dayEvents.slice(0, 3).map((event, i) => (
                        <div 
                          key={i} 
                          className={`text-xs p-1 rounded truncate ${
                            event.category === 'exam' ? 'bg-red-100 text-red-800' :
                            event.category === 'academic' ? 'bg-blue-100 text-blue-800' :
                            event.category === 'meeting' ? 'bg-purple-100 text-purple-800' :
                            event.category === 'holiday' ? 'bg-green-100 text-green-800' :
                            event.category === 'cultural' ? 'bg-pink-100 text-pink-800' :
                            event.category === 'sports' ? 'bg-orange-100 text-orange-800' :
                            'bg-gray-100 text-gray-800'
                          }`}
                        >
                          {event.title}
                        </div>
                      ))}
                      {dayEvents.length > 3 && (
                        <div className="text-xs text-gray-500 pl-1">
                          +{dayEvents.length - 3} more
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {Object.keys(eventsByDate).length === 0 ? (
            <Card>
              <CardContent className="p-6 text-center">
                <p className="text-gray-500">No events found for the selected month and filter.</p>
              </CardContent>
            </Card>
          ) : (
            Object.keys(eventsByDate).sort().map((date) => (
              <Card key={date}>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">{formatDate(date)}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {eventsByDate[date].map((event) => (
                      <div key={event.id} className="flex items-start p-3 border rounded-md">
                        <div className="flex items-center justify-center bg-gray-100 p-3 rounded-md mr-4">
                          {getCategoryIcon(event.category)}
                        </div>
                        <div className="flex-1">
                          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
                            <div>
                              <h3 className="font-medium">{event.title}</h3>
                              <div className="flex items-center gap-2 mt-1">
                                {getCategoryBadge(event.category)}
                              </div>
                            </div>
                            {event.allDay ? (
                              <Badge variant="outline">All Day</Badge>
                            ) : (
                              <div className="flex items-center text-sm text-gray-600">
                                <Clock className="h-4 w-4 mr-1" />
                                {event.startTime} - {event.endTime}
                              </div>
                            )}
                          </div>
                          {event.description && (
                            <p className="text-sm text-gray-600 mt-2">{event.description}</p>
                          )}
                          {event.location && (
                            <div className="flex items-center text-sm text-gray-600 mt-2">
                              <MapPin className="h-4 w-4 mr-1" />
                              {event.location}
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Upcoming Important Dates</CardTitle>
          <CardDescription>Key events and deadlines to remember</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {sortedEvents.length > 0 ? (
              sortedEvents.slice(0, 5).map((event) => (
                <div key={event.id} className="flex items-start">
                  <div className={`
                    ${event.category === 'exam' ? 'bg-red-100 text-red-800' : 
                      event.category === 'academic' ? 'bg-blue-100 text-blue-800' :
                      event.category === 'meeting' ? 'bg-purple-100 text-purple-800' :
                      event.category === 'holiday' ? 'bg-green-100 text-green-800' :
                      event.category === 'cultural' ? 'bg-pink-100 text-pink-800' :
                      event.category === 'sports' ? 'bg-orange-100 text-orange-800' :
                      'bg-gray-100 text-gray-800'
                    } p-2 rounded-md mr-4 text-center min-w-[60px]`}>
                    <div className="text-xs font-medium">{format(new Date(event.date), 'MMM').toUpperCase()}</div>
                    <div className="text-lg font-bold">{format(new Date(event.date), 'dd')}</div>
                  </div>
                  <div>
                    <h4 className="font-medium">{event.title}</h4>
                    <p className="text-sm text-gray-600">{event.description || 'No description available.'}</p>
                    {event.location && (
                      <div className="text-xs text-gray-500 flex items-center mt-1">
                        <MapPin className="h-3 w-3 mr-1" />
                        <span>{event.location}</span>
                      </div>
                    )}
                    {event.startTime && (
                      <div className="text-xs text-gray-500 flex items-center">
                        <Clock className="h-3 w-3 mr-1" />
                        <span>{event.startTime} {event.endTime ? `- ${event.endTime}` : ''}</span>
                      </div>
                    )}
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-2">
                <p className="text-sm text-gray-500">No upcoming events found</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default EventCalendar; 