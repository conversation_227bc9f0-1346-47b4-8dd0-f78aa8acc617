import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  MessageSquare, 
  Send, 
  Clock, 
  CheckCircle2, 
  XCircle, 
  AlertCircle,
  HelpCircle,
  ThumbsUp,
  Wrench,
  User,
  Calendar
} from "lucide-react";
import { Student } from "@/types/student";
import { capitalize } from '@/utils/string-utils';

interface FeedbackProps {
  student: Student | null;
}

const Feedback = ({ student }: FeedbackProps) => {
  const [activeTab, setActiveTab] = useState<string>("submit");
  const [feedbackType, setFeedbackType] = useState<string>("");
  const [subject, setSubject] = useState<string>("");
  const [message, setMessage] = useState<string>("");
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [isSubmitted, setIsSubmitted] = useState<boolean>(false);
  
  if (!student) {
    return (
      <div className="p-6 text-center">
        <h2 className="text-2xl font-bold text-red-600">Student Information Not Found</h2>
        <p className="text-gray-600 mt-2">Your student profile could not be loaded. Please contact support.</p>
      </div>
    );
  }

  // Mock data for previous feedback submissions
  const previousFeedback = [
    {
      id: "f1",
      type: "query",
      subject: "Course Registration",
      message: "I'm having trouble registering for the advanced grammar course. The system shows it's full, but I was told there are still spots available.",
      date: "2023-04-25",
      status: "resolved",
      response: "Thank you for your query. We've checked the system and there was an error in the capacity count. We've added you to the course. Please check your schedule.",
      responseDate: "2023-04-26"
    },
    {
      id: "f2",
      type: "technical",
      subject: "Dashboard Access Issue",
      message: "I'm unable to access the assignments section of my dashboard. It shows an error message saying 'Access Denied'.",
      date: "2023-04-18",
      status: "resolved",
      response: "We've fixed the permission issue with your account. Please try accessing the assignments section again. If you still face issues, please let us know.",
      responseDate: "2023-04-19"
    },
    {
      id: "f3",
      type: "feedback",
      subject: "Suggestion for Library Resources",
      message: "I would like to suggest adding more advanced reading materials for vocabulary building. The current resources are good for beginners but limited for intermediate and advanced levels.",
      date: "2023-04-10",
      status: "pending",
      response: "",
      responseDate: ""
    },
    {
      id: "f4",
      type: "query",
      subject: "Exam Schedule Clarification",
      message: "Is the listening exam on May 19 in Room 203 or Room 204? I've seen different information in different places.",
      date: "2023-05-01",
      status: "in_progress",
      response: "",
      responseDate: ""
    }
  ];

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', { 
        year: 'numeric',
        month: 'short', 
        day: 'numeric'
      });
    } catch (e) {
      return dateString;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "resolved":
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Resolved</Badge>;
      case "in_progress":
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">In Progress</Badge>;
      case "pending":
        return <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">Pending</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "query":
        return <HelpCircle className="h-5 w-5 text-blue-600" />;
      case "feedback":
        return <ThumbsUp className="h-5 w-5 text-green-600" />;
      case "technical":
        return <Wrench className="h-5 w-5 text-amber-600" />;
      default:
        return <MessageSquare className="h-5 w-5 text-gray-600" />;
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsSubmitting(false);
      setIsSubmitted(true);
      
      // Reset form after 3 seconds
      setTimeout(() => {
        setIsSubmitted(false);
        setFeedbackType("");
        setSubject("");
        setMessage("");
        setActiveTab("history");
      }, 3000);
    }, 1500);
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">Feedback & Support</h1>
        <p className="text-muted-foreground">Submit queries, feedback, and technical support requests.</p>
      </div>

      <Tabs defaultValue="submit" className="space-y-4" value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="submit">Submit New</TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
          <TabsTrigger value="faq">FAQ</TabsTrigger>
        </TabsList>

        <TabsContent value="submit">
          <Card>
            <CardHeader>
              <CardTitle>Submit Feedback or Query</CardTitle>
              <CardDescription>We value your feedback and are here to help with any questions.</CardDescription>
            </CardHeader>
            <CardContent>
              {isSubmitted ? (
                <div className="flex flex-col items-center justify-center py-8">
                  <div className="bg-green-100 p-3 rounded-full mb-4">
                    <CheckCircle2 className="h-8 w-8 text-green-600" />
                  </div>
                  <h3 className="text-xl font-medium text-green-700">Submission Successful</h3>
                  <p className="text-gray-600 mt-2 text-center">
                    Thank you for your feedback. We'll get back to you as soon as possible.
                  </p>
                </div>
              ) : (
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="feedback-type">Type of Feedback</Label>
                    <Select value={feedbackType} onValueChange={setFeedbackType} required>
                      <SelectTrigger id="feedback-type">
                        <SelectValue placeholder="Select type of feedback" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="query">Question/Query</SelectItem>
                        <SelectItem value="feedback">Suggestion/Feedback</SelectItem>
                        <SelectItem value="technical">Technical Support</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="subject">Subject</Label>
                    <Input 
                      id="subject" 
                      placeholder="Brief subject of your feedback" 
                      value={subject}
                      onChange={(e) => setSubject(e.target.value)}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="message">Message</Label>
                    <Textarea 
                      id="message" 
                      placeholder="Please provide details of your feedback, query, or issue..." 
                      rows={5}
                      value={message}
                      onChange={(e) => setMessage(e.target.value)}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="attachment">Attachment (Optional)</Label>
                    <Input id="attachment" type="file" />
                    <p className="text-xs text-gray-500">
                      You can attach screenshots or documents to help explain your feedback or issue.
                    </p>
                  </div>
                </form>
              )}
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" onClick={() => setActiveTab("history")}>
                View Previous Submissions
              </Button>
              {!isSubmitted && (
                <Button 
                  type="submit" 
                  onClick={handleSubmit}
                  disabled={isSubmitting || !feedbackType || !subject || !message}
                >
                  {isSubmitting ? (
                    <>
                      <span className="animate-spin mr-2">
                        <Clock className="h-4 w-4" />
                      </span>
                      Submitting...
                    </>
                  ) : (
                    <>
                      <Send className="h-4 w-4 mr-2" />
                      Submit Feedback
                    </>
                  )}
                </Button>
              )}
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="history">
          <Card>
            <CardHeader>
              <CardTitle>Previous Submissions</CardTitle>
              <CardDescription>Track the status of your previous feedback and queries.</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {previousFeedback.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    You haven't submitted any feedback or queries yet.
                  </div>
                ) : (
                  previousFeedback.map((feedback) => (
                    <Card key={feedback.id} className="overflow-hidden">
                      <CardContent className="p-6">
                        <div className="flex flex-col md:flex-row gap-4">
                          <div className="flex items-center justify-center bg-gray-100 p-4 rounded-md min-w-[60px] h-[60px]">
                            {getTypeIcon(feedback.type)}
                          </div>
                          <div className="flex-1">
                            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
                              <div>
                                <h3 className="font-medium text-lg">{feedback.subject}</h3>
                                <div className="flex items-center gap-2 mt-1">
                                  <Badge variant="outline" className={`
                                    ${feedback.type === 'academic' ? 'bg-blue-50 text-blue-700 border-blue-200' : 
                                      feedback.type === 'administrative' ? 'bg-purple-50 text-purple-700 border-purple-200' : 
                                      feedback.type === 'facility' ? 'bg-amber-50 text-amber-700 border-amber-200' : 
                                      'bg-green-50 text-green-700 border-green-200'}
                                  `}>
                                    {capitalize(feedback.type)}
                                  </Badge>
                                  {getStatusBadge(feedback.status)}
                                </div>
                              </div>
                              <div className="flex items-center text-sm text-gray-600">
                                <Calendar className="h-4 w-4 mr-1" />
                                <span>Submitted: {formatDate(feedback.date)}</span>
                              </div>
                            </div>
                            <p className="text-sm text-gray-600 mt-3">{feedback.message}</p>
                            
                            {feedback.response && (
                              <div className="mt-4 bg-gray-50 p-4 rounded-md">
                                <div className="flex items-center gap-2 mb-2">
                                  <User className="h-4 w-4 text-blue-600" />
                                  <span className="font-medium text-sm">Response from Support</span>
                                  <span className="text-xs text-gray-500">
                                    ({formatDate(feedback.responseDate)})
                                  </span>
                                </div>
                                <p className="text-sm text-gray-600">{feedback.response}</p>
                              </div>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="faq">
          <Card>
            <CardHeader>
              <CardTitle>Frequently Asked Questions</CardTitle>
              <CardDescription>Find answers to common questions.</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="border rounded-md overflow-hidden">
                  <div className="p-4 border-b bg-gray-50">
                    <h3 className="font-medium">How do I reset my password?</h3>
                  </div>
                  <div className="p-4">
                    <p className="text-sm text-gray-600">
                      You can reset your password by clicking on the "Forgot Password" link on the login page. 
                      Alternatively, you can contact the IT support desk for assistance.
                    </p>
                  </div>
                </div>

                <div className="border rounded-md overflow-hidden">
                  <div className="p-4 border-b bg-gray-50">
                    <h3 className="font-medium">How can I check my attendance record?</h3>
                  </div>
                  <div className="p-4">
                    <p className="text-sm text-gray-600">
                      You can view your attendance record in the Attendance section of your dashboard. 
                      It shows your overall attendance percentage, subject-wise attendance, and daily records.
                    </p>
                  </div>
                </div>

                <div className="border rounded-md overflow-hidden">
                  <div className="p-4 border-b bg-gray-50">
                    <h3 className="font-medium">When will I receive my exam results?</h3>
                  </div>
                  <div className="p-4">
                    <p className="text-sm text-gray-600">
                      Exam results are typically released within two weeks after the examination period ends. 
                      You will receive a notification when your results are available in the Exams & Results section.
                    </p>
                  </div>
                </div>

                <div className="border rounded-md overflow-hidden">
                  <div className="p-4 border-b bg-gray-50">
                    <h3 className="font-medium">How do I submit assignments online?</h3>
                  </div>
                  <div className="p-4">
                    <p className="text-sm text-gray-600">
                      Go to the Assignments section of your dashboard, select the assignment you want to submit, 
                      upload your file, add any comments if needed, and click the Submit button. Make sure to submit before the deadline.
                    </p>
                  </div>
                </div>

                <div className="border rounded-md overflow-hidden">
                  <div className="p-4 border-b bg-gray-50">
                    <h3 className="font-medium">Who should I contact for technical issues with the dashboard?</h3>
                  </div>
                  <div className="p-4">
                    <p className="text-sm text-gray-600">
                      For technical issues, you can submit a support request through this Feedback & Support section. 
                      Select "Technical Support" as the type of feedback. Our IT team will assist you as soon as possible.
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <Card>
        <CardHeader>
          <CardTitle>Contact Information</CardTitle>
          <CardDescription>Other ways to reach out to us</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center p-3 border rounded-md">
              <div className="bg-blue-100 p-2 rounded-md mr-4">
                <MessageSquare className="h-5 w-5 text-blue-600" />
              </div>
              <div className="flex-1">
                <h4 className="font-medium">Academic Support</h4>
                <p className="text-sm text-gray-600"><EMAIL></p>
              </div>
            </div>
            <div className="flex items-center p-3 border rounded-md">
              <div className="bg-green-100 p-2 rounded-md mr-4">
                <Wrench className="h-5 w-5 text-green-600" />
              </div>
              <div className="flex-1">
                <h4 className="font-medium">Technical Support</h4>
                <p className="text-sm text-gray-600"><EMAIL></p>
              </div>
            </div>
            <div className="flex items-center p-3 border rounded-md">
              <div className="bg-purple-100 p-2 rounded-md mr-4">
                <User className="h-5 w-5 text-purple-600" />
              </div>
              <div className="flex-1">
                <h4 className="font-medium">Student Services</h4>
                <p className="text-sm text-gray-600"><EMAIL></p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Feedback; 