import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { Student } from "@/types/student";
import { getStudentGrades, getStudentGradeStats, GradeItem } from "@/api/student-grades";
import { 
  FileText, 
  BookOpen, 
  Calendar, 
  BarChart, 
  TrendingUp, 
  TrendingDown, 
  Award, 
  Loader2,
  AlertCircle,
  Search
} from "lucide-react";

interface GradesProps {
  student: Student | null;
}

interface GradeStats {
  average: number;
  highest: number;
  lowest: number;
  totalItems: number;
  assignmentAverage: number;
  examAverage: number;
  recentGrades: GradeItem[];
}

const Grades = ({ student }: GradesProps) => {
  const [filter, setFilter] = useState<string>("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState<string>("date");

  // Fetch grades data
  const { 
    data: grades = [], 
    isLoading: isLoadingGrades, 
    error: gradesError 
  } = useQuery({
    queryKey: ['studentGrades', student?.id],
    queryFn: () => student?.id ? getStudentGrades(student.id) : Promise.resolve([]),
    enabled: !!student?.id
  });

  // Fetch grade statistics
  const { 
    data: stats, 
    isLoading: isLoadingStats, 
    error: statsError 
  } = useQuery<GradeStats | null>({
    queryKey: ['studentGradeStats', student?.id],
    queryFn: () => student?.id ? getStudentGradeStats(student.id) : Promise.resolve(null),
    enabled: !!student?.id
  });

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric', 
        year: 'numeric' 
      });
    } catch (error) {
      console.error("Error formatting date:", error);
      return "Invalid date";
    }
  };

  // Get grade badge based on percentage
  const getGradeBadge = (percentage: number) => {
    if (percentage >= 90) {
      return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">A ({percentage.toFixed(1)}%)</Badge>;
    } else if (percentage >= 80) {
      return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">B ({percentage.toFixed(1)}%)</Badge>;
    } else if (percentage >= 70) {
      return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">C ({percentage.toFixed(1)}%)</Badge>;
    } else if (percentage >= 60) {
      return <Badge className="bg-orange-100 text-orange-800 hover:bg-orange-100">D ({percentage.toFixed(1)}%)</Badge>;
    } else {
      return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">F ({percentage.toFixed(1)}%)</Badge>;
    }
  };

  // Get progress bar color based on percentage
  const getProgressColor = (percentage: number) => {
    if (percentage >= 80) return "bg-primary text-green-500";
    if (percentage >= 70) return "bg-blue-500";
    if (percentage >= 60) return "bg-yellow-500";
    return "bg-red-500";
  };

  // Filter and sort grades
  const filteredGrades = grades.filter(grade => {
    const matchesFilter = 
      filter === "all" || 
      (filter === "assignments" && grade.type === "assignment") ||
      (filter === "exams" && grade.type === "exam");
    
    const matchesSearch = 
      grade.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (grade.course_name && grade.course_name.toLowerCase().includes(searchTerm.toLowerCase()));
    
    return matchesFilter && matchesSearch;
  }).sort((a, b) => {
    if (sortBy === "date") {
      return new Date(b.date).getTime() - new Date(a.date).getTime();
    } else if (sortBy === "title") {
      return a.title.localeCompare(b.title);
    } else if (sortBy === "grade-high") {
      return b.percentage - a.percentage;
    } else if (sortBy === "grade-low") {
      return a.percentage - b.percentage;
    }
    return 0;
  });

  if (isLoadingGrades || isLoadingStats) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (gradesError || statsError) {
    return (
      <div className="p-4 bg-red-50 text-red-700 rounded-md">
        <div className="flex items-center">
          <AlertCircle className="h-5 w-5 mr-2" />
          <p className="font-semibold">Error loading grades</p>
        </div>
        <p className="mt-2">{((gradesError || statsError) as Error)?.message || "An unknown error occurred"}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold tracking-tight">Grades & Performance</h2>
        <p className="text-muted-foreground">
          View your grades and academic performance
        </p>
      </div>

      {/* Grade Statistics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Overall Average</CardTitle>
            <BarChart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.average || 0}%</div>
            <Progress 
              value={stats?.average || 0} 
              className={`h-2 mt-2 ${getProgressColor(stats?.average || 0)}`}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Assignment Average</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.assignmentAverage || 0}%</div>
            <Progress 
              value={stats?.assignmentAverage || 0} 
              className={`h-2 mt-2 ${getProgressColor(stats?.assignmentAverage || 0)}`}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Exam Average</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.examAverage || 0}%</div>
            <Progress 
              value={stats?.examAverage || 0} 
              className={`h-2 mt-2 ${getProgressColor(stats?.examAverage || 0)}`}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Graded Items</CardTitle>
            <Award className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.totalItems || 0}</div>
            <div className="flex justify-between mt-2 text-xs text-muted-foreground">
              <div className="flex items-center">
                <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
                <span>High: {stats?.highest ? stats.highest.toFixed(2) : 0}%</span>
              </div>
              <div className="flex items-center">
                <TrendingDown className="h-3 w-3 mr-1 text-red-500" />
                <span>Low: {stats?.lowest ? stats.lowest.toFixed(2) : 0}%</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Grade List */}
      <Card>
        <CardHeader>
          <CardTitle>Grade History</CardTitle>
          <CardDescription>
            View all your graded assignments and exams
          </CardDescription>
          <div className="flex flex-col sm:flex-row gap-4 mt-4">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search grades..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <div className="flex gap-2">
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="date">Date (Newest)</SelectItem>
                  <SelectItem value="title">Title (A-Z)</SelectItem>
                  <SelectItem value="grade-high">Grade (Highest)</SelectItem>
                  <SelectItem value="grade-low">Grade (Lowest)</SelectItem>
                </SelectContent>
              </Select>
              <Tabs defaultValue="all" value={filter} onValueChange={setFilter} className="w-[300px]">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="all">All</TabsTrigger>
                  <TabsTrigger value="assignments">Assignments</TabsTrigger>
                  <TabsTrigger value="exams">Exams</TabsTrigger>
                </TabsList>
              </Tabs>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {filteredGrades.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <Award className="h-12 w-12 text-muted-foreground mb-4" />
              <p className="text-muted-foreground">No grades found</p>
              <p className="text-sm text-muted-foreground mt-1">
                {filter !== "all" 
                  ? `No ${filter} found. Try changing the filter.` 
                  : searchTerm 
                    ? "No grades match your search. Try a different search term." 
                    : "You don't have any graded items yet."}
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredGrades.map((grade) => (
                <div key={grade.id} className="p-4 border rounded-lg">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
                    <div>
                      <div className="flex items-center gap-2">
                        {grade.type === "assignment" ? (
                          <FileText className="h-5 w-5 text-blue-500" />
                        ) : (
                          <BookOpen className="h-5 w-5 text-purple-500" />
                        )}
                        <h3 className="font-medium">{grade.title}</h3>
                        <Badge variant="outline">
                          {grade.type === "assignment" ? "Assignment" : "Exam"}
                        </Badge>
                      </div>
                      {grade.course_name && (
                        <p className="text-sm text-muted-foreground mt-1">
                          Course: {grade.course_name}
                        </p>
                      )}
                    </div>
                    <div className="flex flex-col md:flex-row md:items-center gap-2 md:gap-4">
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-1 text-muted-foreground" />
                        <span className="text-sm">{formatDate(grade.date)}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium">
                          {grade.grade} / {grade.max_points}
                        </span>
                        {getGradeBadge(grade.percentage)}
                      </div>
                    </div>
                  </div>
                  
                  <Progress 
                    value={grade.percentage} 
                    className={`h-2 mt-3 ${getProgressColor(grade.percentage)}`}
                  />
                  
                  {grade.feedback && (
                    <div className="mt-3">
                      <Separator className="my-2" />
                      <p className="text-sm font-medium">Feedback:</p>
                      <p className="text-sm mt-1">{grade.feedback}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default Grades; 