import { useState, useEffect } from "react";
import { useNavigate, Routes, Route } from "react-router-dom";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { 
  Calendar, 
  Clock, 
  MapPin, 
  BarChart2, 
  TrendingUp, 
  Award, 
  BookOpen,
  ArrowLeft,
  ChevronRight,
  Download,
  Loader2,
  AlertCircle
} from "lucide-react";
import { Student } from "@/types/student";
import { apiClient } from "@/lib/api-client";
// Exam timetable types now defined locally
interface ExamTimetableEntry {
  id: string;
  subject: string;
  date: string;
  time: string;
  duration: string;
  room?: string;
}

interface ExamsProps {
  student: Student | null;
}

interface UpcomingExam {
  id: string;
  subject: string;
  date: string; // ISO date string
  startTime: string;
  endTime: string;
  room: string;
  type: string;
  teacher: string;
}

interface PastExam {
  id: string;
  subject: string;
  date: Timestamp;
  type: string;
  totalMarks: number;
  obtainedMarks: number;
  grade: string;
  feedback?: string;
}

interface PerformanceData {
  overall: {
    average: number;
    highest: number;
    lowest: number;
  };
  subjects: {
    name: string;
    scores: number[];
    average: number;
  }[];
  terms: {
    name: string;
    average: number;
  }[];
}

const ExamsList = ({ student }: ExamsProps) => {
  const navigate = useNavigate();
  const [upcomingExams, setUpcomingExams] = useState<UpcomingExam[]>([]);
  const [pastExams, setPastExams] = useState<PastExam[]>([]);
  const [performanceData, setPerformanceData] = useState<PerformanceData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    const fetchExamsData = async () => {
      if (!student?.id) {
        setLoading(false);
        setError('Student information not available');
        return;
      }

      try {
        setLoading(true);
        
        // Get the student's level ID to fetch relevant exams
        const studentLevelId = student.level?.id || student.level_id;
        
        if (!studentLevelId) {
          console.error('No level ID associated with student');
          setError('Could not determine your level/class to fetch exam timetable');
          setLoading(false);
          return;
        }

        console.log('Fetching exam timetable for level:', studentLevelId);
        
        // Fetch exams from exam_timetable collection
        const examTimetableEntries = await timetableModel.getExamTimetableByLevel(studentLevelId);
        console.log('Found exam timetable entries:', examTimetableEntries.length);
        
        // Filter to get only upcoming exams (where examDate is in the future)
        const today = new Date().toISOString().split('T')[0]; // Get today's date in YYYY-MM-DD format
        
        const upcomingExamsData = examTimetableEntries
          .filter(entry => entry.examDate >= today)
          .map(entry => ({
            id: entry.id,
            subject: entry.subject,
            date: entry.examDate,
            startTime: entry.startTime,
            endTime: entry.endTime,
            room: entry.room,
            type: entry.examType,
            teacher: entry.teacher
          }))
          .sort((a, b) => a.date.localeCompare(b.date));
        
        setUpcomingExams(upcomingExamsData);
        
        // Check if exam_results collection exists for past exams
        // This part is unchanged as we're still using exam_results for past exams
        const resultsCollectionRef = collection(db, 'exam_results');
        const testResultsQuery = query(resultsCollectionRef, limit(1));
        const testResultsSnapshot = await getDocs(testResultsQuery);
        
        let pastExamsData: PastExam[] = [];
        
        if (!testResultsSnapshot.empty) {
          // Fetch past exams
          console.log('Fetching past exams for student:', student.id);
          const pastExamsQuery = query(
            resultsCollectionRef,
            where('studentId', '==', student.id),
            orderBy('date', 'desc')
          );
          
          const pastExamsSnapshot = await getDocs(pastExamsQuery);
          console.log('Found past exams:', pastExamsSnapshot.size);
          
          pastExamsData = pastExamsSnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          } as PastExam));
        } else {
          console.log('Exam results collection is empty or does not exist');
        }
        
        setPastExams(pastExamsData);
        
        // Calculate performance data
        if (pastExamsData.length > 0) {
          // Calculate overall stats
          const scores = pastExamsData.map(exam => 
            Math.round((exam.obtainedMarks / exam.totalMarks) * 100)
          );
          
          const overall = {
            average: Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length),
            highest: Math.max(...scores),
            lowest: Math.min(...scores)
          };
          
          // Group by subjects
          const subjectMap = new Map<string, number[]>();
          pastExamsData.forEach(exam => {
            if (!subjectMap.has(exam.subject)) {
              subjectMap.set(exam.subject, []);
            }
            subjectMap.get(exam.subject)?.push(
              Math.round((exam.obtainedMarks / exam.totalMarks) * 100)
            );
          });
          
          const subjects = Array.from(subjectMap.entries()).map(([name, scores]) => ({
            name,
            scores,
            average: Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length)
          }));
          
          // Group by terms (assuming term info is in the exam type)
          const termMap = new Map<string, number[]>();
          pastExamsData.forEach(exam => {
            const term = exam.type.includes('Final') ? 'Term Final' : 
                        exam.type.includes('Mid') ? 'Term Mid' : 'Quizzes';
            
            if (!termMap.has(term)) {
              termMap.set(term, []);
            }
            termMap.get(term)?.push(
              Math.round((exam.obtainedMarks / exam.totalMarks) * 100)
            );
          });
          
          const terms = Array.from(termMap.entries()).map(([name, scores]) => ({
            name,
            average: Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length)
          }));
          
          setPerformanceData({
            overall,
            subjects,
            terms
          });
        } else {
          setPerformanceData(null);
        }
        
        if (upcomingExamsData.length === 0 && pastExamsData.length === 0) {
          setError('No exam data found for your account');
        } else {
          setError(null);
        }
      } catch (err) {
        console.error('Error fetching exams data:', err);
        setError('Failed to load exams data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchExamsData();
  }, [student?.id, student?.level?.id, student?.level_id]);
  
  // Modified formatDate function to work with ISO date strings
  const formatDate = (dateStr: string | Timestamp) => {
    try {
      let date: Date;
      
      if (dateStr instanceof Timestamp) {
        date = dateStr.toDate();
      } else {
        date = new Date(dateStr);
      }
      
      return date.toLocaleDateString('en-US', { 
        weekday: 'short',
        month: 'short', 
        day: 'numeric', 
        year: 'numeric' 
      });
    } catch (e) {
      return 'Invalid date';
    }
  };

  // Modified getDaysRemaining function to work with ISO date strings
  const getDaysRemaining = (dateStr: string | Timestamp) => {
    const today = new Date();
    let examDate: Date;
    
    if (dateStr instanceof Timestamp) {
      examDate = dateStr.toDate();
    } else {
      examDate = new Date(dateStr);
    }
    
    const diffTime = examDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) {
      return "Past";
    } else if (diffDays === 0) {
      return "Today";
    } else if (diffDays === 1) {
      return "Tomorrow";
    } else {
      return `${diffDays} days left`;
    }
  };

  const getGradeColor = (grade: string) => {
    switch (grade) {
      case "A+":
      case "A":
        return "text-green-600";
      case "B+":
      case "B":
        return "text-blue-600";
      case "C+":
      case "C":
        return "text-yellow-600";
      default:
        return "text-red-600";
    }
  };

  const calculatePercentage = (obtained: number, total: number) => {
    return Math.round((obtained / total) * 100);
  };

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p className="mt-4 text-muted-foreground">Loading exams data...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <AlertCircle className="h-8 w-8 text-red-500" />
        <p className="mt-4 text-red-500">{error}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">Exams & Results</h1>
        <p className="text-muted-foreground">View upcoming exams, check grades, and track your academic performance.</p>
      </div>

      <Tabs defaultValue="upcoming" className="space-y-4">
        <TabsList>
          <TabsTrigger value="upcoming">Upcoming Exams</TabsTrigger>
          <TabsTrigger value="results">Exam Results</TabsTrigger>
          <TabsTrigger value="performance">Performance Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="upcoming">
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">Scheduled Examinations</h2>
            {upcomingExams.length === 0 ? (
              <Card>
                <CardContent className="p-6 text-center">
                  <div className="flex flex-col items-center justify-center text-gray-500">
                    <Calendar className="h-12 w-12 mb-2 text-gray-400" />
                    <p>No upcoming exams scheduled.</p>
                  </div>
                </CardContent>
              </Card>
            ) : (
              upcomingExams.map((exam) => (
                <Card key={exam.id} className="overflow-hidden">
                  <CardContent className="p-4">
                    <div className="flex flex-col md:flex-row gap-4">
                      <div className="bg-blue-100 text-blue-800 p-3 rounded-md text-center min-w-[100px] md:min-w-[120px] flex flex-col justify-center">
                        <div className="text-xs font-medium">
                          {new Date(exam.date).toLocaleDateString('en-US', { month: 'short' })}
                        </div>
                        <div className="text-2xl font-bold">
                          {new Date(exam.date).getDate()}
                        </div>
                        <div className="text-xs">
                          {new Date(exam.date).toLocaleDateString('en-US', { weekday: 'short' })}
                        </div>
                        <div className="mt-2 text-xs font-medium">
                          {getDaysRemaining(exam.date)}
                        </div>
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex flex-col md:flex-row md:items-start md:justify-between gap-2">
                          <div>
                            <h3 className="font-medium text-lg">{exam.subject}</h3>
                            <p className="text-sm text-gray-600">{exam.type}</p>
                          </div>
                          <div>
                            <Badge variant="outline" className="w-fit">
                              Teacher: {exam.teacher}
                            </Badge>
                          </div>
                        </div>
                        
                        <div className="mt-3 flex flex-col sm:flex-row gap-3">
                          <div className="flex items-center text-sm text-gray-600">
                            <Clock className="h-4 w-4 mr-1 text-gray-500" />
                            {exam.startTime} - {exam.endTime}
                          </div>
                          <div className="flex items-center text-sm text-gray-600">
                            <MapPin className="h-4 w-4 mr-1 text-gray-500" />
                            Room {exam.room}
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </TabsContent>

        <TabsContent value="results">
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">Past Examination Results</h2>
            {pastExams.length === 0 ? (
              <Card>
                <CardContent className="p-6 text-center">
                  <p className="text-muted-foreground">No exam results available yet.</p>
                </CardContent>
              </Card>
            ) : (
              pastExams.map((exam) => (
                <Card key={exam.id} className="overflow-hidden">
                  <CardContent className="p-6">
                    <div className="flex flex-col md:flex-row gap-4">
                      <div className="bg-gray-100 p-3 rounded-md text-center min-w-[100px] md:min-w-[120px] flex flex-col justify-center">
                        <div className="text-xs font-medium">
                          {exam.date.toDate().toLocaleDateString('en-US', { month: 'short' })}
                        </div>
                        <div className="text-2xl font-bold">
                          {exam.date.toDate().getDate()}
                        </div>
                        <div className="text-xs">
                          {exam.date.toDate().toLocaleDateString('en-US', { weekday: 'short' })}
                        </div>
                        <div className={`mt-2 text-lg font-bold ${getGradeColor(exam.grade)}`}>
                          {exam.grade}
                        </div>
                      </div>
                      <div className="flex-1">
                        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
                          <div>
                            <h3 className="font-medium text-lg">{exam.subject}</h3>
                            <p className="text-sm text-gray-600">{exam.type}</p>
                          </div>
                          <div className="text-sm">
                            <span className="font-medium">{exam.obtainedMarks}</span>
                            <span className="text-gray-500">/{exam.totalMarks}</span>
                            <span className="ml-2 text-gray-500">
                              ({calculatePercentage(exam.obtainedMarks, exam.totalMarks)}%)
                            </span>
                          </div>
                        </div>
                        <div className="mt-2">
                          <Progress 
                            value={calculatePercentage(exam.obtainedMarks, exam.totalMarks)} 
                            className="h-2" 
                          />
                        </div>
                        {exam.feedback && (
                          <div className="mt-3 text-sm text-gray-600 border-t pt-2">
                            <p className="font-medium">Feedback:</p>
                            <p>{exam.feedback}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </TabsContent>

        <TabsContent value="performance">
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">Performance Analytics</h2>
            
            {!performanceData ? (
              <Card>
                <CardContent className="p-6 text-center">
                  <p className="text-muted-foreground">Not enough exam data to generate performance analytics.</p>
                </CardContent>
              </Card>
            ) : (
              <>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Average Score</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-3xl font-bold">{performanceData.overall.average}%</div>
                      <Progress value={performanceData.overall.average} className="h-2 mt-2" />
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Highest Score</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-3xl font-bold text-green-600">{performanceData.overall.highest}%</div>
                      <Progress value={performanceData.overall.highest} className="h-2 mt-2 bg-green-100" />
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Lowest Score</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-3xl font-bold text-amber-600">{performanceData.overall.lowest}%</div>
                      <Progress value={performanceData.overall.lowest} className="h-2 mt-2 bg-amber-100" />
                    </CardContent>
                  </Card>
                </div>
                
                <Card>
                  <CardHeader>
                    <CardTitle>Subject Performance</CardTitle>
                    <CardDescription>Your performance across different subjects</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {performanceData.subjects.map((subject, index) => (
                        <div key={index}>
                          <div className="flex items-center justify-between mb-1">
                            <span className="text-sm font-medium">{subject.name}</span>
                            <span className="text-sm font-medium">{subject.average}%</span>
                          </div>
                          <Progress value={subject.average} className="h-2" />
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle>Term Performance</CardTitle>
                    <CardDescription>Your performance across different terms</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      {performanceData.terms.map((term, index) => (
                        <Card key={index} className="border">
                          <CardContent className="p-4 text-center">
                            <h3 className="font-medium">{term.name}</h3>
                            <div className="text-2xl font-bold mt-2">{term.average}%</div>
                            <Progress value={term.average} className="h-2 mt-2" />
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

const Exams = ({ student }: ExamsProps) => {
  return (
    <div>
      <Routes>
        <Route index element={<ExamsList student={student} />} />
      </Routes>
    </div>
  );
};

export default Exams; 