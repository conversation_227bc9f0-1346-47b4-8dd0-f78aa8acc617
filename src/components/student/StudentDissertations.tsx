import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { 
  Plus, 
  FileText, 
  Search, 
  Calendar, 
  User, 
  Clock,
  Eye,
  Edit,
  MoreVertical,
  CheckCircle,
  AlertCircle,
  XCircle,
  Download,
  Archive,
  Star,
  BookOpen,
  Trash2
} from 'lucide-react';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { DissertationDocument } from '@/types/dissertation';
import { useAuth } from '@/contexts/AuthContext';
import { useDissertations } from '@/hooks/useDissertations';
import { formatDistanceToNow, format } from 'date-fns';
import { toast } from 'sonner';

interface StudentDissertationsProps {
  student: any;
}

const StudentDissertations: React.FC<StudentDissertationsProps> = ({ student }) => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { 
    dissertations, 
    loading, 
    error, 
    createDissertation, 
    deleteDissertation,
    loadUserDissertations,
    loadDissertations
  } = useDissertations();
  
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('current');
  const [creatingDissertation, setCreatingDissertation] = useState(false);

  const currentDissertations = dissertations.filter(d => 
    d.status === 'draft' || d.status === 'under_review' || d.status === 'ready_for_review' || d.status === 'needs_revision'
  );

  const pastDissertations = dissertations.filter(d => d.status === 'approved');

  const filteredCurrentDissertations = currentDissertations.filter(dissertation =>
    dissertation.title.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredPastDissertations = pastDissertations.filter(dissertation =>
    dissertation.title.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleCreateNew = async () => {
    if (!user?.uid) {
      toast.error('You must be logged in to create a dissertation');
      return;
    }

    setCreatingDissertation(true);
    try {
      const title = `Untitled Dissertation - ${format(new Date(), 'MMM dd, yyyy')}`;
      const dissertationId = await createDissertation(title);
      navigate(`/dissertation/${dissertationId}`);
    } catch (error) {
      console.error('Error creating dissertation:', error);
      toast.error('Failed to create dissertation');
    } finally {
      setCreatingDissertation(false);
    }
  };

  const handleOpenDissertation = (dissertationId: string) => {
    navigate(`/dissertation/${dissertationId}`);
  };

  const handleDeleteDissertation = async (dissertationId: string, title: string) => {
    try {
      console.log('Deleting dissertation:', dissertationId);
      await deleteDissertation(dissertationId);
      toast.success(`"${title}" has been deleted successfully`);
      // Force reload dissertations
      if (user?.uid) {
        await loadDissertations();
      }
    } catch (error) {
      console.error('Error deleting dissertation:', error);
      toast.error(`Failed to delete dissertation: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const handleDuplicateDissertation = async (dissertation: DissertationDocument) => {
    try {
      const newTitle = `Copy of ${dissertation.title}`;
      const newDissertationId = await createDissertation(newTitle);
      
      // TODO: Copy content from original dissertation
      // This would require updating the dissertation content after creation
      
      toast.success('Dissertation duplicated successfully');
      navigate(`/dissertation/${newDissertationId}`);
    } catch (error) {
      console.error('Error duplicating dissertation:', error);
      toast.error('Failed to duplicate dissertation');
    }
  };

  const getStatusColor = (status: DissertationDocument['status']) => {
    switch (status) {
      case 'draft': return 'bg-gray-100 text-gray-800';
      case 'ready_for_review': return 'bg-blue-100 text-blue-800';
      case 'under_review': return 'bg-yellow-100 text-yellow-800';
      case 'approved': return 'bg-green-100 text-green-800';
      case 'needs_revision': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: DissertationDocument['status']) => {
    switch (status) {
      case 'approved': return <CheckCircle className="w-4 h-4" />;
      case 'needs_revision': return <XCircle className="w-4 h-4" />;
      case 'under_review': return <AlertCircle className="w-4 h-4" />;
      default: return <FileText className="w-4 h-4" />;
    }
  };

  if (loading && dissertations.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error && dissertations.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64 space-y-4">
        <XCircle className="w-12 h-12 text-red-500" />
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900">Error Loading Dissertations</h3>
          <p className="text-gray-600 mt-1">{error}</p>
          <Button 
            onClick={loadDissertations} 
            className="mt-4"
            variant="outline"
          >
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">My Dissertations</h1>
          <p className="text-gray-600 mt-1">Manage your dissertation projects and track progress</p>
        </div>
        <Button 
          onClick={handleCreateNew} 
          className="bg-blue-600 hover:bg-blue-700"
          disabled={creatingDissertation}
        >
          <Plus className="w-4 h-4 mr-2" />
          {creatingDissertation ? 'Creating...' : 'New Dissertation'}
        </Button>
      </div>

      {/* Search */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
        <Input
          placeholder="Search dissertations..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Dissertations</p>
                <p className="text-2xl font-bold">{dissertations.length}</p>
              </div>
              <FileText className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">In Progress</p>
                <p className="text-2xl font-bold">{currentDissertations.length}</p>
              </div>
              <Clock className="w-8 h-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Completed</p>
                <p className="text-2xl font-bold">{pastDissertations.length}</p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Words</p>
                <p className="text-2xl font-bold">
                  {dissertations.reduce((sum, d) => sum + (d.word_count || 0), 0).toLocaleString()}
                </p>
              </div>
              <Edit className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs for Current and Past Dissertations */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="current" className="flex items-center space-x-2">
            <Clock className="w-4 h-4" />
            <span>Current Projects ({currentDissertations.length})</span>
          </TabsTrigger>
          <TabsTrigger value="past" className="flex items-center space-x-2">
            <Archive className="w-4 h-4" />
            <span>Past Dissertations ({pastDissertations.length})</span>
          </TabsTrigger>
        </TabsList>

        {/* Current Dissertations - Grid View */}
        <TabsContent value="current" className="mt-6">
          {filteredCurrentDissertations.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No current dissertations found</h3>
                <p className="text-gray-600 mb-4">
                  {searchTerm ? 'Try adjusting your search terms' : 'Get started by creating your first dissertation'}
                </p>
                {!searchTerm && (
                  <Button onClick={handleCreateNew} disabled={creatingDissertation}>
                    <Plus className="w-4 h-4 mr-2" />
                    {creatingDissertation ? 'Creating...' : 'Create Your First Dissertation'}
                  </Button>
                )}
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredCurrentDissertations.map((dissertation) => (
                <Card key={dissertation.id} className="hover:shadow-lg transition-all duration-200 group cursor-pointer"
                      onClick={() => handleOpenDissertation(dissertation.id)}>
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors line-clamp-2">
                          {dissertation.title}
                        </CardTitle>
                        <div className="flex items-center space-x-2 mt-2">
                          <Badge className={`${getStatusColor(dissertation.status)} flex items-center space-x-1`}>
                            {getStatusIcon(dissertation.status)}
                            <span>{dissertation.status.replace('_', ' ').toUpperCase()}</span>
                          </Badge>
                          {dissertation.active_collaborators && dissertation.active_collaborators.length > 0 && (
                            <div className="flex items-center space-x-1 text-green-600">
                              <User className="w-3 h-3" />
                              <span className="text-xs">{dissertation.active_collaborators.length}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  
                  <CardContent className="pt-0">
                    <div className="space-y-3">
                      {/* Stats */}
                      <div className="grid grid-cols-2 gap-3 text-sm">
                        <div className="flex items-center space-x-1 text-gray-600">
                          <FileText className="w-4 h-4" />
                          <span>{(dissertation.word_count || 0).toLocaleString()}</span>
                        </div>
                        <div className="flex items-center space-x-1 text-gray-600">
                          <BookOpen className="w-4 h-4" />
                          <span>{dissertation.page_count || 1} pages</span>
                        </div>
                        <div className="flex items-center space-x-1 text-gray-600">
                          <Clock className="w-4 h-4" />
                          <span>{formatDistanceToNow(dissertation.updated_at, { addSuffix: true })}</span>
                        </div>
                        <div className="flex items-center space-x-1 text-gray-600">
                          <Calendar className="w-4 h-4" />
                          <span>
                            {dissertation.submission_deadline 
                              ? formatDistanceToNow(dissertation.submission_deadline, { addSuffix: true })
                              : 'No deadline'
                            }
                          </span>
                        </div>
                      </div>

                      {/* Deadline */}
                      {dissertation.submission_deadline && (
                        <div className="text-sm text-orange-600">
                          <div className="flex items-center space-x-1">
                            <AlertCircle className="w-4 h-4" />
                            <span>Due {formatDistanceToNow(dissertation.submission_deadline, { addSuffix: true })}</span>
                          </div>
                        </div>
                      )}

                      {/* Action Buttons */}
                      <div className="flex items-center space-x-2 pt-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="flex-1"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleOpenDissertation(dissertation.id);
                          }}
                        >
                          <Eye className="w-4 h-4 mr-1" />
                          Open
                        </Button>
                        
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                            <Button variant="ghost" size="sm">
                              <MoreVertical className="w-4 h-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleOpenDissertation(dissertation.id)}>
                              <Edit className="w-4 h-4 mr-2" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleDuplicateDissertation(dissertation)}>
                              <FileText className="w-4 h-4 mr-2" />
                              Duplicate
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Calendar className="w-4 h-4 mr-2" />
                              Set Deadline
                            </DropdownMenuItem>
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                                  <Trash2 className="w-4 h-4 mr-2" />
                                  Delete
                                </DropdownMenuItem>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Delete Dissertation</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    Are you sure you want to delete "{dissertation.title}"? This action cannot be undone.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                                  <AlertDialogAction
                                    onClick={() => handleDeleteDissertation(dissertation.id, dissertation.title)}
                                    className="bg-red-600 hover:bg-red-700"
                                  >
                                    Delete
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        {/* Past Dissertations - Grid View */}
        <TabsContent value="past" className="mt-6">
          {filteredPastDissertations.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <Archive className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No past dissertations found</h3>
                <p className="text-gray-600">
                  {searchTerm ? 'Try adjusting your search terms' : 'Complete your current dissertations to see them here'}
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredPastDissertations.map((dissertation) => (
                <Card key={dissertation.id} className="hover:shadow-lg transition-all duration-200 group cursor-pointer"
                      onClick={() => handleOpenDissertation(dissertation.id)}>
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors line-clamp-2">
                          {dissertation.title}
                        </CardTitle>
                        <div className="flex items-center space-x-2 mt-2">
                          <Badge className={`${getStatusColor(dissertation.status)} flex items-center space-x-1`}>
                            {getStatusIcon(dissertation.status)}
                            <span>COMPLETED</span>
                          </Badge>
                          <Star className="w-4 h-4 text-yellow-500" />
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  
                  <CardContent className="pt-0">
                    <div className="space-y-3">
                      {/* Stats */}
                      <div className="grid grid-cols-2 gap-3 text-sm">
                        <div className="flex items-center space-x-1 text-gray-600">
                          <FileText className="w-4 h-4" />
                          <span>{(dissertation.word_count || 0).toLocaleString()}</span>
                        </div>
                        <div className="flex items-center space-x-1 text-gray-600">
                          <BookOpen className="w-4 h-4" />
                          <span>{dissertation.page_count || 1} pages</span>
                        </div>
                        <div className="flex items-center space-x-1 text-gray-600">
                          <Clock className="w-4 h-4" />
                          <span>v{dissertation.version}</span>
                        </div>
                        <div className="flex items-center space-x-1 text-gray-600">
                          <CheckCircle className="w-4 h-4" />
                          <span>Final</span>
                        </div>
                      </div>

                      {/* Completion Date */}
                      <div className="text-sm text-gray-500">
                        <div className="flex items-center space-x-1">
                          <Calendar className="w-4 h-4" />
                          <span>Completed {format(dissertation.updated_at, 'MMM dd, yyyy')}</span>
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex items-center space-x-2 pt-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="flex-1"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleOpenDissertation(dissertation.id);
                          }}
                        >
                          <Eye className="w-4 h-4 mr-1" />
                          View
                        </Button>
                        
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                            <Button variant="ghost" size="sm">
                              <MoreVertical className="w-4 h-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleOpenDissertation(dissertation.id)}>
                              <Eye className="w-4 h-4 mr-2" />
                              View
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Download className="w-4 h-4 mr-2" />
                              Download PDF
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleDuplicateDissertation(dissertation)}>
                              <FileText className="w-4 h-4 mr-2" />
                              Create New Version
                            </DropdownMenuItem>
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                                  <Trash2 className="w-4 h-4 mr-2" />
                                  Delete
                                </DropdownMenuItem>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Delete Dissertation</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    Are you sure you want to delete "{dissertation.title}"? This action cannot be undone.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                                  <AlertDialogAction
                                    onClick={() => handleDeleteDissertation(dissertation.id, dissertation.title)}
                                    className="bg-red-600 hover:bg-red-700"
                                  >
                                    Delete
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                            <DropdownMenuItem>
                              <Archive className="w-4 h-4 mr-2" />
                              Archive
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default StudentDissertations; 