import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardDescription, CardFooter } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  BookOpen, 
  Search, 
  Download, 
  ExternalLink, 
  BookMarked,
  FileText,
  Video,
  Headphones,
  Globe,
  Star,
  Clock,
  Filter,
  Loader2,
  AlertCircle
} from "lucide-react";
import { Student } from "@/types/student";
import { apiClient } from "@/lib/api-client";
import { capitalize } from '@/utils/string-utils';

interface LibraryProps {
  student: Student | null;
}

interface Resource {
  id: string;
  title: string;
  description: string;
  type: string;
  subject: string;
  author: string;
  publisher: string;
  dateAdded: Timestamp;
  rating: number;
  downloadUrl?: string;
  coverImage?: string;
  url?: string;
  accessLevel: string[];
}

// Helper components to keep the main component cleaner
const ResourceCard = ({ resource }: { resource: Resource }) => {
  const getResourceIcon = (type: string) => {
    switch (type) {
      case "book":
        return <BookOpen className="h-5 w-5 text-blue-600" />;
      case "document":
        return <FileText className="h-5 w-5 text-amber-600" />;
      case "video":
        return <Video className="h-5 w-5 text-red-600" />;
      case "audio":
        return <Headphones className="h-5 w-5 text-purple-600" />;
      case "website":
        return <Globe className="h-5 w-5 text-green-600" />;
      default:
        return <BookMarked className="h-5 w-5 text-gray-600" />;
    }
  };

  const getResourceBadge = (type: string) => {
    switch (type) {
      case "book":
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Book</Badge>;
      case "document":
        return <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">Document</Badge>;
      case "video":
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">Video</Badge>;
      case "audio":
        return <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">Audio</Badge>;
      case "website":
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Website</Badge>;
      default:
        return <Badge variant="outline">Resource</Badge>;
    }
  };

  const formatDate = (timestamp: Timestamp) => {
    const date = timestamp.toDate();
    return new Intl.DateTimeFormat('en-US', { 
      year: 'numeric',
      month: 'long', 
      day: 'numeric'
    }).format(date);
  };

  return (
    <Card className="overflow-hidden">
      <CardContent className="p-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex items-center justify-center bg-gray-100 p-4 rounded-md min-w-[60px] h-[60px]">
            {getResourceIcon(resource.type)}
          </div>
          <div className="flex-1">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
              <div>
                <h3 className="font-medium text-lg">{resource.title}</h3>
                <div className="flex items-center gap-2 mt-1">
                  {getResourceBadge(resource.type)}
                  {resource.subject && (
                    <Badge variant="outline">{resource.subject}</Badge>
                  )}
                </div>
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <Clock className="h-4 w-4 mr-1" />
                <span>Added: {formatDate(resource.dateAdded)}</span>
              </div>
            </div>
            <p className="text-sm text-gray-600 mt-3">{resource.description}</p>
            
            <div className="flex flex-wrap items-center gap-2 mt-4">
              {resource.rating && (
                <div className="flex items-center text-sm text-amber-600 mr-4">
                  <Star className="h-4 w-4 mr-1 fill-amber-500 text-amber-500" />
                  <span>{resource.rating}/5</span>
                </div>
              )}
              
              {resource.url && (
                <Button variant="outline" size="sm" className="h-8" asChild>
                  <a href={resource.url} target="_blank" rel="noopener noreferrer">
                    <ExternalLink className="h-4 w-4 mr-1" />
                    Open
                  </a>
                </Button>
              )}
              
              {resource.downloadUrl && (
                <Button variant="outline" size="sm" className="h-8" asChild>
                  <a href={resource.downloadUrl} download>
                    <Download className="h-4 w-4 mr-1" />
                    Download
                  </a>
                </Button>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

const StudentLibrary = ({ student }: LibraryProps) => {
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [selectedSubject, setSelectedSubject] = useState<string>("all");
  const [selectedType, setSelectedType] = useState<string>("all");
  const [resources, setResources] = useState<Resource[]>([]);
  const [subjects, setSubjects] = useState<string[]>([]);
  const [resourceTypes, setResourceTypes] = useState<string[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    const fetchLibraryData = async () => {
      if (!student?.id) return;
      
      try {
        setLoading(true);
        
        // Get the student's level and program for access control
        const studentLevel = student.level?.id || student.level_id || 'beginner';
        const studentCourse = student.course_id || 'general';
        
        // Fetch resources that are available to this student
        const resourcesQuery = query(
          collection(db, 'library_resources'),
          where('accessLevel', 'array-contains-any', [studentLevel, studentCourse, 'all'])
        );
        
        const resourcesSnapshot = await getDocs(resourcesQuery);
        const resourcesData = resourcesSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        } as Resource));
        
        setResources(resourcesData);
        
        // Extract unique subjects and resource types
        const uniqueSubjects = Array.from(new Set(resourcesData.map(r => r.subject)));
        const uniqueTypes = Array.from(new Set(resourcesData.map(r => r.type)));
        
        setSubjects(uniqueSubjects);
        setResourceTypes(uniqueTypes);
        setError(null);
      } catch (err) {
        console.error('Error fetching library resources:', err);
        setError('Failed to load library resources. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchLibraryData();
  }, [student?.id]);
  
  if (!student) {
    return (
      <div className="p-6 text-center">
        <h2 className="text-2xl font-bold text-red-600">Student Information Not Found</h2>
        <p className="text-gray-600 mt-2">Your student profile could not be loaded. Please contact support.</p>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin text-green-600" />
        <span className="ml-2 text-lg text-gray-600">Loading library resources...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 text-center">
        <AlertCircle className="h-12 w-12 text-red-600 mx-auto mb-2" />
        <h2 className="text-xl font-bold text-red-600">Error Loading Library</h2>
        <p className="text-gray-600 mt-2">{error}</p>
        <Button 
          className="mt-4"
          onClick={() => window.location.reload()}
        >
          Retry
        </Button>
      </div>
    );
  }

  // Filter resources based on search query and selected filters
  const filteredResources = resources.filter(resource => {
    const matchesSearch = searchQuery === "" || 
      resource.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      resource.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (resource.author && resource.author.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesSubject = selectedSubject === "all" || resource.subject === selectedSubject;
    const matchesType = selectedType === "all" || resource.type === selectedType;
    
    return matchesSearch && matchesSubject && matchesType;
  });

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">Digital Library</h1>
        <p className="text-muted-foreground">Access books, documents, videos, and other learning resources.</p>
      </div>

      <div className="flex flex-col md:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              type="search"
              placeholder="Search resources..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
        <div className="flex flex-col sm:flex-row gap-2">
          <Select value={selectedSubject} onValueChange={setSelectedSubject}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select Subject" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Subjects</SelectItem>
              {subjects.map((subject) => (
                <SelectItem key={subject} value={subject}>{subject}</SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Select value={selectedType} onValueChange={setSelectedType}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              {resourceTypes.map((type) => (
                <SelectItem key={type} value={type}>
                  {capitalize(type)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <Tabs defaultValue="all" className="space-y-4">
        <TabsList>
          <TabsTrigger value="all">All Resources</TabsTrigger>
          <TabsTrigger value="recommended">Recommended</TabsTrigger>
          <TabsTrigger value="recent">Recently Added</TabsTrigger>
          <TabsTrigger value="popular">Most Popular</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-4">
          {filteredResources.length === 0 ? (
            <Card>
              <CardContent className="p-6 text-center">
                <p className="text-gray-500">No resources found matching your search criteria.</p>
              </CardContent>
            </Card>
          ) : (
            filteredResources.map((resource) => (
              <ResourceCard key={resource.id} resource={resource} />
            ))
          )}
        </TabsContent>

        <TabsContent value="recommended" className="space-y-4">
          {filteredResources
            .filter(resource => resource.rating >= 4.5)
            .map((resource) => (
              <ResourceCard key={resource.id} resource={resource} />
            ))}
        </TabsContent>

        <TabsContent value="recent" className="space-y-4">
          {filteredResources
            .sort((a, b) => b.dateAdded.seconds - a.dateAdded.seconds)
            .slice(0, 5)
            .map((resource) => (
              <ResourceCard key={resource.id} resource={resource} />
            ))}
        </TabsContent>

        <TabsContent value="popular" className="space-y-4">
          {filteredResources
            .sort((a, b) => b.rating - a.rating)
            .slice(0, 5)
            .map((resource) => (
              <ResourceCard key={resource.id} resource={resource} />
            ))}
        </TabsContent>
      </Tabs>

      <Card>
        <CardHeader>
          <CardTitle>External Learning Resources</CardTitle>
          <CardDescription>Additional resources to enhance your learning</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="border rounded-md p-4">
              <div className="flex items-center gap-3 mb-2">
                <Globe className="h-5 w-5 text-blue-600" />
                <h3 className="font-medium">BBC Learning English</h3>
              </div>
              <p className="text-sm text-gray-600 mb-3">
                Free resources to help you learn English including videos, podcasts, and interactive exercises.
              </p>
              <Button variant="outline" size="sm" className="w-full" asChild>
                <a href="https://www.bbc.co.uk/learningenglish" target="_blank" rel="noopener noreferrer">
                  <ExternalLink className="h-4 w-4 mr-1" />
                  Visit Website
                </a>
              </Button>
            </div>
            
            <div className="border rounded-md p-4">
              <div className="flex items-center gap-3 mb-2">
                <Globe className="h-5 w-5 text-red-600" />
                <h3 className="font-medium">British Council</h3>
              </div>
              <p className="text-sm text-gray-600 mb-3">
                English courses, learning resources, and practice materials for all levels.
              </p>
              <Button variant="outline" size="sm" className="w-full" asChild>
                <a href="https://learnenglish.britishcouncil.org" target="_blank" rel="noopener noreferrer">
                  <ExternalLink className="h-4 w-4 mr-1" />
                  Visit Website
                </a>
              </Button>
            </div>
            
            <div className="border rounded-md p-4">
              <div className="flex items-center gap-3 mb-2">
                <Video className="h-5 w-5 text-red-600" />
                <h3 className="font-medium">TED Talks</h3>
              </div>
              <p className="text-sm text-gray-600 mb-3">
                Watch inspiring talks on various topics to improve your listening and vocabulary skills.
              </p>
              <Button variant="outline" size="sm" className="w-full" asChild>
                <a href="https://www.ted.com" target="_blank" rel="noopener noreferrer">
                  <ExternalLink className="h-4 w-4 mr-1" />
                  Visit Website
                </a>
              </Button>
            </div>
            
            <div className="border rounded-md p-4">
              <div className="flex items-center gap-3 mb-2">
                <Headphones className="h-5 w-5 text-green-600" />
                <h3 className="font-medium">English Podcasts</h3>
              </div>
              <p className="text-sm text-gray-600 mb-3">
                Listen to podcasts designed for English learners to improve your listening skills.
              </p>
              <Button variant="outline" size="sm" className="w-full" asChild>
                <a href="https://www.eslpod.com" target="_blank" rel="noopener noreferrer">
                  <ExternalLink className="h-4 w-4 mr-1" />
                  Visit Website
                </a>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default StudentLibrary; 