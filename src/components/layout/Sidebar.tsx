import { Link, useLocation } from 'react-router-dom';
import { But<PERSON> } from "@/components/ui/button";
import { usePageNavigation } from '@/hooks/usePageNavigation';
import {
  LayoutDashboard,
  Users,
  BookOpen,
  GraduationCap,
  CreditCard,
  CalendarDays,
  LogOut,
  Settings as SettingsIcon,
  UserPlus,
  Wallet,
  Receipt,
  Layers,
  UserCircle,
  ClipboardList,
  FileText,
  BarChart2,
  Bell,
  BookText,
  GraduationCap as ExamIcon,
  MessageSquare,
  Library,
  Award,
  IdCard,
  PenTool
} from 'lucide-react';

interface SidebarProps {
  sidebarOpen: boolean;
  isSuperAdmin: boolean;
  showEnrollmentSection: boolean;
  showFinanceSection: boolean;
  showSettings: boolean;
  isTeacher?: boolean;
  isStudent?: boolean;
  logout: () => void;
}

export const Sidebar = ({
  sidebarOpen,
  isSuperAdmin,
  showEnrollmentSection,
  showFinanceSection,
  showSettings,
  isTeacher = false,
  isStudent = false,
  logout,
}: SidebarProps) => {
  const location = useLocation();
  const { navigateTo } = usePageNavigation();

  const isActiveRoute = (path: string) => {
    return location.pathname.includes(path);
  };

  // Determine which sections to show
  const isFinanceAdmin = showFinanceSection && !isSuperAdmin && !showSettings;
  
  // Only show dashboard link if not a finance admin and not a teacher/student
  const showDashboardLink = !isFinanceAdmin && !isTeacher && !isStudent;
  const shouldShowEnrollment = showEnrollmentSection || isSuperAdmin;
  const shouldShowFinance = showFinanceSection || isSuperAdmin;
  const shouldShowSettings = showSettings || isSuperAdmin;
  
  // Determine user role
  const userRole = isSuperAdmin ? 'super_admin' : showSettings ? 'admin' : '';

  return (
    <>
      {/* Mobile overlay */}
      <div
        className={`fixed inset-0 bg-black/50 z-40 lg:hidden transition-opacity duration-200 ${
          sidebarOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
        }`}
        onClick={(e) => {
          e.preventDefault();
          document.dispatchEvent(new CustomEvent('toggle-sidebar'));
        }}
      />

      {/* Sidebar */}
      <aside
        className={`fixed top-16 left-0 z-50 w-64 h-[calc(100vh-4rem)] bg-white border-r shadow-sm transition-transform duration-200 ease-in-out lg:translate-x-0 overflow-y-auto ${
          sidebarOpen ? 'translate-x-0' : '-translate-x-full'
        }`}
      >
        <div className="h-full px-3 py-4 overflow-y-auto">
          <nav className="space-y-2 flex-1">
            {/* Student Navigation Items */}
            {isStudent && (
              <>
                <Link 
                  to="/dashboard/student"
                  onClick={(e) => {
                    e.preventDefault();
                    if (window.innerWidth < 1024) {
                      e.stopPropagation();
                    }
                    navigateTo('/dashboard/student');
                  }}
                >
                  <Button
                    variant="ghost"
                    className={`w-full justify-start ${
                      location.pathname === '/dashboard/student' ? 'bg-green-50 text-green-700' : 'text-gray-600'
                    }`}
                  >
                    <LayoutDashboard className="mr-2 h-4 w-4" />
                    Overview
                  </Button>
                </Link>

                <Link 
                  to="/dashboard/student/profile"
                  onClick={(e) => {
                    e.preventDefault();
                    if (window.innerWidth < 1024) {
                      e.stopPropagation();
                    }
                    navigateTo('/dashboard/student/profile');
                  }}
                >
                  <Button
                    variant="ghost"
                    className={`w-full justify-start ${
                      isActiveRoute('/dashboard/student/profile') ? 'bg-green-50 text-green-700' : 'text-gray-600'
                    }`}
                  >
                    <UserCircle className="mr-2 h-4 w-4" />
                    Profile
                  </Button>
                </Link>

                <Link 
                  to="/dashboard/student/courses"
                  onClick={(e) => {
                    e.preventDefault();
                    if (window.innerWidth < 1024) {
                      e.stopPropagation();
                    }
                    navigateTo('/dashboard/student/courses');
                  }}
                >
                  <Button
                    variant="ghost"
                    className={`w-full justify-start ${
                      isActiveRoute('/dashboard/student/courses') ? 'bg-green-50 text-green-700' : 'text-gray-600'
                    }`}
                  >
                    <BookOpen className="mr-2 h-4 w-4" />
                    Courses
                  </Button>
                </Link>

                <Link 
                  to="/dashboard/student/timetable"
                  onClick={(e) => {
                    e.preventDefault();
                    if (window.innerWidth < 1024) {
                      e.stopPropagation();
                    }
                    navigateTo('/dashboard/student/timetable');
                  }}
                >
                  <Button
                    variant="ghost"
                    className={`w-full justify-start ${
                      isActiveRoute('/dashboard/student/timetable') ? 'bg-green-50 text-green-700' : 'text-gray-600'
                    }`}
                  >
                    <CalendarDays className="mr-2 h-4 w-4" />
                    Timetable
                  </Button>
                </Link>

                <Link 
                  to="/dashboard/student/assignments"
                  onClick={(e) => {
                    e.preventDefault();
                    if (window.innerWidth < 1024) {
                      e.stopPropagation();
                    }
                    navigateTo('/dashboard/student/assignments');
                  }}
                >
                  <Button
                    variant="ghost"
                    className={`w-full justify-start ${
                      isActiveRoute('/dashboard/student/assignments') ? 'bg-green-50 text-green-700' : 'text-gray-600'
                    }`}
                  >
                    <ClipboardList className="mr-2 h-4 w-4" />
                    Assignments
                  </Button>
                </Link>

                <Link 
                  to="/dashboard/student/attendance"
                  onClick={(e) => {
                    e.preventDefault();
                    if (window.innerWidth < 1024) {
                      e.stopPropagation();
                    }
                    navigateTo('/dashboard/student/attendance');
                  }}
                >
                  <Button
                    variant="ghost"
                    className={`w-full justify-start ${
                      isActiveRoute('/dashboard/student/attendance') ? 'bg-green-50 text-green-700' : 'text-gray-600'
                    }`}
                  >
                    <ClipboardList className="mr-2 h-4 w-4" />
                    Attendance
                  </Button>
                </Link>

                <Link 
                  to="/dashboard/student/materials"
                  onClick={(e) => {
                    e.preventDefault();
                    if (window.innerWidth < 1024) {
                      e.stopPropagation();
                    }
                    navigateTo('/dashboard/student/materials');
                  }}
                >
                  <Button
                    variant="ghost"
                    className={`w-full justify-start ${
                      isActiveRoute('/dashboard/student/materials') ? 'bg-green-50 text-green-700' : 'text-gray-600'
                    }`}
                  >
                    <BookText className="mr-2 h-4 w-4" />
                    Materials
                  </Button>
                </Link>

                <Link 
                  to="/dashboard/student/announcements"
                  onClick={(e) => {
                    e.preventDefault();
                    if (window.innerWidth < 1024) {
                      e.stopPropagation();
                    }
                    navigateTo('/dashboard/student/announcements');
                  }}
                >
                  <Button
                    variant="ghost"
                    className={`w-full justify-start ${
                      isActiveRoute('/dashboard/student/announcements') ? 'bg-green-50 text-green-700' : 'text-gray-600'
                    }`}
                  >
                    <Bell className="mr-2 h-4 w-4" />
                    Announcements
                  </Button>
                </Link>

                <Link 
                  to="/dashboard/student/calendar"
                  onClick={(e) => {
                    e.preventDefault();
                    if (window.innerWidth < 1024) {
                      e.stopPropagation();
                    }
                    navigateTo('/dashboard/student/calendar');
                  }}
                >
                  <Button
                    variant="ghost"
                    className={`w-full justify-start ${
                      isActiveRoute('/dashboard/student/calendar') ? 'bg-green-50 text-green-700' : 'text-gray-600'
                    }`}
                  >
                    <CalendarDays className="mr-2 h-4 w-4" />
                    Events Calendar
                  </Button>
                </Link>

                <Link 
                  to="/dashboard/student/feedback"
                  onClick={(e) => {
                    e.preventDefault();
                    if (window.innerWidth < 1024) {
                      e.stopPropagation();
                    }
                    navigateTo('/dashboard/student/feedback');
                  }}
                >
                  <Button
                    variant="ghost"
                    className={`w-full justify-start ${
                      isActiveRoute('/dashboard/student/feedback') ? 'bg-green-50 text-green-700' : 'text-gray-600'
                    }`}
                  >
                    <MessageSquare className="mr-2 h-4 w-4" />
                    Feedback
                  </Button>
                </Link>

                <Link 
                  to="/dashboard/student/grades"
                  onClick={(e) => {
                    e.preventDefault();
                    if (window.innerWidth < 1024) {
                      e.stopPropagation();
                    }
                    navigateTo('/dashboard/student/grades');
                  }}
                >
                  <Button
                    variant="ghost"
                    className={`w-full justify-start ${
                      isActiveRoute('/dashboard/student/grades') ? 'bg-green-50 text-green-700' : 'text-gray-600'
                    }`}
                  >
                    <Award className="mr-2 h-4 w-4" />
                    Grades
                  </Button>
                </Link>

                <Link 
                  to="/dashboard/student/dissertations"
                  onClick={(e) => {
                    e.preventDefault();
                    if (window.innerWidth < 1024) {
                      e.stopPropagation();
                    }
                    navigateTo('/dashboard/student/dissertations');
                  }}
                >
                  <Button
                    variant="ghost"
                    className={`w-full justify-start ${
                      isActiveRoute('/dashboard/student/dissertations') ? 'bg-green-50 text-green-700' : 'text-gray-600'
                    }`}
                  >
                    <PenTool className="mr-2 h-4 w-4" />
                    Dissertations
                  </Button>
                </Link>
              </>
            )}

            {/* Teacher Navigation Items */}
            {isTeacher && (
              <>
                <Link 
                  to="/dashboard/teacher"
                  onClick={(e) => {
                    e.preventDefault();
                    if (window.innerWidth < 1024) {
                      e.stopPropagation();
                    }
                    navigateTo('/dashboard/teacher');
                  }}
                >
                  <Button
                    variant="ghost"
                    className={`w-full justify-start ${
                      location.pathname === '/dashboard/teacher' ? 'bg-green-50 text-green-700' : 'text-gray-600'
                    }`}
                  >
                    <LayoutDashboard className="mr-2 h-4 w-4" />
                    Overview
                  </Button>
                </Link>

                <Link 
                  to="/dashboard/teacher/profile"
                  onClick={(e) => {
                    e.preventDefault();
                    if (window.innerWidth < 1024) {
                      e.stopPropagation();
                    }
                    navigateTo('/dashboard/teacher/profile');
                  }}
                >
                  <Button
                    variant="ghost"
                    className={`w-full justify-start ${
                      isActiveRoute('/dashboard/teacher/profile') ? 'bg-green-50 text-green-700' : 'text-gray-600'
                    }`}
                  >
                    <UserCircle className="mr-2 h-4 w-4" />
                    Profile
                  </Button>
                </Link>

                <Link 
                  to="/dashboard/teacher/levels"
                  onClick={(e) => {
                    e.preventDefault();
                    if (window.innerWidth < 1024) {
                      e.stopPropagation();
                    }
                    navigateTo('/dashboard/teacher/levels');
                  }}
                >
                  <Button
                    variant="ghost"
                    className={`w-full justify-start ${
                      isActiveRoute('/dashboard/teacher/levels') ? 'bg-green-50 text-green-700' : 'text-gray-600'
                    }`}
                  >
                    <GraduationCap className="mr-2 h-4 w-4" />
                    Levels
                  </Button>
                </Link>

                <Link 
                  to="/dashboard/teacher/attendance"
                  onClick={(e) => {
                    e.preventDefault();
                    if (window.innerWidth < 1024) {
                      e.stopPropagation();
                    }
                    navigateTo('/dashboard/teacher/attendance');
                  }}
                >
                  <Button
                    variant="ghost"
                    className={`w-full justify-start ${
                      isActiveRoute('/dashboard/teacher/attendance') ? 'bg-green-50 text-green-700' : 'text-gray-600'
                    }`}
                  >
                    <ClipboardList className="mr-2 h-4 w-4" />
                    Attendance
                  </Button>
                </Link>

                <Link 
                  to="/dashboard/teacher/timetable"
                  onClick={(e) => {
                    e.preventDefault();
                    if (window.innerWidth < 1024) {
                      e.stopPropagation();
                    }
                    navigateTo('/dashboard/teacher/timetable');
                  }}
                >
                  <Button
                    variant="ghost"
                    className={`w-full justify-start ${
                      isActiveRoute('/dashboard/teacher/timetable') ? 'bg-green-50 text-green-700' : 'text-gray-600'
                    }`}
                  >
                    <CalendarDays className="mr-2 h-4 w-4" />
                    Timetable
                  </Button>
                </Link>

                <Link 
                  to="/dashboard/teacher/assignments"
                  onClick={(e) => {
                    e.preventDefault();
                    if (window.innerWidth < 1024) {
                      e.stopPropagation();
                    }
                    navigateTo('/dashboard/teacher/assignments');
                  }}
                >
                  <Button
                    variant="ghost"
                    className={`w-full justify-start ${
                      isActiveRoute('/dashboard/teacher/assignments') ? 'bg-green-50 text-green-700' : 'text-gray-600'
                    }`}
                  >
                    <FileText className="mr-2 h-4 w-4" />
                    Assignments
                  </Button>
                </Link>

                <Link 
                  to="/dashboard/teacher/performance"
                  onClick={(e) => {
                    e.preventDefault();
                    if (window.innerWidth < 1024) {
                      e.stopPropagation();
                    }
                    navigateTo('/dashboard/teacher/performance');
                  }}
                >
                  <Button
                    variant="ghost"
                    className={`w-full justify-start ${
                      isActiveRoute('/dashboard/teacher/performance') ? 'bg-green-50 text-green-700' : 'text-gray-600'
                    }`}
                  >
                    <BarChart2 className="mr-2 h-4 w-4" />
                    Performance
                  </Button>
                </Link>

                <Link 
                  to="/dashboard/teacher/announcements"
                  onClick={(e) => {
                    e.preventDefault();
                    if (window.innerWidth < 1024) {
                      e.stopPropagation();
                    }
                    navigateTo('/dashboard/teacher/announcements');
                  }}
                >
                  <Button
                    variant="ghost"
                    className={`w-full justify-start ${
                      isActiveRoute('/dashboard/teacher/announcements') ? 'bg-green-50 text-green-700' : 'text-gray-600'
                    }`}
                  >
                    <Bell className="mr-2 h-4 w-4" />
                    Announcements
                  </Button>
                </Link>

                <Link 
                  to="/dashboard/teacher/exams"
                  onClick={(e) => {
                    e.preventDefault();
                    if (window.innerWidth < 1024) {
                      e.stopPropagation();
                    }
                    navigateTo('/dashboard/teacher/exams');
                  }}
                >
                  <Button
                    variant="ghost"
                    className={`w-full justify-start ${
                      isActiveRoute('/dashboard/teacher/exams') ? 'bg-green-50 text-green-700' : 'text-gray-600'
                    }`}
                  >
                    <ExamIcon className="mr-2 h-4 w-4" />
                    Exams
                  </Button>
                </Link>

                <Link 
                  to="/dashboard/teacher/materials"
                  onClick={(e) => {
                    e.preventDefault();
                    if (window.innerWidth < 1024) {
                      e.stopPropagation();
                    }
                    navigateTo('/dashboard/teacher/materials');
                  }}
                >
                  <Button
                    variant="ghost"
                    className={`w-full justify-start ${
                      isActiveRoute('/dashboard/teacher/materials') ? 'bg-green-50 text-green-700' : 'text-gray-600'
                    }`}
                  >
                    <BookText className="mr-2 h-4 w-4" />
                    Materials
                  </Button>
                </Link>

                <Link 
                  to="/dashboard/teacher/dissertations"
                  onClick={(e) => {
                    e.preventDefault();
                    if (window.innerWidth < 1024) {
                      e.stopPropagation();
                    }
                    navigateTo('/dashboard/teacher/dissertations');
                  }}
                >
                  <Button
                    variant="ghost"
                    className={`w-full justify-start ${
                      isActiveRoute('/dashboard/teacher/dissertations') ? 'bg-green-50 text-green-700' : 'text-gray-600'
                    }`}
                  >
                    <PenTool className="mr-2 h-4 w-4" />
                    Dissertations
                  </Button>
                </Link>
              </>
            )}

            {/* Show Dashboard link only if not a finance admin and not a teacher */}
            {showDashboardLink && !isTeacher && (
              <Link 
                to="/dashboard"
                onClick={(e) => {
                  e.preventDefault();
                  if (window.innerWidth < 1024) {
                    e.stopPropagation();
                  }
                  navigateTo('/dashboard');
                }}
              >
                <Button
                  variant="ghost"
                  className={`w-full justify-start ${
                    location.pathname === '/dashboard' || location.pathname === '/' ? 'bg-green-50 text-green-700' : 'text-gray-600'
                  }`}
                >
                  <LayoutDashboard className="mr-2 h-4 w-4" />
                  Dashboard
                </Button>
              </Link>
            )}

            {/* Announcements and Events links for all admin users */}
            {(isSuperAdmin || userRole === 'admin') && !isTeacher && !isStudent && (
              <>
                <Link 
                  to="/dashboard/announcements"
                  onClick={(e) => {
                    e.preventDefault();
                    if (window.innerWidth < 1024) {
                      e.stopPropagation();
                    }
                    navigateTo('/dashboard/announcements');
                  }}
                >
                  <Button
                    variant="ghost"
                    className={`w-full justify-start ${
                      isActiveRoute('/dashboard/announcements') ? 'bg-green-50 text-green-700' : 'text-gray-600'
                    }`}
                  >
                    <Bell className="mr-2 h-4 w-4" />
                    Announcements
                  </Button>
                </Link>

                <Link 
                  to="/dashboard/events"
                  onClick={(e) => {
                    e.preventDefault();
                    if (window.innerWidth < 1024) {
                      e.stopPropagation();
                    }
                    navigateTo('/dashboard/events');
                  }}
                >
                  <Button
                    variant="ghost"
                    className={`w-full justify-start ${
                      isActiveRoute('/dashboard/events') ? 'bg-green-50 text-green-700' : 'text-gray-600'
                    }`}
                  >
                    <CalendarDays className="mr-2 h-4 w-4" />
                    Events & Calendar
                  </Button>
                </Link>

                <Link 
                  to="/dashboard/timetable"
                  onClick={(e) => {
                    e.preventDefault();
                    if (window.innerWidth < 1024) {
                      e.stopPropagation();
                    }
                    navigateTo('/dashboard/timetable');
                  }}
                >
                  <Button
                    variant="ghost"
                    className={`w-full justify-start ${
                      isActiveRoute('/dashboard/timetable') ? 'bg-green-50 text-green-700' : 'text-gray-600'
                    }`}
                  >
                    <CalendarDays className="mr-2 h-4 w-4" />
                    Timetable Management
                  </Button>
                </Link>

                <Link 
                  to="/dashboard/dissertations"
                  onClick={(e) => {
                    e.preventDefault();
                    if (window.innerWidth < 1024) {
                      e.stopPropagation();
                    }
                    navigateTo('/dashboard/dissertations');
                  }}
                >
                  <Button
                    variant="ghost"
                    className={`w-full justify-start ${
                      isActiveRoute('/dashboard/dissertations') ? 'bg-green-50 text-green-700' : 'text-gray-600'
                    }`}
                  >
                    <PenTool className="mr-2 h-4 w-4" />
                    Dissertations
                  </Button>
                </Link>
              </>
            )}

            {/* Rest of the navigation items */}
            {shouldShowEnrollment && !isTeacher && (
              <>
                <Link 
                  to="/dashboard/enrollment"
                  onClick={(e) => {
                    e.preventDefault();
                    if (window.innerWidth < 1024) {
                      e.stopPropagation();
                    }
                    navigateTo('/dashboard/enrollment');
                  }}
                >
                  <Button
                    variant="ghost"
                    className={`w-full justify-start ${
                      isActiveRoute('/dashboard/enrollment') ? 'bg-green-50 text-green-700' : 'text-gray-600'
                    }`}
                  >
                    <Users className="mr-2 h-4 w-4" />
                    Students
                  </Button>
                </Link>

                <Link 
                  to="/dashboard/enrollment/new"
                  onClick={(e) => {
                    e.preventDefault();
                    if (window.innerWidth < 1024) {
                      e.stopPropagation();
                    }
                    navigateTo('/dashboard/enrollment/new');
                  }}
                >
                  <Button
                    variant="ghost"
                    className={`w-full justify-start ${
                      isActiveRoute('/dashboard/enrollment/new') ? 'bg-green-50 text-green-700' : 'text-gray-600'
                    }`}
                  >
                    <UserPlus className="mr-2 h-4 w-4" />
                    New Student
                  </Button>
                </Link>

                <Link 
                  to="/dashboard/enrollment/id-cards"
                  onClick={(e) => {
                    e.preventDefault();
                    if (window.innerWidth < 1024) {
                      e.stopPropagation();
                    }
                    navigateTo('/dashboard/enrollment/id-cards');
                  }}
                >
                  <Button
                    variant="ghost"
                    className={`w-full justify-start ${
                      isActiveRoute('/dashboard/enrollment/id-cards') ? 'bg-green-50 text-green-700' : 'text-gray-600'
                    }`}
                  >
                    <IdCard className="mr-2 h-4 w-4" />
                    ID Cards
                  </Button>
                </Link>

                <Link 
                  to="/dashboard/enrollment/attendance"
                  onClick={(e) => {
                    e.preventDefault();
                    if (window.innerWidth < 1024) {
                      e.stopPropagation();
                    }
                    navigateTo('/dashboard/enrollment/attendance');
                  }}
                >
                  <Button
                    variant="ghost"
                    className={`w-full justify-start ${
                      isActiveRoute('/dashboard/enrollment/attendance') ? 'bg-green-50 text-green-700' : 'text-gray-600'
                    }`}
                  >
                    <CalendarDays className="mr-2 h-4 w-4" />
                    Attendance
                  </Button>
                </Link>
              </>
            )}

            {shouldShowFinance && !isTeacher && (
              <>
                <Link 
                  to="/dashboard/finance"
                  onClick={(e) => {
                    e.preventDefault();
                    if (window.innerWidth < 1024) {
                      e.stopPropagation();
                    }
                    navigateTo('/dashboard/finance');
                  }}
                >
                  <Button
                    variant="ghost"
                    className={`w-full justify-start ${
                      isActiveRoute('/dashboard/finance') && !isActiveRoute('/dashboard/finance/fees') ? 'bg-green-50 text-green-700' : 'text-gray-600'
                    }`}
                  >
                    <CreditCard className="mr-2 h-4 w-4" />
                    Overview
                  </Button>
                </Link>

                <Link 
                  to="/dashboard/finance/fees"
                  onClick={(e) => {
                    e.preventDefault();
                    if (window.innerWidth < 1024) {
                      e.stopPropagation();
                    }
                    navigateTo('/dashboard/finance/fees');
                  }}
                >
                  <Button
                    variant="ghost"
                    className={`w-full justify-start ${
                      isActiveRoute('/dashboard/finance/fees') ? 'bg-green-50 text-green-700' : 'text-gray-600'
                    }`}
                  >
                    <Wallet className="mr-2 h-4 w-4" />
                    Fees
                  </Button>
                </Link>

                <Link 
                  to="/dashboard/finance/transactions"
                  onClick={(e) => {
                    e.preventDefault();
                    if (window.innerWidth < 1024) {
                      e.stopPropagation();
                    }
                    navigateTo('/dashboard/finance/transactions');
                  }}
                >
                  <Button
                    variant="ghost"
                    className={`w-full justify-start ${
                      isActiveRoute('/dashboard/finance/transactions') ? 'bg-green-50 text-green-700' : 'text-gray-600'
                    }`}
                  >
                    <Receipt className="mr-2 h-4 w-4" />
                    Transactions
                  </Button>
                </Link>
              </>
            )}

            {shouldShowSettings && !isTeacher && (
              <>
                <Link 
                  to="/dashboard/courses"
                  onClick={(e) => {
                    e.preventDefault();
                    if (window.innerWidth < 1024) {
                      e.stopPropagation();
                    }
                    navigateTo('/dashboard/courses');
                  }}
                >
                  <Button
                    variant="ghost"
                    className={`w-full justify-start ${
                      isActiveRoute('/dashboard/courses') ? 'bg-green-50 text-green-700' : 'text-gray-600'
                    }`}
                  >
                    <BookOpen className="mr-2 h-4 w-4" />
                    Courses
                  </Button>
                </Link>

                <Link 
                  to="/dashboard/subjects"
                  onClick={(e) => {
                    e.preventDefault();
                    if (window.innerWidth < 1024) {
                      e.stopPropagation();
                    }
                    navigateTo('/dashboard/subjects');
                  }}
                >
                  <Button
                    variant="ghost"
                    className={`w-full justify-start ${
                      isActiveRoute('/dashboard/subjects') ? 'bg-green-50 text-green-700' : 'text-gray-600'
                    }`}
                  >
                    <BookText className="mr-2 h-4 w-4" />
                    Subjects
                  </Button>
                </Link>

                <Link 
                  to="/dashboard/levels"
                  onClick={(e) => {
                    e.preventDefault();
                    if (window.innerWidth < 1024) {
                      e.stopPropagation();
                    }
                    navigateTo('/dashboard/levels');
                  }}
                >
                  <Button
                    variant="ghost"
                    className={`w-full justify-start ${
                      isActiveRoute('/dashboard/levels') ? 'bg-green-50 text-green-700' : 'text-gray-600'
                    }`}
                  >
                    <Layers className="mr-2 h-4 w-4" />
                    Levels
                  </Button>
                </Link>
                
                <Link 
                  to="/dashboard/settings"
                  onClick={(e) => {
                    e.preventDefault();
                    if (window.innerWidth < 1024) {
                      e.stopPropagation();
                    }
                    navigateTo('/dashboard/settings');
                  }}
                >
                  <Button
                    variant="ghost"
                    className={`w-full justify-start ${
                      isActiveRoute('/dashboard/settings') ? 'bg-green-50 text-green-700' : 'text-gray-600'
                    }`}
                  >
                    <SettingsIcon className="mr-2 h-4 w-4" />
                    Settings
                  </Button>
                </Link>
              </>
            )}

            {/* Logout button */}
            <Button
              variant="ghost"
              className="w-full justify-start text-gray-600 hover:bg-red-50 hover:text-red-700"
              onClick={logout}
            >
              <LogOut className="mr-2 h-4 w-4" />
              Logout
            </Button>
          </nav>
        </div>
      </aside>
    </>
  );
};
