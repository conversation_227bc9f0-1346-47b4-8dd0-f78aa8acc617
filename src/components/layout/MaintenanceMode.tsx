
import { LogOut, AlertTriangle } from 'lucide-react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

interface MaintenanceModeProps {
  logout: () => void;
}

export const MaintenanceMode = ({ logout }: MaintenanceModeProps) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-yellow-50 to-green-50 flex items-center justify-center p-4">
      <Card className="max-w-md w-full">
        <CardContent className="pt-6">
          <div className="text-center space-y-4">
            <div className="bg-yellow-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto">
              <AlertTriangle className="h-8 w-8 text-yellow-600" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900">System Maintenance</h2>
            <p className="text-gray-600">
              The system is currently undergoing scheduled maintenance. Please try again later or contact the administrator for more information.
            </p>
            <p className="text-sm text-gray-500 mt-2">
              Maintenance mode is active to ensure system stability during updates.
            </p>
            <Button
              onClick={logout}
              variant="outline"
              className="mt-4 w-full"
            >
              <LogOut className="mr-2 h-4 w-4" />
              Logout
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
