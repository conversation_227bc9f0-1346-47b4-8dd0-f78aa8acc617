import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useState, useEffect } from "react";
import { Wifi, WifiOff } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import CourseManagement from "./settings/CourseManagement";
import LevelManagement from "./settings/LevelManagement";
import GeneralSettings from "./settings/GeneralSettings";
import TeacherManagement from "./settings/TeacherManagement";
import DissertationManagement from "./DissertationManagement";
import { ActivityLogs } from "./ActivityLogs";
import { UserManagement } from "./UserManagement";

export const Settings = () => {
  const [isOffline, setIsOffline] = useState<boolean>(!navigator.onLine);

  useEffect(() => {
    const handleOnline = () => setIsOffline(false);
    const handleOffline = () => setIsOffline(true);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold">Settings</h2>
        <p className="text-muted-foreground">Manage your school settings</p>
      </div>

      {isOffline && (
        <Alert className="bg-yellow-50 border-yellow-200">
          <WifiOff className="h-4 w-4 text-yellow-600" />
          <AlertDescription className="text-yellow-800">
            You are currently offline. Changes will be saved when your connection is restored.
          </AlertDescription>
        </Alert>
      )}

      {!isOffline && (
        <Alert className="bg-green-50 border-green-200">
          <Wifi className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">
            You are online. All changes will be saved immediately.
          </AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="general">
        <TabsList>
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="courses">Courses</TabsTrigger>
          <TabsTrigger value="levels">Levels</TabsTrigger>
          <TabsTrigger value="teachers">Teachers</TabsTrigger>
          <TabsTrigger value="dissertations">Dissertations</TabsTrigger>
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="activity">Activity Logs</TabsTrigger>
        </TabsList>
        <TabsContent value="general">
          <GeneralSettings />
        </TabsContent>
        <TabsContent value="courses">
          <CourseManagement />
        </TabsContent>
        <TabsContent value="levels">
          <LevelManagement />
        </TabsContent>
        <TabsContent value="teachers">
          <TeacherManagement />
        </TabsContent>
        <TabsContent value="dissertations">
          <DissertationManagement />
        </TabsContent>
        <TabsContent value="users">
          <UserManagement />
        </TabsContent>
        <TabsContent value="activity">
          <ActivityLogs />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Settings;
