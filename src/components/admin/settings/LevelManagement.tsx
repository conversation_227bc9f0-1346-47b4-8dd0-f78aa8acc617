import { useState, useEffect, useRef, useMemo } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { Trash2, AlertCircle, Pencil, Filter } from 'lucide-react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { getLevels, createLevel, deleteLevel, updateLevel } from '@/api/levels';
import { getCourses } from '@/api/courses';
import { Label } from "@/components/ui/label";

interface Level {
  id: string;
  name: string;
  code: string;
  description?: string;
  course_id: string;
  course?: {
    name: string;
  } | null;
  order?: number;
  status: 'active' | 'inactive';
  created_at?: any;
  updated_at?: any;
}

const defaultLevel = {
  name: '',
  code: '',
  description: '',
  course_id: '',
  status: 'active' as const,
  order: 0
};

const LevelManagement = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [newLevel, setNewLevel] = useState(defaultLevel);
  const [levelToDelete, setLevelToDelete] = useState<Level | null>(null);
  const [levelToEdit, setLevelToEdit] = useState<Level | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isOffline, setIsOffline] = useState<boolean>(!navigator.onLine);
  const pendingUpdatesRef = useRef<{id: string, data: Partial<Level>}[]>([]);
  const [courseFilter, setCourseFilter] = useState<string>('all');

  const { data: levels = [], isLoading: isLoadingLevels } = useQuery({
    queryKey: ['levels'],
    queryFn: getLevels
  });

  const { data: courses = [], isLoading: isLoadingCourses } = useQuery({
    queryKey: ['courses'],
    queryFn: getCourses
  });

  // Debug useEffect to monitor levelToEdit changes
  useEffect(() => {
    console.log('levelToEdit state changed:', levelToEdit);
  }, [levelToEdit]);

  // Track online/offline status
  useEffect(() => {
    const handleOnline = () => {
      console.log('App is online, attempting to process pending level updates');
      setIsOffline(false);
      processPendingUpdates();
    };
    
    const handleOffline = () => {
      console.log('App is offline');
      setIsOffline(true);
      toast({
        title: "You're offline",
        description: "Changes will be saved when your connection is restored",
        variant: "destructive",
      });
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [toast]);

  // Function to process any pending updates when online
  const processPendingUpdates = async () => {
    if (pendingUpdatesRef.current.length === 0) return;
    
    toast({
      title: "Connection restored",
      description: `Attempting to process ${pendingUpdatesRef.current.length} pending level updates`,
    });
    
    for (const update of pendingUpdatesRef.current) {
      try {
        await updateLevel(update.id, update.data);
        
        // Remove from pending queue
        pendingUpdatesRef.current = pendingUpdatesRef.current.filter(
          item => item.id !== update.id || JSON.stringify(item.data) !== JSON.stringify(update.data)
        );
        
        toast({
          title: "Update applied",
          description: `Level ${update.data.name || update.id} was updated successfully`,
        });
      } catch (error) {
        console.error('Failed to process pending level update:', error);
      }
    }
    
    // Refresh the data
    queryClient.invalidateQueries({ queryKey: ['levels'] });
  };

  const handleInputChange = (field: string, value: string | number) => {
    setNewLevel(prev => ({ ...prev, [field]: value }));
  };
  
  const handleEditInputChange = (field: string, value: string | number) => {
    setLevelToEdit(prev => prev ? { ...prev, [field]: value } : null);
  };

  const handleAddLevel = async () => {
    if (!newLevel.name || !newLevel.code || !newLevel.course_id) {
      toast({
        title: "Error",
        description: "Level name, code, and course are required",
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);
    
    try {
      await createLevel(newLevel);
      
      // Reset form
      setNewLevel(defaultLevel);
      
      // Refresh levels list
      queryClient.invalidateQueries({ queryKey: ['levels'] });
      
      toast({
        title: "Success",
        description: "Level added successfully",
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to add level",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleUpdateLevel = async () => {
    if (!levelToEdit || !levelToEdit.id) {
      console.error('Cannot update level: levelToEdit or levelToEdit.id is null');
      toast({
        title: "Error",
        description: "Level not found",
        variant: "destructive"
      });
      return;
    }

    if (!levelToEdit.name || !levelToEdit.code || !levelToEdit.course_id) {
      console.error('Cannot update level: Required fields missing', {
        name: levelToEdit.name,
        code: levelToEdit.code,
        course_id: levelToEdit.course_id
      });
      toast({
        title: "Error",
        description: "Level name, code, and course are required",
        variant: "destructive"
      });
      return;
    }

    console.log('Starting level update for:', levelToEdit);
    setIsSubmitting(true);
    
    // Prepare the data to update
    const levelData = {
      name: levelToEdit.name,
      code: levelToEdit.code,
      description: levelToEdit.description,
      course_id: levelToEdit.course_id,
      order: levelToEdit.order,
      status: levelToEdit.status
    };
    
    try {
      // If offline, store the update for later
      if (isOffline) {
        pendingUpdatesRef.current.push({
          id: levelToEdit.id,
          data: levelData
        });
        
        toast({
          title: "Offline mode",
          description: "Your level changes will be applied when you're back online",
        });
        
        // Still update the UI optimistically
        setLevelToEdit(null);
        
        // Update local cache optimistically
        queryClient.setQueryData(['levels'], (oldData: Level[] = []) => {
          return oldData.map(level => 
            level.id === levelToEdit.id 
              ? { ...level, ...levelData } 
              : level
          );
        });
        
        return;
      }
      
      // If online, proceed with normal update
      console.log('Calling updateLevel API with:', levelToEdit.id, levelData);
      
      await updateLevel(levelToEdit.id, levelData);
      
      console.log('Level update successful');
      
      // Reset form
      setLevelToEdit(null);
      
      // Refresh levels list
      queryClient.invalidateQueries({ queryKey: ['levels'] });
      
      toast({
        title: "Success",
        description: "Level updated successfully",
      });
    } catch (error: any) {
      console.error('Level update failed:', error);
      
      // Check if this is a connectivity issue
      if (error.message?.includes('network') || error.code === 'unavailable' || !navigator.onLine) {
        setIsOffline(true);
        
        // Store for later
        pendingUpdatesRef.current.push({
          id: levelToEdit.id,
          data: levelData
        });
        
        toast({
          title: "Connection issue",
          description: "Your level changes will be saved when connection is restored",
        });
        
        // Update UI optimistically
        setLevelToEdit(null);
      } else {
        toast({
          title: "Error",
          description: error.message || "Failed to update level",
          variant: "destructive"
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteLevel = async (level: Level) => {
    setLevelToDelete(level);
  };

  const confirmDeleteLevel = async () => {
    if (!levelToDelete) return;
    
    try {
      await deleteLevel(levelToDelete.id);
      
      // Refresh levels list
      queryClient.invalidateQueries({ queryKey: ['levels'] });
      
      toast({
        title: "Success",
        description: "Level deleted successfully",
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to delete level",
        variant: "destructive"
      });
    } finally {
      setLevelToDelete(null);
    }
  };

  // Function to reset the course filter
  const resetFilter = () => {
    setCourseFilter('all');
  };

  // Filter levels by selected course
  const filteredLevels = useMemo(() => {
    if (!courseFilter || courseFilter === 'all') return levels;
    return levels.filter(level => level.course_id === courseFilter);
  }, [levels, courseFilter]);

  if (isLoadingLevels || isLoadingCourses) {
    return <div>Loading...</div>;
  }

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Level Management</h1>
      
      <Card className="p-6">
        <h2 className="text-xl font-semibold mb-4">
          {levelToEdit ? 'Edit Level' : 'Add New Level'}
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="name">Level Name</Label>
            <Input
              id="name"
              value={levelToEdit ? levelToEdit.name : newLevel.name}
              onChange={(e) => levelToEdit 
                ? handleEditInputChange('name', e.target.value)
                : handleInputChange('name', e.target.value)
              }
              placeholder="Enter level name"
            />
          </div>
          
          <div>
            <Label htmlFor="code">Level Code</Label>
            <Input
              id="code"
              value={levelToEdit ? levelToEdit.code : newLevel.code}
              onChange={(e) => levelToEdit
                ? handleEditInputChange('code', e.target.value)
                : handleInputChange('code', e.target.value)
              }
              placeholder="Enter level code"
            />
          </div>
          
          <div>
            <Label htmlFor="course">Course</Label>
            <Select
              value={levelToEdit ? levelToEdit.course_id : newLevel.course_id}
              onValueChange={(value) => levelToEdit
                ? handleEditInputChange('course_id', value)
                : handleInputChange('course_id', value)
              }
            >
              <SelectTrigger id="course">
                <SelectValue placeholder="Select course" />
              </SelectTrigger>
              <SelectContent>
                {courses.map((course) => (
                  <SelectItem key={course.id} value={course.id}>
                    {course.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <Label htmlFor="order">Order</Label>
            <Input
              id="order"
              type="number"
              value={levelToEdit ? levelToEdit.order : newLevel.order}
              onChange={(e) => levelToEdit
                ? handleEditInputChange('order', parseInt(e.target.value))
                : handleInputChange('order', parseInt(e.target.value))
              }
              placeholder="Enter display order"
            />
          </div>
          
          <div>
            <Label htmlFor="status">Status</Label>
            <Select
              value={levelToEdit ? levelToEdit.status : newLevel.status}
              onValueChange={(value) => levelToEdit
                ? handleEditInputChange('status', value as 'active' | 'inactive')
                : handleInputChange('status', value as 'active' | 'inactive')
              }
            >
              <SelectTrigger id="status">
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        
        <div className="mt-4">
          <Label htmlFor="description">Description</Label>
          <Input
            id="description"
            value={levelToEdit ? levelToEdit.description : newLevel.description}
            onChange={(e) => levelToEdit
              ? handleEditInputChange('description', e.target.value)
              : handleInputChange('description', e.target.value)
            }
            placeholder="Enter level description"
          />
        </div>
        
        <div className="mt-4 flex gap-2">
          {levelToEdit ? (
            <>
              <Button 
                onClick={handleUpdateLevel}
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Updating...' : 'Update Level'}
              </Button>
              <Button 
                variant="secondary"
                onClick={() => setLevelToEdit(null)}
              >
                Cancel
              </Button>
            </>
          ) : (
            <Button 
              onClick={handleAddLevel}
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Adding...' : 'Add Level'}
            </Button>
          )}
        </div>
      </Card>
      
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold">Existing Levels</h2>
          <div className="flex items-center space-x-2">
            <Select
              value={courseFilter}
              onValueChange={(value) => setCourseFilter(value)}
            >
              <SelectTrigger className="w-[200px]">
                <SelectValue placeholder="Filter by course" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Courses</SelectItem>
                {courses.map((course) => (
                  <SelectItem key={course.id} value={course.id}>
                    {course.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {courseFilter && courseFilter !== 'all' && (
              <Button variant="outline" size="sm" onClick={resetFilter}>
                Clear Filter
              </Button>
            )}
          </div>
        </div>
        <div className="space-y-4">
          {filteredLevels.length === 0 ? (
            <p>No levels found.</p>
          ) : (
            filteredLevels.map((level) => (
              <div key={level.id} className="flex items-center justify-between p-4 border rounded">
                <div>
                  <h3 className="font-medium">{level.name} ({level.code})</h3>
                  <p className="text-sm text-gray-500">{level.description}</p>
                  <p className="text-sm">Course: {level.course?.name || 'Unknown'}</p>
                  <div className="flex items-center mt-1">
                    <span className={`px-2 py-0.5 text-xs rounded ${
                      level.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {level.status}
                    </span>
                  </div>
                </div>
                
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      console.log('Edit button clicked for level:', level);
                      setLevelToEdit(level);
                      console.log('levelToEdit after setting:', level);
                    }}
                  >
                    <Pencil className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => handleDeleteLevel(level)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))
          )}
        </div>
      </Card>
      
      <AlertDialog open={!!levelToDelete} onOpenChange={(open) => !open && setLevelToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the level "{levelToDelete?.name}".
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDeleteLevel}>Delete</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default LevelManagement;
