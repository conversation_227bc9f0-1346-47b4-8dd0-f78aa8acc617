import { useState, useEffect, useRef } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { useQuery, useQueryClient } from '@tanstack/react-query';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { DatePicker } from "@/components/ui/date-picker";
import { Switch } from "@/components/ui/switch";
import { Trash2, Pencil } from "lucide-react";
import { Label } from "@/components/ui/label";
import { createCourse, deleteCourse, updateCourse } from '@/api/courses';
import { getCourses } from '@/api/courses';
import { logActivity } from '@/utils/activity-logger';

interface Course {
  id?: string;
  name: string;
  description: string;
  code: string;
  status: 'active' | 'inactive';
  duration: string;
  fee: number;
}

const defaultCourse: Course = {
  name: '',
  description: '',
  code: '',
  status: 'active',
  duration: '12 months',
  fee: 0
};

const CourseManagement = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [newCourse, setNewCourse] = useState<Course>(defaultCourse);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [courseToEdit, setCourseToEdit] = useState<Course | null>(null);
  const [isOffline, setIsOffline] = useState<boolean>(!navigator.onLine);
  const pendingUpdatesRef = useRef<{id: string, data: Partial<Course>}[]>([]);

  const { data: courses = [], isLoading, error } = useQuery({
    queryKey: ['courses'],
    queryFn: getCourses
  });

  // Debug useEffect to monitor courseToEdit changes
  useEffect(() => {
    console.log('courseToEdit state changed:', courseToEdit);
  }, [courseToEdit]);

  // Track online/offline status
  useEffect(() => {
    const handleOnline = () => {
      console.log('App is online, attempting to process pending updates');
      setIsOffline(false);
      processPendingUpdates();
    };
    
    const handleOffline = () => {
      console.log('App is offline');
      setIsOffline(true);
      toast({
        title: "You're offline",
        description: "Changes will be saved when your connection is restored",
        variant: "destructive",
      });
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [toast]);

  // Function to process any pending updates when online
  const processPendingUpdates = async () => {
    if (pendingUpdatesRef.current.length === 0) return;
    
    toast({
      title: "Connection restored",
      description: `Attempting to process ${pendingUpdatesRef.current.length} pending updates`,
    });
    
    for (const update of pendingUpdatesRef.current) {
      try {
        await updateCourse(update.id, update.data);
        
        // Remove from pending queue
        pendingUpdatesRef.current = pendingUpdatesRef.current.filter(
          item => item.id !== update.id || JSON.stringify(item.data) !== JSON.stringify(update.data)
        );
        
        toast({
          title: "Update applied",
          description: `Course ${update.data.name || update.id} was updated successfully`,
        });
      } catch (error) {
        console.error('Failed to process pending update:', error);
      }
    }
    
    // Refresh the data
    queryClient.invalidateQueries({ queryKey: ['courses'] });
  };

  const handleInputChange = (field: keyof Course, value: string | number | boolean) => {
    setNewCourse(prev => ({ ...prev, [field]: value }));
  };

  const handleEditInputChange = (field: keyof Course, value: string | number | boolean) => {
    setCourseToEdit(prev => prev ? { ...prev, [field]: value } : null);
  };

  const handleAddCourse = async () => {
    if (!newCourse.name || !newCourse.code) {
      toast({
        title: "Error",
        description: "Course name and code are required",
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);
    
    try {
      await createCourse(newCourse);
      
      // Reset form
      setNewCourse(defaultCourse);
      
      // Refresh courses list
      queryClient.invalidateQueries({ queryKey: ['courses'] });
      
      toast({
        title: "Success",
        description: "Course added successfully",
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to add course",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleUpdateCourse = async () => {
    if (!courseToEdit || !courseToEdit.id) {
      console.error('Cannot update course: courseToEdit or courseToEdit.id is null', { courseToEdit });
      toast({
        title: "Error",
        description: "Course not found",
        variant: "destructive"
      });
      return;
    }
    
    console.log('Starting course update for:', courseToEdit);
    setIsSubmitting(true);
    
    // Prepare the data to update
    const courseData = {
      name: courseToEdit.name,
      code: courseToEdit.code,
      description: courseToEdit.description,
      duration: courseToEdit.duration,
      fee: courseToEdit.fee,
      status: courseToEdit.status
    };
    
    try {
      // If offline, store the update for later
      if (isOffline) {
        pendingUpdatesRef.current.push({
          id: courseToEdit.id,
          data: courseData
        });
        
        toast({
          title: "Offline mode",
          description: "Your changes will be applied when you're back online",
        });
        
        // Still update the UI optimistically
        setCourseToEdit(null);
        
        // Update local cache optimistically
        queryClient.setQueryData(['courses'], (oldData: Course[] = []) => {
          return oldData.map(course => 
            course.id === courseToEdit.id 
              ? { ...course, ...courseData } 
              : course
          );
        });
        
        return;
      }
      
      // If online, proceed with normal update
      console.log('Calling updateCourse API with ID:', courseToEdit.id);
      console.log('and data:', courseData);
      
      await updateCourse(courseToEdit.id, courseData);
      
      console.log('Course update successful');
      
      // Cancel edit mode
      setCourseToEdit(null);
      
      // Refresh courses list
      queryClient.invalidateQueries({ queryKey: ['courses'] });
      
      toast({
        title: "Success",
        description: "Course updated successfully",
      });
    } catch (error: any) {
      console.error('Course update failed:', error);
      
      // Check if this is a connectivity issue
      if (error.message?.includes('network') || error.code === 'unavailable' || !navigator.onLine) {
        setIsOffline(true);
        
        // Store for later
        pendingUpdatesRef.current.push({
          id: courseToEdit.id,
          data: courseData
        });
        
        toast({
          title: "Connection issue",
          description: "Your changes will be saved when connection is restored",
        });
        
        // Update UI optimistically
        setCourseToEdit(null);
      } else {
        // Log detailed error information
        if (error.response) {
          console.error('Error response data:', error.response.data);
          console.error('Error response status:', error.response.status);
        }
        
        toast({
          title: "Error",
          description: error.message || "Failed to update course",
          variant: "destructive"
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteCourse = async (courseId: string) => {
    if (!confirm("Are you sure you want to delete this course?")) return;
    
    try {
      await deleteCourse(courseId);
      
      // Refresh courses list
      queryClient.invalidateQueries({ queryKey: ['courses'] });
      
      toast({
        title: "Success",
        description: "Course deleted successfully",
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to delete course",
        variant: "destructive"
      });
    }
  };

  const handleUpdateFee = async (courseId: string, newFee: number) => {
    try {
      if (isNaN(newFee) || newFee < 0) {
        toast({
          title: "Error",
          description: "Please enter a valid fee amount",
          variant: "destructive"
        });
        return;
      }

      console.log(`Updating fee for course ${courseId} to ${newFee}`);
      
      await updateCourse(courseId, { fee: newFee });
      
      // Log the activity
      await logActivity('course_fee_updated', {
        courseId,
        newFee,
        previousFee: courses.find(c => c.id === courseId)?.fee
      });
      
      // Refresh courses list
      queryClient.invalidateQueries({ queryKey: ['courses'] });
      
      toast({
        title: "Success",
        description: "Course fee updated successfully",
      });
    } catch (error: any) {
      console.error('Error updating course fee:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to update course fee",
        variant: "destructive"
      });
    }
  };

  if (isLoading) {
    return <div>Loading courses...</div>;
  }

  if (error) {
    return <div>Error loading courses: {(error as Error).message}</div>;
  }

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Course Management</h1>
      
      <Card className="p-6">
        <h2 className="text-xl font-semibold mb-4">
          {courseToEdit ? 'Edit Course' : 'Add New Course'}
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="name">Course Name</Label>
            <Input
              id="name"
              value={courseToEdit ? courseToEdit.name : newCourse.name}
              onChange={(e) => courseToEdit 
                ? handleEditInputChange('name', e.target.value)
                : handleInputChange('name', e.target.value)
              }
              placeholder="Enter course name"
            />
          </div>
          
          <div>
            <Label htmlFor="code">Course Code</Label>
            <Input
              id="code"
              value={courseToEdit ? courseToEdit.code : newCourse.code}
              onChange={(e) => courseToEdit
                ? handleEditInputChange('code', e.target.value)
                : handleInputChange('code', e.target.value)
              }
              placeholder="Enter course code"
            />
          </div>
          
          <div>
            <Label htmlFor="duration">Duration</Label>
            <Input
              id="duration"
              value={courseToEdit ? courseToEdit.duration : newCourse.duration}
              onChange={(e) => courseToEdit
                ? handleEditInputChange('duration', e.target.value)
                : handleInputChange('duration', e.target.value)
              }
              placeholder="e.g., 12 months"
            />
          </div>
          
          <div>
            <Label htmlFor="fee">Course Fee</Label>
            <Input
              id="fee"
              type="number"
              value={courseToEdit ? courseToEdit.fee : newCourse.fee}
              onChange={(e) => courseToEdit
                ? handleEditInputChange('fee', Number(e.target.value))
                : handleInputChange('fee', Number(e.target.value))
              }
              placeholder="Enter course fee"
            />
          </div>
          
          <div>
            <Label htmlFor="status">Status</Label>
            <Select
              value={courseToEdit ? courseToEdit.status : newCourse.status}
              onValueChange={(value) => courseToEdit
                ? handleEditInputChange('status', value as 'active' | 'inactive')
                : handleInputChange('status', value as 'active' | 'inactive')
              }
            >
              <SelectTrigger id="status">
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        
        <div className="mt-4">
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            value={courseToEdit ? courseToEdit.description : newCourse.description}
            onChange={(e) => courseToEdit
              ? handleEditInputChange('description', e.target.value)
              : handleInputChange('description', e.target.value)
            }
            placeholder="Enter course description"
            rows={3}
          />
        </div>
        
        <div className="mt-4 flex gap-2">
          {courseToEdit ? (
            <>
              <Button 
                onClick={handleUpdateCourse}
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Updating...' : 'Update Course'}
              </Button>
              <Button 
                variant="secondary"
                onClick={() => setCourseToEdit(null)}
              >
                Cancel
              </Button>
            </>
          ) : (
            <Button 
              onClick={handleAddCourse}
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Adding...' : 'Add Course'}
            </Button>
          )}
        </div>
      </Card>
      
      <Card className="p-6">
        <h2 className="text-xl font-semibold mb-4">Existing Courses</h2>
        <div className="space-y-4">
          {courses.length === 0 ? (
            <p>No courses found.</p>
          ) : (
            courses.map((course) => (
              <div key={course.id} className="flex items-center justify-between p-4 border rounded">
                <div>
                  <h3 className="font-medium">{course.name} ({course.code})</h3>
                  <p className="text-sm text-gray-500">{course.description}</p>
                  <p className="text-sm">Duration: {course.duration}</p>
                  <div className="flex items-center mt-1">
                    <span className={`px-2 py-0.5 text-xs rounded ${
                      course.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {course.status}
                    </span>
                  </div>
                </div>
                
                <div className="flex items-center space-x-4">
                  <div>
                    <Label htmlFor={`fee-${course.id}`} className="text-sm">Fee (Le)</Label>
                    <div className="flex items-center">
                      <Input
                        id={`fee-${course.id}`}
                        type="number"
                        value={course.fee}
                        onChange={(e) => handleUpdateFee(course.id!, Number(e.target.value))}
                        className="w-32 h-8"
                        min="0"
                        step="0.01"
                      />
                      <span className="ml-2 text-sm text-gray-500">
                        {course.fee.toLocaleString('en-US', {
                          style: 'currency',
                          currency: 'SLL'
                        })}
                      </span>
                    </div>
                  </div>
                  
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        console.log('Edit button clicked for course:', course);
                        setCourseToEdit(course);
                        console.log('courseToEdit after setting:', course);
                      }}
                    >
                      <Pencil className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => handleDeleteCourse(course.id!)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </Card>
    </div>
  );
};

export default CourseManagement;
