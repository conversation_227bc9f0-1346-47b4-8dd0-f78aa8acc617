import { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Tabs,
  TabsContent,
  Ta<PERSON>List,
  TabsTrigger,
} from "@/components/ui/tabs";
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  getTeachers, 
  createTeacher, 
  updateTeacher, 
  deleteTeacher, 
  assignCourse, 
  unassignCourse, 
  assignLevel, 
  unassignLevel, 
  getTeacherById
} from '@/api/teachers';
import { getCourses } from '@/api/courses';
import { getLevels } from '@/api/levels';
import type { Teacher } from '@/types/user';
import { Loader2, UserPlus, Trash2, PencilIcon, MoreHorizontal, BookOpen, Layers, Grid, List, Mail, Phone } from 'lucide-react';

const defaultTeacher: Omit<Teacher, 'id' | 'created_at' | 'updated_at'> = {
  name: '',
  email: '',
  password: '',
  role: 'teacher',
  specialization: '',
  bio: '',
  contact_number: '',
  assigned_courses: [],
  assigned_levels: []
};

const TeacherManagement = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [newTeacher, setNewTeacher] = useState(defaultTeacher);
  const [isAddingTeacher, setIsAddingTeacher] = useState(false);
  const [teacherToEdit, setTeacherToEdit] = useState<Teacher | null>(null);
  const [isAssigningCourse, setIsAssigningCourse] = useState(false);
  const [isAssigningLevel, setIsAssigningLevel] = useState(false);
  const [selectedTeacher, setSelectedTeacher] = useState<Teacher | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [teacherToDelete, setTeacherToDelete] = useState<Teacher | null>(null);
  const [isCreatingTeacher, setIsCreatingTeacher] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortConfig, setSortConfig] = useState<{
    sortBy: 'name' | 'email' | 'created_at';
    sortDirection: 'asc' | 'desc';
  }>({
    sortBy: 'name',
    sortDirection: 'asc'
  });
  const [isUpdatingTeacher, setIsUpdatingTeacher] = useState(false);
  const [summaryTab, setSummaryTab] = useState<'overview' | 'courses' | 'levels'>('overview');
  const [lastCreatedTeacherId, setLastCreatedTeacherId] = useState<string | null>(null);

  // Fetch teachers, courses, and levels
  const { 
    data: teachers = [], 
    isLoading: isLoadingTeachers, 
    refetch: refetchTeachers, 
    isFetching,
    isError,
    error: teachersError
  } = useQuery({
    queryKey: ['teachers', searchTerm, sortConfig],
    queryFn: () => {
      console.log('Fetching teachers with options:', { searchTerm, sortConfig });
      return getTeachers({
        searchTerm,
        sortBy: sortConfig.sortBy,
        sortDirection: sortConfig.sortDirection
      });
    },
    staleTime: 0, // Ensure data is always considered stale
    refetchOnWindowFocus: true, // Refetch when the window regains focus
    retry: 3,
    retryDelay: 1000,
    onSuccess: (data) => {
      console.log('Successfully fetched teachers:', data.length);
      // If we just created a teacher, check if it's in the results
      if (lastCreatedTeacherId) {
        const found = data.some(teacher => teacher.id === lastCreatedTeacherId);
        console.log(`Teacher with ID ${lastCreatedTeacherId} found in results: ${found}`);
        if (found) {
          setLastCreatedTeacherId(null); // Reset once found
        }
      }
    },
    onError: (err: any) => {
      console.error("Error fetching teachers:", err);
      setError(err?.message || "Failed to load teachers. Please refresh the page.");
    }
  });

  const { data: courses = [], isLoading: isLoadingCourses } = useQuery({
    queryKey: ['courses'],
    queryFn: getCourses
  });

  const { data: levels = [], isLoading: isLoadingLevels } = useQuery({
    queryKey: ['levels'],
    queryFn: getLevels
  });

  // Add a new query for getting a specific teacher when editing
  const { 
    refetch: refetchTeacherDetails, 
    isLoading: isLoadingTeacherDetails 
  } = useQuery({
    queryKey: ['teacher-details', selectedTeacher?.id],
    queryFn: () => selectedTeacher ? getTeacherById(selectedTeacher.id) : null,
    enabled: false, // Don't run automatically
    onSuccess: (data) => {
      if (data) {
        setTeacherToEdit(data);
      }
    },
    onError: (err: any) => {
      toast({
        title: "Error",
        description: "Failed to fetch teacher details. Please try again.",
        variant: "destructive",
      });
    }
  });

  // Add effect to poll for updates if needed
  useEffect(() => {
    // Poll if we've recently created a teacher but it's not showing up yet
    let interval: NodeJS.Timeout | null = null;
    
    if (lastCreatedTeacherId) {
      console.log(`Polling for recently created teacher with ID: ${lastCreatedTeacherId}`);
      interval = setInterval(() => {
        console.log("Polling for teacher updates...");
        refetchTeachers();
      }, 2000); // Poll more frequently (every 2 seconds)
      
      // Stop polling after 30 seconds to prevent infinite polling
      const timeout = setTimeout(() => {
        if (interval) {
          clearInterval(interval);
          setLastCreatedTeacherId(null);
          console.log("Stopped polling after timeout");
        }
      }, 30000);
      
      return () => {
        if (interval) clearInterval(interval);
        clearTimeout(timeout);
      };
    }
    
    // Also keep the original polling for when there are no teachers
    if (teachers && teachers.length === 0 && !isError && !isLoadingTeachers) {
      interval = setInterval(() => {
        console.log("Polling for initial teacher data...");
        refetchTeachers();
      }, 5000);
    }
    
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [teachers, refetchTeachers, isError, isLoadingTeachers, lastCreatedTeacherId]);

  // Mutations
  const createTeacherMutation = useMutation({
    mutationFn: createTeacher,
    onSuccess: async (createdTeacher) => {
      console.log('Teacher created successfully:', createdTeacher);
      
      // Store the ID of the created teacher to track it
      setLastCreatedTeacherId(createdTeacher.id);
      
      // Force an immediate refresh of teachers data
      await queryClient.invalidateQueries({ queryKey: ['teachers'] });
      
      // Immediately try to fetch the updated list
      await refetchTeachers();
      
      toast({
        title: "Success",
        description: "Teacher created successfully. Refreshing the list...",
      });
      
      setNewTeacher(defaultTeacher);
      setIsAddingTeacher(false);
    },
    onError: (error: any) => {
      // Error is handled in handleCreateTeacher
      console.error("Error in createTeacherMutation:", error);
    }
  });

  const updateTeacherMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<Teacher> }) =>
      updateTeacher(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teachers'] });
      toast({
        title: "Success",
        description: "Teacher updated successfully",
      });
      setTeacherToEdit(null);
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update teacher",
        variant: "destructive",
      });
    }
  });

  const deleteTeacherMutation = useMutation({
    mutationFn: deleteTeacher,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teachers'] });
      toast({
        title: "Success",
        description: "Teacher deleted successfully",
      });
      setTeacherToDelete(null);
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to delete teacher",
        variant: "destructive",
      });
    }
  });

  const assignCourseMutation = useMutation({
    mutationFn: ({ teacherId, courseId }: { teacherId: string; courseId: string }) =>
      assignCourse(teacherId, courseId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teachers'] });
      toast({
        title: "Success",
        description: "Course assigned successfully",
      });
      setIsAssigningCourse(false);
    },
    onError: (error: any) => {
      console.error("Error assigning course:", error);
      toast({
        title: "Error",
        description: error?.message || "Failed to assign course. Please try again.",
        variant: "destructive",
      });
      setIsAssigningCourse(false);
    }
  });

  const assignLevelMutation = useMutation({
    mutationFn: ({ teacherId, levelId }: { teacherId: string; levelId: string }) =>
      assignLevel(teacherId, levelId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teachers'] });
      toast({
        title: "Success",
        description: "Level assigned successfully",
      });
      setIsAssigningLevel(false);
    },
    onError: (error: any) => {
      console.error("Error assigning level:", error);
      toast({
        title: "Error",
        description: error?.message || "Failed to assign level. Please try again.",
        variant: "destructive",
      });
      setIsAssigningLevel(false);
    }
  });

  const unassignCourseMutation = useMutation({
    mutationFn: ({ teacherId, courseId }: { teacherId: string; courseId: string }) =>
      unassignCourse(teacherId, courseId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teachers'] });
      toast({
        title: "Success",
        description: "Course unassigned successfully",
      });
    },
    onError: (error: any) => {
      console.error("Error unassigning course:", error);
      toast({
        title: "Error",
        description: error?.message || "Failed to unassign course. Please try again.",
        variant: "destructive",
      });
    }
  });

  const unassignLevelMutation = useMutation({
    mutationFn: ({ teacherId, levelId }: { teacherId: string; levelId: string }) =>
      unassignLevel(teacherId, levelId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teachers'] });
      toast({
        title: "Success",
        description: "Level unassigned successfully",
      });
    },
    onError: (error: any) => {
      console.error("Error unassigning level:", error);
      toast({
        title: "Error",
        description: error?.message || "Failed to unassign level. Please try again.",
        variant: "destructive",
      });
    }
  });

  const handleCreateTeacher = async () => {
    if (!newTeacher.name || !newTeacher.email || !newTeacher.password) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }
    
    setIsCreatingTeacher(true);
    
    try {
      console.log('Creating teacher with data:', newTeacher);
      await createTeacherMutation.mutateAsync(newTeacher);
      // The success callback in the mutation will handle the rest
    } catch (error: any) {
      console.error("Error in handleCreateTeacher:", error);
      
      // Specific error handling for email already in use
      if (error?.message?.includes('auth/email-already-in-use')) {
        toast({
          title: "Email Already in Use",
          description: "This email address is already registered. Please use a different email.",
          variant: "destructive",
        });
      } else {
        toast({
          title: "Error",
          description: error?.message || "Failed to create teacher. Please try again.",
          variant: "destructive",
        });
      }
    } finally {
      setIsCreatingTeacher(false);
    }
  };

  const handleUpdateTeacher = async () => {
    if (!teacherToEdit || !teacherToEdit.id) return;
    
    setIsUpdatingTeacher(true);
    try {
      await updateTeacherMutation.mutateAsync({
        id: teacherToEdit.id,
        data: teacherToEdit
      });
      setIsUpdatingTeacher(false);
    } catch (error) {
      setIsUpdatingTeacher(false);
      // Error is handled in the mutation's onError
    }
  };

  const handleDeleteTeacher = () => {
    if (!teacherToDelete) return;
    deleteTeacherMutation.mutate(teacherToDelete.id);
  };

  const handleAssignCourse = (courseId: string) => {
    if (!selectedTeacher) return;
    assignCourseMutation.mutate({
      teacherId: selectedTeacher.id,
      courseId
    });
  };

  const handleAssignLevel = (levelId: string) => {
    if (!selectedTeacher) return;
    assignLevelMutation.mutate({
      teacherId: selectedTeacher.id,
      levelId
    });
  };

  const handleUnassignCourse = (teacherId: string, courseId: string) => {
    unassignCourseMutation.mutate({ teacherId, courseId });
  };

  const handleUnassignLevel = (teacherId: string, levelId: string) => {
    unassignLevelMutation.mutate({ teacherId, levelId });
  };

  // Helper function to get course names from IDs
  const getCourseNames = (courseIds: string[] = []) => {
    return courseIds.map(id => {
      const course = courses.find(c => c.id === id);
      return course ? course.name : 'Unknown Course';
    }).join(', ');
  };

  // Helper function to get level names from IDs
  const getLevelNames = (levelIds: string[] = []) => {
    return levelIds.map(id => {
      const level = levels.find(l => l.id === id);
      return level ? level.name : 'Unknown Level';
    }).join(', ');
  };

  // Add sort handler
  const handleSort = (field: 'name' | 'email' | 'created_at') => {
    setSortConfig(prev => ({
      sortBy: field,
      sortDirection: prev.sortBy === field && prev.sortDirection === 'asc' ? 'desc' : 'asc'
    }));
  };

  // Add search handler
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // Update the edit teacher handler to fetch the most recent data
  const handleEditTeacher = (teacher: Teacher) => {
    setSelectedTeacher(teacher);
    setTeacherToEdit(teacher); // Set initial data from the grid/list
    refetchTeacherDetails(); // Fetch the most up-to-date data
  };

  // Analysis helpers
  const getTeachersByCourse = () => {
    const courseMap = new Map<string, { id: string, name: string, count: number, teachers: Teacher[] }>();
    
    // Initialize with all courses
    courses.forEach(course => {
      courseMap.set(course.id, { 
        id: course.id, 
        name: course.name, 
        count: 0, 
        teachers: [] 
      });
    });
    
    // Count teachers assigned to each course
    teachers.forEach(teacher => {
      if (teacher.assigned_courses) {
        teacher.assigned_courses.forEach(courseId => {
          const courseData = courseMap.get(courseId);
          if (courseData) {
            courseData.count++;
            courseData.teachers.push(teacher);
          }
        });
      }
    });
    
    return Array.from(courseMap.values())
      .sort((a, b) => b.count - a.count);
  };
  
  const getTeachersByLevel = () => {
    const levelMap = new Map<string, { id: string, name: string, count: number, teachers: Teacher[] }>();
    
    // Initialize with all levels
    levels.forEach(level => {
      levelMap.set(level.id, { 
        id: level.id, 
        name: level.name, 
        count: 0, 
        teachers: [] 
      });
    });
    
    // Count teachers assigned to each level
    teachers.forEach(teacher => {
      if (teacher.assigned_levels) {
        teacher.assigned_levels.forEach(levelId => {
          const levelData = levelMap.get(levelId);
          if (levelData) {
            levelData.count++;
            levelData.teachers.push(teacher);
          }
        });
      }
    });
    
    return Array.from(levelMap.values())
      .sort((a, b) => b.count - a.count);
  };

  const teachersByCourse = getTeachersByCourse();
  const teachersByLevel = getTeachersByLevel();

  if (isError) {
    // Extract the index URL from the error message if it exists
    // Firebase console URL removed - using PHP backend
    
    // Function to open the Firebase console to create the index
    const openFirebaseConsole = () => {
      if (indexUrl) {
        window.open(indexUrl, '_blank');
      }
    };
    
    return (
      <div className="flex flex-col items-center justify-center p-8 space-y-4">
        <div className="text-red-500 text-lg font-semibold">Error loading teachers</div>
        <p className="text-gray-600">{error || "There was an error loading the teacher data."}</p>
        
        {/* Add specific guidance for Firebase index errors */}
        {teachersError && teachersError.message && teachersError.message.includes('requires an index') && (
          <div className="bg-amber-50 border border-amber-200 rounded-md p-4 max-w-2xl">
            <h3 className="text-amber-800 font-medium mb-2">Firebase Index Required</h3>
            <p className="text-amber-700 mb-3">
              This query requires a Firestore index to be created. This is a one-time setup needed for complex queries.
            </p>
            <div className="flex flex-col space-y-3">
              <Button 
                variant="default"
                onClick={openFirebaseConsole}
                className="bg-amber-100 hover:bg-amber-200 text-amber-800 font-medium"
              >
                <svg className="h-4 w-4 mr-2" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                  <polyline points="15 3 21 3 21 9"></polyline>
                  <line x1="10" y1="14" x2="21" y2="3"></line>
                </svg>
                Create Firebase Index
              </Button>
              <p className="text-xs text-amber-600">
                After creating the index, it may take a few minutes to become active. Refresh this page after creating the index.
              </p>
            </div>
          </div>
        )}
        
        <Button onClick={() => {
          setError(null);
          refetchTeachers();
        }}>
          <svg className="h-4 w-4 mr-2" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"></path>
            <path d="M21 3v5h-5"></path>
            <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"></path>
            <path d="M3 21v-5h5"></path>
          </svg>
          Try Again
        </Button>
      </div>
    );
  }

  if (isLoadingTeachers || isLoadingCourses || isLoadingLevels) {
    return (
      <div className="flex flex-col items-center justify-center p-8 space-y-4">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
        <p className="text-lg font-medium">Loading teacher data...</p>
        <p className="text-sm text-muted-foreground">This may take a moment</p>
      </div>
    );
  }

  const hasTeachers = teachers && teachers.length > 0;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Teacher Management</h2>
          <p className="text-muted-foreground">Manage teachers and their assignments</p>
        </div>
        <div className="flex items-center gap-4">
          <Button 
            variant="outline" 
            size="icon" 
            onClick={() => refetchTeachers()} 
            disabled={isFetching}
            title="Refresh teacher list"
          >
            <div className={`${isFetching ? 'animate-spin' : ''}`}>
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-refresh-cw">
                <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"></path>
                <path d="M21 3v5h-5"></path>
                <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"></path>
                <path d="M3 21v-5h5"></path>
              </svg>
            </div>
          </Button>
          <div className="flex items-center border rounded-md">
            <Button 
              variant={viewMode === 'grid' ? 'default' : 'ghost'} 
              size="sm"
              onClick={() => setViewMode('grid')}
              className="rounded-r-none"
            >
              <Grid className="h-4 w-4 mr-2" />
              Grid
            </Button>
            <Button 
              variant={viewMode === 'list' ? 'default' : 'ghost'} 
              size="sm"
              onClick={() => setViewMode('list')}
              className="rounded-l-none"
            >
              <List className="h-4 w-4 mr-2" />
              List
            </Button>
          </div>
          <div className="relative w-64">
            <Input
              placeholder="Search teachers..."
              value={searchTerm}
              onChange={handleSearch}
              className="pl-8"
            />
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 absolute left-2.5 top-2.5 text-gray-500"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
            {searchTerm && (
              <Button
                variant="ghost"
                size="icon"
                className="h-7 w-7 absolute right-1 top-1"
                onClick={() => setSearchTerm('')}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </Button>
            )}
          </div>
          <Button onClick={() => setIsAddingTeacher(true)}>
            <UserPlus className="h-4 w-4 mr-2" />
            Add Teacher
          </Button>
        </div>
      </div>

      {/* Show a refreshing indicator when fetching data */}
      {isFetching && !isLoadingTeachers && (
        <div className="flex items-center justify-center p-2 bg-blue-50 rounded-md mb-4">
          <Loader2 className="h-4 w-4 mr-2 animate-spin text-blue-500" />
          <p className="text-sm text-blue-500">Refreshing teacher data...</p>
        </div>
      )}

      {/* Teacher Summary */}
      <Tabs value={summaryTab} onValueChange={(value) => setSummaryTab(value as any)}>
        <TabsList className="mb-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="courses">By Course</TabsTrigger>
          <TabsTrigger value="levels">By Level</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Teachers</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{teachers.length}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Courses Covered</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {hasTeachers ? new Set(teachers.flatMap(t => t.assigned_courses || [])).size : 0}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Levels Covered</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {hasTeachers ? new Set(teachers.flatMap(t => t.assigned_levels || [])).size : 0}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="courses">
          <div className="rounded-md border">
            <Table>
              <TableCaption>Teachers assigned to courses</TableCaption>
              <TableHeader>
                <TableRow>
                  <TableHead>Course</TableHead>
                  <TableHead>Teachers Assigned</TableHead>
                  <TableHead className="w-[300px]">Teacher Distribution</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {teachersByCourse.map(course => (
                  <TableRow key={course.id}>
                    <TableCell className="font-medium">{course.name}</TableCell>
                    <TableCell>{course.count}</TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {course.teachers.slice(0, 3).map(teacher => (
                          <Badge key={teacher.id} variant="secondary">
                            {teacher.name}
                          </Badge>
                        ))}
                        {course.teachers.length > 3 && (
                          <Badge variant="outline">
                            +{course.teachers.length - 3} more
                          </Badge>
                        )}
                        {course.teachers.length === 0 && (
                          <span className="text-sm text-muted-foreground">No teachers assigned</span>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
                {teachersByCourse.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={3} className="text-center py-6 text-muted-foreground">
                      No courses available
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </TabsContent>
        
        <TabsContent value="levels">
          <div className="rounded-md border">
            <Table>
              <TableCaption>Teachers assigned to levels</TableCaption>
              <TableHeader>
                <TableRow>
                  <TableHead>Level</TableHead>
                  <TableHead>Teachers Assigned</TableHead>
                  <TableHead className="w-[300px]">Teacher Distribution</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {teachersByLevel.map(level => (
                  <TableRow key={level.id}>
                    <TableCell className="font-medium">{level.name}</TableCell>
                    <TableCell>{level.count}</TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {level.teachers.slice(0, 3).map(teacher => (
                          <Badge key={teacher.id} variant="secondary">
                            {teacher.name}
                          </Badge>
                        ))}
                        {level.teachers.length > 3 && (
                          <Badge variant="outline">
                            +{level.teachers.length - 3} more
                          </Badge>
                        )}
                        {level.teachers.length === 0 && (
                          <span className="text-sm text-muted-foreground">No teachers assigned</span>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
                {teachersByLevel.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={3} className="text-center py-6 text-muted-foreground">
                      No levels available
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </TabsContent>
      </Tabs>

      {/* Display search results message */}
      {searchTerm && (
        <div className="text-sm text-muted-foreground mb-2">
          {teachers.length === 0 ? (
            <span>No teachers found matching "{searchTerm}"</span>
          ) : (
            <span>Found {teachers.length} teacher{teachers.length !== 1 ? 's' : ''} matching "{searchTerm}"</span>
          )}
        </div>
      )}

      {/* Empty state message when no teachers exist */}
      {!hasTeachers && !searchTerm && (
        <div className="flex flex-col items-center justify-center p-8 space-y-4 bg-gray-50 rounded-lg border border-dashed border-gray-300">
          <div className="rounded-full bg-primary/10 p-3">
            <UserPlus className="h-6 w-6 text-primary" />
          </div>
          <h3 className="text-lg font-medium">No teachers added yet</h3>
          <p className="text-sm text-muted-foreground text-center max-w-md">
            Get started by adding your first teacher. Teachers can be assigned to courses and levels.
          </p>
          <Button onClick={() => setIsAddingTeacher(true)}>
            <UserPlus className="h-4 w-4 mr-2" />
            Add Your First Teacher
          </Button>
        </div>
      )}

      {/* Only show teacher list/grid if we have teachers */}
      {hasTeachers && (
        viewMode === 'grid' ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {teachers.map((teacher) => (
              <Card key={teacher.id}>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle>{teacher.name}</CardTitle>
                      <p className="text-sm text-muted-foreground">{teacher.email}</p>
                      {teacher.specialization && (
                        <Badge variant="secondary" className="mt-2">
                          {teacher.specialization}
                        </Badge>
                      )}
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent>
                        <DropdownMenuItem onClick={() => handleEditTeacher(teacher)}>
                          <PencilIcon className="h-4 w-4 mr-2" />
                          Edit Details
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => {
                          setSelectedTeacher(teacher);
                          setIsAssigningCourse(true);
                        }}>
                          <BookOpen className="h-4 w-4 mr-2" />
                          Assign Course
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => {
                          setSelectedTeacher(teacher);
                          setIsAssigningLevel(true);
                        }}>
                          <Layers className="h-4 w-4 mr-2" />
                          Assign Level
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem 
                          onClick={() => setTeacherToDelete(teacher)}
                          className="text-red-600"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete Teacher
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {teacher.contact_number && (
                      <div className="flex items-center text-sm">
                        <Phone className="h-4 w-4 mr-2 text-muted-foreground" />
                        {teacher.contact_number}
                      </div>
                    )}
                    {teacher.bio && (
                      <div className="text-sm text-muted-foreground">
                        {teacher.bio.length > 100 ? `${teacher.bio.substring(0, 100)}...` : teacher.bio}
                      </div>
                    )}
                    {teacher.assigned_courses && teacher.assigned_courses.length > 0 && (
                      <div>
                        <h4 className="font-semibold mb-2">Assigned Courses</h4>
                        <div className="flex flex-wrap gap-2">
                          {teacher.assigned_courses.map((courseId) => {
                            const course = courses.find(c => c.id === courseId);
                            return course ? (
                              <Badge
                                key={courseId}
                                variant="secondary"
                                className="cursor-pointer"
                                onClick={() => handleUnassignCourse(teacher.id, courseId)}
                              >
                                {course.name} ×
                              </Badge>
                            ) : null;
                          })}
                        </div>
                      </div>
                    )}
                    {teacher.assigned_levels && teacher.assigned_levels.length > 0 && (
                      <div>
                        <h4 className="font-semibold mb-2">Assigned Levels</h4>
                        <div className="flex flex-wrap gap-2">
                          {teacher.assigned_levels.map((levelId) => {
                            const level = levels.find(l => l.id === levelId);
                            return level ? (
                              <Badge
                                key={levelId}
                                variant="secondary"
                                className="cursor-pointer"
                                onClick={() => handleUnassignLevel(teacher.id, levelId)}
                              >
                                {level.name} ×
                              </Badge>
                            ) : null;
                          })}
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="rounded-md border">
            <Table>
              <TableCaption>A list of all teachers</TableCaption>
              <TableHeader>
                <TableRow>
                  <TableHead>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="p-0 h-auto font-semibold flex items-center hover:bg-transparent"
                      onClick={() => handleSort('name')}
                    >
                      Name
                      {sortConfig.sortBy === 'name' && (
                        <span className="ml-1">
                          {sortConfig.sortDirection === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </Button>
                  </TableHead>
                  <TableHead>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="p-0 h-auto font-semibold flex items-center hover:bg-transparent"
                      onClick={() => handleSort('email')}
                    >
                      Contact Information
                      {sortConfig.sortBy === 'email' && (
                        <span className="ml-1">
                          {sortConfig.sortDirection === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </Button>
                  </TableHead>
                  <TableHead>Specialization</TableHead>
                  <TableHead>Assigned Courses</TableHead>
                  <TableHead>Assigned Levels</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {teachers.map((teacher) => (
                  <TableRow key={teacher.id}>
                    <TableCell className="font-medium">{teacher.name}</TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <div className="flex items-center">
                          <Mail className="h-4 w-4 mr-2 text-muted-foreground" />
                          {teacher.email}
                        </div>
                        {teacher.contact_number && (
                          <div className="flex items-center mt-1">
                            <Phone className="h-4 w-4 mr-2 text-muted-foreground" />
                            {teacher.contact_number}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>{teacher.specialization || '-'}</TableCell>
                    <TableCell>
                      {teacher.assigned_courses && teacher.assigned_courses.length > 0 
                        ? getCourseNames(teacher.assigned_courses)
                        : '-'
                      }
                    </TableCell>
                    <TableCell>
                      {teacher.assigned_levels && teacher.assigned_levels.length > 0 
                        ? getLevelNames(teacher.assigned_levels)
                        : '-'
                      }
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button 
                          variant="ghost" 
                          size="icon"
                          onClick={() => handleEditTeacher(teacher)}
                        >
                          <PencilIcon className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="icon"
                          onClick={() => {
                            setSelectedTeacher(teacher);
                            setIsAssigningCourse(true);
                          }}
                        >
                          <BookOpen className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="icon"
                          onClick={() => {
                            setSelectedTeacher(teacher);
                            setIsAssigningLevel(true);
                          }}
                        >
                          <Layers className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="icon"
                          className="text-red-600"
                          onClick={() => setTeacherToDelete(teacher)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
                {teachers.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-6 text-muted-foreground">
                      {searchTerm 
                        ? `No teachers found matching "${searchTerm}"`
                        : "No teachers found. Add your first teacher to get started."}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        )
      )}

      {/* Add Teacher Dialog */}
      <Dialog open={isAddingTeacher} onOpenChange={(open) => !isCreatingTeacher && setIsAddingTeacher(open)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add New Teacher</DialogTitle>
            <DialogDescription>
              Create a new teacher account and set their details.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="name">Name</Label>
              <Input
                id="name"
                value={newTeacher.name}
                onChange={(e) => setNewTeacher({ ...newTeacher, name: e.target.value })}
              />
            </div>
            <div>
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={newTeacher.email}
                onChange={(e) => setNewTeacher({ ...newTeacher, email: e.target.value })}
              />
            </div>
            <div>
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                value={newTeacher.password}
                onChange={(e) => setNewTeacher({ ...newTeacher, password: e.target.value })}
              />
            </div>
            <div>
              <Label htmlFor="specialization">Specialization</Label>
              <Input
                id="specialization"
                value={newTeacher.specialization}
                onChange={(e) => setNewTeacher({ ...newTeacher, specialization: e.target.value })}
              />
            </div>
            <div>
              <Label htmlFor="contact">Contact Number</Label>
              <Input
                id="contact"
                value={newTeacher.contact_number}
                onChange={(e) => setNewTeacher({ ...newTeacher, contact_number: e.target.value })}
              />
            </div>
            <div>
              <Label htmlFor="bio">Bio</Label>
              <Textarea
                id="bio"
                value={newTeacher.bio}
                onChange={(e) => setNewTeacher({ ...newTeacher, bio: e.target.value })}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddingTeacher(false)} disabled={isCreatingTeacher}>
              Cancel
            </Button>
            <Button onClick={handleCreateTeacher} disabled={isCreatingTeacher}>
              {isCreatingTeacher ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Creating...
                </>
              ) : (
                'Create Teacher'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Assign Course Dialog */}
      <Dialog open={isAssigningCourse} onOpenChange={setIsAssigningCourse}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Assign Course</DialogTitle>
            <DialogDescription>
              Select a course to assign to {selectedTeacher?.name}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <Select onValueChange={(value) => handleAssignCourse(value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select a course" />
              </SelectTrigger>
              <SelectContent>
                {courses.map((course) => (
                  <SelectItem
                    key={course.id}
                    value={course.id}
                    disabled={selectedTeacher?.assigned_courses?.includes(course.id)}
                  >
                    {course.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </DialogContent>
      </Dialog>

      {/* Assign Level Dialog */}
      <Dialog open={isAssigningLevel} onOpenChange={setIsAssigningLevel}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Assign Level</DialogTitle>
            <DialogDescription>
              Select a level to assign to {selectedTeacher?.name}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <Select onValueChange={(value) => handleAssignLevel(value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select a level" />
              </SelectTrigger>
              <SelectContent>
                {levels.map((level) => (
                  <SelectItem
                    key={level.id}
                    value={level.id}
                    disabled={selectedTeacher?.assigned_levels?.includes(level.id)}
                  >
                    {level.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </DialogContent>
      </Dialog>

      {/* Edit Teacher Dialog */}
      <Dialog open={!!teacherToEdit} onOpenChange={(open) => !open && setTeacherToEdit(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Teacher</DialogTitle>
            <DialogDescription>
              Update teacher information
            </DialogDescription>
          </DialogHeader>
          {isLoadingTeacherDetails ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">Loading teacher details...</span>
            </div>
          ) : teacherToEdit ? (
            <div className="space-y-4">
              <div>
                <Label htmlFor="edit-name">Name</Label>
                <Input
                  id="edit-name"
                  value={teacherToEdit.name}
                  onChange={(e) => setTeacherToEdit({ ...teacherToEdit, name: e.target.value })}
                />
              </div>
              <div>
                <Label htmlFor="edit-specialization">Specialization</Label>
                <Input
                  id="edit-specialization"
                  value={teacherToEdit.specialization}
                  onChange={(e) => setTeacherToEdit({ ...teacherToEdit, specialization: e.target.value })}
                />
              </div>
              <div>
                <Label htmlFor="edit-contact">Contact Number</Label>
                <Input
                  id="edit-contact"
                  value={teacherToEdit.contact_number}
                  onChange={(e) => setTeacherToEdit({ ...teacherToEdit, contact_number: e.target.value })}
                />
              </div>
              <div>
                <Label htmlFor="edit-bio">Bio</Label>
                <Textarea
                  id="edit-bio"
                  value={teacherToEdit.bio}
                  onChange={(e) => setTeacherToEdit({ ...teacherToEdit, bio: e.target.value })}
                />
              </div>
            </div>
          ) : null}
          <DialogFooter>
            <Button variant="outline" onClick={() => setTeacherToEdit(null)} disabled={isUpdatingTeacher}>
              Cancel
            </Button>
            <Button onClick={handleUpdateTeacher} disabled={isUpdatingTeacher || isLoadingTeacherDetails}>
              {isUpdatingTeacher ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Updating...
                </>
              ) : (
                'Update Teacher'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Teacher Confirmation Dialog */}
      <Dialog open={!!teacherToDelete} onOpenChange={(open) => !open && setTeacherToDelete(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Teacher</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete {teacherToDelete?.name}? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setTeacherToDelete(null)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDeleteTeacher}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default TeacherManagement; 