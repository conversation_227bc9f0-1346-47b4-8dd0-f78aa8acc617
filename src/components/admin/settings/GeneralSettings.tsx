import { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { apiClient } from '@/lib/api-client';
import type { GeneralSettings } from '@/types/database/settings';
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";

const GeneralSettings = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [settings, setSettings] = useState<Partial<GeneralSettings>>({
    id: 'default',
    school_name: 'Academic Dashboard',
    maintenance_mode: false
  });
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const { data: savedSettings, isLoading } = useQuery({
    queryKey: ['settings'],
    queryFn: async () => {
      try {
        console.log('Fetching settings from API');
        const data = await apiClient.get('/settings/general');
        return data;
      } catch (error) {
        console.error('Error fetching settings:', error);
        setErrorMessage('Failed to load settings. Using default values.');

        // Return default settings when API fails
        return {
          id: 'default',
          school_name: 'Academic Dashboard',
          maintenance_mode: false
        };
      }
    },
    retry: 1,
    retryDelay: 1000,
  });

  useEffect(() => {
    if (savedSettings) {
      console.log('Updating settings state with:', savedSettings);
      setSettings(savedSettings);
    }
  }, [savedSettings]);

  const updateSettingsMutation = useMutation({
    mutationFn: async (newSettings: Partial<GeneralSettings>) => {
      try {
        console.log('Updating settings via API:', newSettings);
        const data = await apiClient.put('/settings/general', {
          school_name: newSettings.school_name,
          maintenance_mode: newSettings.maintenance_mode,
        });

        return data;
      } catch (error) {
        console.error('Error updating settings:', error);
        throw error;
      }
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['settings'] });
      
      // Save to localStorage for app-wide access
      const localSettings = {
        schoolName: data.school_name,
        maintenanceMode: data.maintenance_mode
      };
      localStorage.setItem('generalSettings', JSON.stringify(localSettings));
      
      // Trigger a storage event to notify other components about the change
      const storageEvent = new StorageEvent('storage', {
        key: 'generalSettings',
        newValue: JSON.stringify(localSettings)
      });
      window.dispatchEvent(storageEvent);
      
      toast({
        title: "Success",
        description: "Settings saved successfully",
      });
      
      // Clear any error messages
      setErrorMessage(null);
    },
    onError: (error) => {
      console.error('Mutation error:', error);
      toast({
        title: "Error",
        description: "Failed to save settings",
        variant: "destructive",
      });
    },
  });

  const handleSave = () => {
    console.log('Saving settings:', settings);
    updateSettingsMutation.mutate(settings);
  };

  if (isLoading) {
    return <div>Loading settings...</div>;
  }

  return (
    <div className="p-6">
      <h2 className="text-2xl font-bold mb-6">General Settings</h2>
      
      {errorMessage && (
        <Alert className="mb-4 bg-yellow-50 text-yellow-800 border-yellow-200">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{errorMessage}</AlertDescription>
        </Alert>
      )}
      
      <Card className="p-6 space-y-6">
        <div className="space-y-4">
          <div>
            <Label htmlFor="schoolName">School Name</Label>
            <Input
              id="schoolName"
              value={settings.school_name}
              onChange={(e) => setSettings({ ...settings, school_name: e.target.value })}
            />
          </div>

          <div className="flex items-center justify-between">
            <Label htmlFor="maintenanceMode">Maintenance Mode</Label>
            <Switch
              id="maintenanceMode"
              checked={settings.maintenance_mode}
              onCheckedChange={(checked) => setSettings({ ...settings, maintenance_mode: checked })}
            />
          </div>
        </div>

        <Button onClick={handleSave} className="w-full">Save Settings</Button>
      </Card>
    </div>
  );
};

export default GeneralSettings;
