import { useState, useEffect, useRef } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { useQuery, useQueryClient } from '@tanstack/react-query';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Trash2, Pencil } from "lucide-react";
import { Label } from "@/components/ui/label";
import { createSubject, deleteSubject, updateSubject, getSubjects } from '@/api/subjects';
import { getCourses } from '@/api/courses';
import { logActivity } from '@/utils/activity-logger';

interface Subject {
  id?: string;
  name: string;
  description: string;
  code: string;
  courseId: string;
  teacherId?: string;
  credits: number;
  status: 'active' | 'inactive';
}

interface Course {
  id: string;
  name: string;
  code: string;
}

const defaultSubject: Subject = {
  name: '',
  description: '',
  code: '',
  courseId: '',
  credits: 3,
  status: 'active'
};

const SubjectManagement = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [newSubject, setNewSubject] = useState<Subject>(defaultSubject);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [subjectToEdit, setSubjectToEdit] = useState<Subject | null>(null);
  const pendingUpdatesRef = useRef<{id: string, data: Partial<Subject>}[]>([]);

  const { data: subjects = [], isLoading: isLoadingSubjects, error: subjectsError } = useQuery({
    queryKey: ['subjects'],
    queryFn: getSubjects
  });

  const { data: courses = [] as Course[], isLoading: isLoadingCourses } = useQuery({
    queryKey: ['courses'],
    queryFn: getCourses
  });

  // Track online/offline status
  useEffect(() => {
    const handleOnline = () => {
      console.log('App is online, attempting to process pending updates');
      processPendingUpdates();
    };
    
    const handleOffline = () => {
      console.log('App is offline');
      toast({
        title: "You're offline",
        description: "Changes will be saved when your connection is restored",
        variant: "destructive",
      });
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [toast]);

  // Function to process any pending updates when online
  const processPendingUpdates = async () => {
    if (pendingUpdatesRef.current.length === 0) return;
    
    toast({
      title: "Connection restored",
      description: `Attempting to process ${pendingUpdatesRef.current.length} pending updates`,
    });
    
    for (const update of pendingUpdatesRef.current) {
      try {
        await updateSubject(update.id, update.data);
        
        // Log the activity
        await logActivity("subject_updated", {
          subjectId: update.id,
          description: `Updated subject (offline change applied)`
        });
      } catch (error) {
        console.error('Failed to process pending update:', error);
        toast({
          title: "Update failed",
          description: `Failed to update subject: ${update.id}`,
          variant: "destructive",
        });
      }
    }
    
    // Clear the pending updates
    pendingUpdatesRef.current = [];
    
    // Refresh the data
    queryClient.invalidateQueries({ queryKey: ['subjects'] });
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    if (subjectToEdit) {
      setSubjectToEdit({
        ...subjectToEdit,
        [name]: name === 'credits' ? Number(value) : value
      });
    } else {
      setNewSubject({
        ...newSubject,
        [name]: name === 'credits' ? Number(value) : value
      });
    }
  };

  const handleStatusChange = (checked: boolean) => {
    const status = checked ? 'active' : 'inactive';
    
    if (subjectToEdit) {
      setSubjectToEdit({
        ...subjectToEdit,
        status
      });
    } else {
      setNewSubject({
        ...newSubject,
        status
      });
    }
  };

  const handleCourseChange = (courseId: string) => {
    if (subjectToEdit) {
      setSubjectToEdit({
        ...subjectToEdit,
        courseId
      });
    } else {
      setNewSubject({
        ...newSubject,
        courseId
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      if (subjectToEdit && subjectToEdit.id) {
        // Update existing subject
        await updateSubject(subjectToEdit.id, {
          name: subjectToEdit.name,
          description: subjectToEdit.description,
          code: subjectToEdit.code,
          courseId: subjectToEdit.courseId,
          credits: subjectToEdit.credits,
          status: subjectToEdit.status
        });
        
        // Log the activity
        await logActivity("subject_updated", {
          subjectId: subjectToEdit.id,
          description: `Updated subject ${subjectToEdit.name} (${subjectToEdit.code})`
        });
        
        setSubjectToEdit(null);
      } else {
        // Create new subject
        const result = await createSubject({
          name: newSubject.name,
          description: newSubject.description,
          code: newSubject.code,
          courseId: newSubject.courseId,
          credits: newSubject.credits,
          status: newSubject.status
        });
        
        // Log the activity
        if (result && result.id) {
          await logActivity("subject_created", {
            subjectId: result.id,
            description: `Created new subject ${newSubject.name} (${newSubject.code})`
          });
        }
        
        // Reset form
        setNewSubject(defaultSubject);
      }
      
      // Refresh the data
      queryClient.invalidateQueries({ queryKey: ['subjects'] });
    } catch (error) {
      console.error('Error submitting subject:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEdit = (subject: Subject) => {
    setSubjectToEdit(subject);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleCancelEdit = () => {
    setSubjectToEdit(null);
  };

  const handleDelete = async (id: string, name: string, code: string) => {
    if (window.confirm(`Are you sure you want to delete subject ${name} (${code})?`)) {
      try {
        await deleteSubject(id);
        
        // Log the activity
        await logActivity("subject_deleted", {
          subjectId: id,
          description: `Deleted subject ${name} (${code})`
        });
        
        // Refresh the data
        queryClient.invalidateQueries({ queryKey: ['subjects'] });
      } catch (error) {
        console.error('Error deleting subject:', error);
      }
    }
  };

  const getCourseNameById = (courseId: string) => {
    const course = courses.find(c => c.id === courseId);
    return course ? `${course.name} (${course.code})` : 'Unknown Course';
  };

  if (isLoadingSubjects || isLoadingCourses) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-700" />
      </div>
    );
  }

  if (subjectsError) {
    return (
      <div className="p-4 bg-red-50 text-red-700 rounded-md">
        Error loading subjects. Please try again later.
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6">Subject Management</h1>
      
      <Card className="p-6 mb-8">
        <h2 className="text-xl font-semibold mb-4">
          {subjectToEdit ? 'Edit Subject' : 'Add New Subject'}
        </h2>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Subject Name</Label>
              <Input
                id="name"
                name="name"
                value={subjectToEdit ? subjectToEdit.name : newSubject.name}
                onChange={handleInputChange}
                placeholder="Enter subject name"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="code">Subject Code</Label>
              <Input
                id="code"
                name="code"
                value={subjectToEdit ? subjectToEdit.code : newSubject.code}
                onChange={handleInputChange}
                placeholder="e.g. MATH101"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="courseId">Course</Label>
              <Select
                value={subjectToEdit ? subjectToEdit.courseId : newSubject.courseId}
                onValueChange={handleCourseChange}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a course" />
                </SelectTrigger>
                <SelectContent>
                  {courses.map((course) => (
                    <SelectItem key={course.id} value={course.id}>
                      {course.name} ({course.code})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="credits">Credits</Label>
              <Input
                id="credits"
                name="credits"
                type="number"
                min="1"
                max="10"
                value={subjectToEdit ? subjectToEdit.credits : newSubject.credits}
                onChange={handleInputChange}
                required
              />
            </div>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              name="description"
              value={subjectToEdit ? subjectToEdit.description : newSubject.description}
              onChange={handleInputChange}
              placeholder="Enter subject description"
              rows={3}
            />
          </div>
          
          <div className="flex items-center space-x-2">
            <Switch
              id="status"
              checked={subjectToEdit ? subjectToEdit.status === 'active' : newSubject.status === 'active'}
              onCheckedChange={handleStatusChange}
            />
            <Label htmlFor="status">Active</Label>
          </div>
          
          <div className="flex space-x-2">
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Saving...' : subjectToEdit ? 'Update Subject' : 'Add Subject'}
            </Button>
            
            {subjectToEdit && (
              <Button type="button" variant="outline" onClick={handleCancelEdit}>
                Cancel
              </Button>
            )}
          </div>
        </form>
      </Card>
      
      <h2 className="text-xl font-semibold mb-4">Subjects List</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {subjects.length === 0 ? (
          <p className="col-span-full text-center text-gray-500">No subjects found. Add your first subject above.</p>
        ) : (
          subjects.map((subject) => (
            <Card key={subject.id} className="p-4">
              <div className="flex justify-between items-start mb-2">
                <div>
                  <h3 className="font-semibold">{subject.name}</h3>
                  <p className="text-sm text-gray-500">{subject.code}</p>
                </div>
                <div className="flex space-x-1">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleEdit(subject)}
                    title="Edit"
                  >
                    <Pencil className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => subject.id && handleDelete(subject.id, subject.name, subject.code)}
                    title="Delete"
                    className="text-red-500 hover:text-red-700"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              
              <p className="text-sm mb-2">
                <span className="font-medium">Course:</span> {getCourseNameById(subject.courseId)}
              </p>
              
              <p className="text-sm mb-2">
                <span className="font-medium">Credits:</span> {subject.credits}
              </p>
              
              {subject.description && (
                <p className="text-sm text-gray-600 mb-2">{subject.description}</p>
              )}
              
              <div className="flex items-center mt-2">
                <span
                  className={`inline-flex items-center px-2 py-1 text-xs rounded-full ${
                    subject.status === 'active'
                      ? 'bg-green-100 text-green-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}
                >
                  {subject.status === 'active' ? 'Active' : 'Inactive'}
                </span>
              </div>
            </Card>
          ))
        )}
      </div>
    </div>
  );
};

export default SubjectManagement; 