import { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { useQuery, useQueryClient } from '@tanstack/react-query';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { DatePicker } from "@/components/ui/date-picker";
import { Trash2, Pencil, AlertCircle, Globe } from "lucide-react";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  getAnnouncements, 
  createAnnouncement, 
  updateAnnouncement, 
  deleteAnnouncement,
  AnnouncementFormData
} from '@/api/announcements';
import { getCourses } from '@/api/courses';
import { getLevels } from '@/api/levels';
import { format } from 'date-fns';
import { capitalize } from '@/utils/string-utils';

interface Announcement {
  id?: string;
  title: string;
  content: string;
  target_levels: string[];
  priority: string;
  expires_at: string;
  is_general?: boolean;
  created_by?: string;
  created_at?: string;
  author?: string;
  updated_by?: string;
  updated_at?: string;
}

interface Level {
  id: string;
  name: string;
  course_id: string;
}

interface Course {
  id: string;
  name: string;
}

const AnnouncementManagement = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  const [newAnnouncement, setNewAnnouncement] = useState<Announcement>({
    title: '',
    content: '',
    target_levels: [],
    priority: 'normal',
    is_general: false,
    expires_at: format(new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), 'yyyy-MM-dd') // 7 days from now
  });
  
  const [announcementToEdit, setAnnouncementToEdit] = useState<Announcement | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [expiryDate, setExpiryDate] = useState<Date | undefined>(
    new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days from now
  );
  const [editExpiryDate, setEditExpiryDate] = useState<Date | undefined>(undefined);
  
  // Fetch announcements
  const { data: announcements = [] } = useQuery({
    queryKey: ['announcements'],
    queryFn: getAnnouncements
  });
  
  // Fetch levels for targeting
  const { data: levels = [] } = useQuery({
    queryKey: ['levels'],
    queryFn: getLevels
  });
  
  // Fetch courses for filtering levels
  const { data: courses = [] } = useQuery({
    queryKey: ['courses'],
    queryFn: getCourses
  });
  
  // Update expiry date in form when date picker changes
  useEffect(() => {
    if (expiryDate) {
      setNewAnnouncement(prev => ({
        ...prev,
        expires_at: format(expiryDate, 'yyyy-MM-dd')
      }));
    }
  }, [expiryDate]);
  
  // Update expiry date in edit form when date picker changes
  useEffect(() => {
    if (editExpiryDate && announcementToEdit) {
      setAnnouncementToEdit({
        ...announcementToEdit,
        expires_at: format(editExpiryDate, 'yyyy-MM-dd')
      });
    }
  }, [editExpiryDate]);
  
  // Set edit expiry date when editing an announcement
  useEffect(() => {
    if (announcementToEdit && announcementToEdit.expires_at) {
      setEditExpiryDate(new Date(announcementToEdit.expires_at));
    }
  }, [announcementToEdit]);
  
  const handleInputChange = (field: keyof Announcement, value: any) => {
    setNewAnnouncement(prev => ({
      ...prev,
      [field]: value
    }));
    
    // If toggling general announcement, reset target levels if needed
    if (field === 'is_general' && value === true) {
      setNewAnnouncement(prev => ({
        ...prev,
        target_levels: []
      }));
    }
  };
  
  const handleEditInputChange = (field: keyof Announcement, value: any) => {
    if (announcementToEdit) {
      setAnnouncementToEdit({
        ...announcementToEdit,
        [field]: value
      });
      
      // If toggling general announcement, reset target levels if needed
      if (field === 'is_general' && value === true && announcementToEdit) {
        setAnnouncementToEdit({
          ...announcementToEdit,
          target_levels: []
        });
      }
    }
  };
  
  const handleAddAnnouncement = async () => {
    if (!newAnnouncement.title.trim()) {
      toast({
        title: "Error",
        description: "Title is required",
        variant: "destructive"
      });
      return;
    }
    
    if (!newAnnouncement.content.trim()) {
      toast({
        title: "Error",
        description: "Content is required",
        variant: "destructive"
      });
      return;
    }
    
    // Only validate target levels if it's not a general announcement
    if (!newAnnouncement.is_general && newAnnouncement.target_levels.length === 0) {
      toast({
        title: "Error",
        description: "Please select at least one target level or mark as a general announcement",
        variant: "destructive"
      });
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      await createAnnouncement(newAnnouncement as AnnouncementFormData);
      
      // Reset form
      setNewAnnouncement({
        title: '',
        content: '',
        target_levels: [],
        priority: 'normal',
        is_general: false,
        expires_at: format(new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), 'yyyy-MM-dd')
      });
      
      // Reset date picker
      setExpiryDate(new Date(Date.now() + 7 * 24 * 60 * 60 * 1000));
      
      // Refresh announcements list
      queryClient.invalidateQueries({ queryKey: ['announcements'] });
      
      toast({
        title: "Success",
        description: "Announcement created successfully"
      });
    } catch (error) {
      console.error('Error creating announcement:', error);
      toast({
        title: "Error",
        description: "Failed to create announcement",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  const handleUpdateAnnouncement = async () => {
    if (!announcementToEdit || !announcementToEdit.id) {
      toast({
        title: "Error",
        description: "Announcement not found",
        variant: "destructive"
      });
      return;
    }
    
    if (!announcementToEdit.title.trim()) {
      toast({
        title: "Error",
        description: "Title is required",
        variant: "destructive"
      });
      return;
    }
    
    if (!announcementToEdit.content.trim()) {
      toast({
        title: "Error",
        description: "Content is required",
        variant: "destructive"
      });
      return;
    }
    
    // Only validate target levels if it's not a general announcement
    if (!announcementToEdit.is_general && announcementToEdit.target_levels.length === 0) {
      toast({
        title: "Error",
        description: "Please select at least one target level or mark as a general announcement",
        variant: "destructive"
      });
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      await updateAnnouncement(announcementToEdit.id, {
        title: announcementToEdit.title,
        content: announcementToEdit.content,
        target_levels: announcementToEdit.target_levels,
        priority: announcementToEdit.priority,
        is_general: announcementToEdit.is_general,
        expires_at: announcementToEdit.expires_at
      });
      
      // Cancel edit mode
      setAnnouncementToEdit(null);
      
      // Refresh announcements list
      queryClient.invalidateQueries({ queryKey: ['announcements'] });
      
      toast({
        title: "Success",
        description: "Announcement updated successfully"
      });
    } catch (error) {
      console.error('Error updating announcement:', error);
      toast({
        title: "Error",
        description: "Failed to update announcement",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  const handleDeleteAnnouncement = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this announcement?')) {
      return;
    }
    
    try {
      await deleteAnnouncement(id);
      
      // Refresh announcements list
      queryClient.invalidateQueries({ queryKey: ['announcements'] });
      
      toast({
        title: "Success",
        description: "Announcement deleted successfully"
      });
    } catch (error) {
      console.error('Error deleting announcement:', error);
      toast({
        title: "Error",
        description: "Failed to delete announcement",
        variant: "destructive"
      });
    }
  };
  
  // Helper function to get level name by ID
  const getLevelName = (levelId: string) => {
    const level = levels.find(l => l.id === levelId);
    return level ? level.name : 'Unknown Level';
  };
  
  // Helper function to check if announcement is expired
  const isExpired = (expiryDate: string) => {
    return new Date(expiryDate) < new Date();
  };
  
  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Announcement Management</h1>
      
      <Card className="p-6">
        <h2 className="text-xl font-semibold mb-4">
          {announcementToEdit ? 'Edit Announcement' : 'Create New Announcement'}
        </h2>
        
        <div className="space-y-4">
          <div>
            <Label htmlFor="title">Title</Label>
            <Input
              id="title"
              value={announcementToEdit ? announcementToEdit.title : newAnnouncement.title}
              onChange={(e) => announcementToEdit 
                ? handleEditInputChange('title', e.target.value)
                : handleInputChange('title', e.target.value)
              }
              placeholder="Enter announcement title"
            />
          </div>
          
          <div>
            <Label htmlFor="content">Content</Label>
            <Textarea
              id="content"
              value={announcementToEdit ? announcementToEdit.content : newAnnouncement.content}
              onChange={(e) => announcementToEdit
                ? handleEditInputChange('content', e.target.value)
                : handleInputChange('content', e.target.value)
              }
              placeholder="Enter announcement content"
              rows={5}
            />
          </div>
          
          <div className="flex items-center space-x-2 mb-4">
            <Checkbox 
              id="is_general" 
              checked={announcementToEdit ? !!announcementToEdit.is_general : !!newAnnouncement.is_general}
              onCheckedChange={(checked) => {
                if (announcementToEdit) {
                  handleEditInputChange('is_general', checked);
                } else {
                  handleInputChange('is_general', checked);
                }
              }}
            />
            <Label 
              htmlFor="is_general" 
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              General Announcement (visible to all users)
            </Label>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Only show target levels if not a general announcement */}
            {(announcementToEdit ? !announcementToEdit.is_general : !newAnnouncement.is_general) && (
              <div>
                <Label htmlFor="target_levels">Target Levels</Label>
                <Select
                  value={announcementToEdit ? announcementToEdit.target_levels[0] || '' : newAnnouncement.target_levels[0] || ''}
                  onValueChange={(value) => {
                    if (announcementToEdit) {
                      handleEditInputChange('target_levels', [value]);
                    } else {
                      handleInputChange('target_levels', [value]);
                    }
                  }}
                  disabled={announcementToEdit ? !!announcementToEdit.is_general : !!newAnnouncement.is_general}
                >
                  <SelectTrigger id="target_levels">
                    <SelectValue placeholder="Select target level" />
                  </SelectTrigger>
                  <SelectContent>
                    {levels.map((level) => (
                      <SelectItem key={level.id} value={level.id}>
                        {level.name} ({courses.find(c => c.id === level.course_id)?.name || 'Unknown Course'})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <p className="text-xs text-gray-500 mt-1">Currently only supporting single level targeting</p>
              </div>
            )}
            
            <div>
              <Label htmlFor="priority">Priority</Label>
              <Select
                value={announcementToEdit ? announcementToEdit.priority : newAnnouncement.priority}
                onValueChange={(value) => announcementToEdit
                  ? handleEditInputChange('priority', value)
                  : handleInputChange('priority', value)
                }
              >
                <SelectTrigger id="priority">
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">Low</SelectItem>
                  <SelectItem value="normal">Normal</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="urgent">Urgent</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="expires_at">Expiry Date</Label>
              <DatePicker
                date={announcementToEdit ? editExpiryDate : expiryDate}
                onSelect={(date) => {
                  if (announcementToEdit) {
                    setEditExpiryDate(date);
                  } else {
                    setExpiryDate(date);
                  }
                }}
              />
            </div>
          </div>
          
          <div className="mt-4 flex gap-2">
            {announcementToEdit ? (
              <>
                <Button 
                  onClick={handleUpdateAnnouncement}
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Updating...' : 'Update Announcement'}
                </Button>
                <Button 
                  variant="secondary"
                  onClick={() => setAnnouncementToEdit(null)}
                >
                  Cancel
                </Button>
              </>
            ) : (
              <Button 
                onClick={handleAddAnnouncement}
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Creating...' : 'Create Announcement'}
              </Button>
            )}
          </div>
        </div>
      </Card>
      
      <Card className="p-6">
        <h2 className="text-xl font-semibold mb-4">Existing Announcements</h2>
        
        {announcements.length === 0 ? (
          <div className="text-center py-8">
            <AlertCircle className="mx-auto h-12 w-12 text-gray-400" />
            <p className="mt-2 text-lg font-medium">No announcements found</p>
            <p className="text-gray-500">Create your first announcement using the form above.</p>
          </div>
        ) : (
          <div className="space-y-4">
            {announcements.map((announcement) => (
              <Card key={announcement.id} className={`
                ${announcement.priority === 'urgent' ? 'border-l-4 border-l-red-500' : 
                  announcement.priority === 'high' ? 'border-l-4 border-l-orange-500' : 
                  announcement.priority === 'normal' ? 'border-l-4 border-l-blue-500' : 
                  'border-l-4 border-l-gray-300'}
              `}>
                <div className="p-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="flex items-center gap-2">
                        <h3 className="font-medium text-lg">{announcement.title}</h3>
                        {announcement.is_general && (
                          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                            <Globe className="h-3 w-3 mr-1" /> General
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm text-gray-500 mt-1">{announcement.content}</p>
                      
                      {!announcement.is_general && announcement.target_levels && announcement.target_levels.length > 0 && (
                        <div className="flex flex-wrap gap-2 mt-2">
                          {announcement.target_levels.map(levelId => (
                            <Badge key={levelId} variant="outline">
                              {getLevelName(levelId)}
                            </Badge>
                          ))}
                        </div>
                      )}
                      
                      <div className="flex items-center gap-2 mt-2 text-sm text-gray-500">
                        <span>Created: {new Date(announcement.created_at || '').toLocaleDateString()}</span>
                        <span>•</span>
                        <span>By: {announcement.author}</span>
                        <span>•</span>
                        <span className={`${isExpired(announcement.expires_at) ? 'text-red-500' : 'text-green-500'}`}>
                          {isExpired(announcement.expires_at) ? 'Expired' : 'Expires'}: {new Date(announcement.expires_at).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                    
                    <div className="flex gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => setAnnouncementToEdit(announcement)}
                      >
                        <Pencil className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => announcement.id && handleDeleteAnnouncement(announcement.id)}
                      >
                        <Trash2 className="h-4 w-4 text-red-500" />
                      </Button>
                    </div>
                  </div>
                  
                  <Badge className={`mt-2 ${
                    announcement.priority === 'urgent' ? 'bg-red-100 text-red-800 hover:bg-red-100' : 
                    announcement.priority === 'high' ? 'bg-orange-100 text-orange-800 hover:bg-orange-100' : 
                    announcement.priority === 'normal' ? 'bg-blue-100 text-blue-800 hover:bg-blue-100' : 
                    'bg-gray-100 text-gray-800 hover:bg-gray-100'
                  }`}>
                    {capitalize(announcement.priority)} Priority
                  </Badge>
                </div>
              </Card>
            ))}
          </div>
        )}
      </Card>
    </div>
  );
};

export default AnnouncementManagement; 