import { useQuery } from "@tanstack/react-query";
import { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DatePicker } from "@/components/ui/date-picker";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Loader2, User } from "lucide-react";
import type { ActivityType } from "@/types/activity";
import { Badge } from "@/components/ui/badge";
import { apiClient } from "@/lib/api-client";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";

// Create a user cache to avoid repeated fetches
const userCache: Record<string, {name: string, email: string}> = {};

const activityLabels: Record<ActivityType, string> = {
  login: "User Login",
  logout: "User Logout",
  student_created: "Student Created",
  student_updated: "Student Updated",
  student_deleted: "Student Deleted",
  student_image_uploaded: "Student Image Uploaded",
  payment_created: "Payment Created",
  payment_updated: "Payment Updated",
  course_created: "Course Created",
  course_updated: "Course Updated",
  course_deleted: "Course Deleted",
  course_fee_updated: "Course Fee Updated",
  level_created: "Level Created",
  level_updated: "Level Updated",
  level_deleted: "Level Deleted",
  transaction_created: "Transaction Created",
  transaction_updated: "Transaction Updated",
  transaction_deleted: "Transaction Deleted",
  settings_updated: "Settings Updated",
  attendance_created: "Attendance Created",
  attendance_updated: "Attendance Updated",
  attendance_bulk_created: "Bulk Attendance Created",
  exam_created: "Exam Created",
  exam_updated: "Exam Updated",
  exam_deleted: "Exam Deleted",
  exam_result_created: "Exam Result Created",
  exam_result_updated: "Exam Result Updated",
  exam_results_bulk_created: "Exam Results Bulk Created",
  subject_created: "Subject Created",
  subject_updated: "Subject Updated",
  subject_deleted: "Subject Deleted"
};

const getActivityColor = (type: ActivityType): string => {
  if (type.includes('created')) return 'bg-green-100 text-green-800';
  if (type.includes('updated')) return 'bg-blue-100 text-blue-800';
  if (type.includes('deleted')) return 'bg-red-100 text-red-800';
  if (type.includes('image')) return 'bg-purple-100 text-purple-800';
  return 'bg-gray-100 text-gray-800';
};

// Helper function to convert timestamp to Date
const timestampToDate = (timestamp: string | { seconds: number; nanoseconds: number } | undefined): Date | undefined => {
  if (!timestamp) return undefined;
  // Case 2: If timestamp is a string (ISO format from Supabase)
  else if (typeof timestamp === 'string') {
    try {
      const date = new Date(timestamp);
      if (!isNaN(date.getTime())) {
        return date;
      }
    } catch (error) {
      console.error("Error parsing timestamp string:", error);
    }
  } 
  // Case 3: If timestamp is a Firestore-like object with seconds and nanoseconds
  else if (timestamp && typeof timestamp === 'object') {
    if ('seconds' in timestamp && 'nanoseconds' in timestamp) {
      try {
        return new Date((timestamp as any).seconds * 1000 + (timestamp as any).nanoseconds / 1000000);
      } catch (error) {
        console.error("Error converting timestamp object:", error);
      }
    }
    // Case 4: If timestamp is a Firestore-like object with toDate method
    else if ('toDate' in timestamp && typeof (timestamp as any).toDate === 'function') {
      try {
        return (timestamp as any).toDate();
      } catch (error) {
        console.error("Error calling toDate on timestamp:", error);
      }
    }
    // Case 5: If timestamp has a different date-like format
    else if ('_seconds' in timestamp && '_nanoseconds' in timestamp) {
      try {
        return new Date((timestamp as any)._seconds * 1000 + (timestamp as any)._nanoseconds / 1000000);
      } catch (error) {
        console.error("Error converting timestamp with _seconds format:", error);
      }
    }
  }
  
  console.warn("Could not parse timestamp format:", timestamp);
  return undefined;
};

// Function to fetch user details
const fetchUserDetails = async (userId: string): Promise<{name: string, email: string}> => {
  // Check cache first
  if (userCache[userId]) {
    return userCache[userId];
  }

  try {
    // Try API first
    const userData = await apiClient.get(`/users/${userId}`) as any;

    const userInfo = {
      name: userData.name || userData.full_name || userData.username || 'Unknown',
      email: userData.email || 'Unknown'
    };

    // Cache the result
    userCache[userId] = userInfo;
    return userInfo;
  } catch (error) {
    console.error(`Error fetching details for user ${userId}:`, error);

    try {
      // Fall back to Supabase
      const { data, error } = await supabase
        .from('profiles')
        .select('full_name, username, email')
        .eq('id', userId)
        .single();

      if (error || !data) {
        // If we can't find the user, return a default
        const defaultInfo = { name: 'Unknown User', email: userId };
        userCache[userId] = defaultInfo;
        return defaultInfo;
      }

      const userInfo = {
        name: data.full_name || data.username || 'Unknown',
        email: data.email || data.username || 'Unknown'
      };

      // Cache the result
      userCache[userId] = userInfo;
      return userInfo;
    } catch (supabaseError) {
      console.error(`Error fetching from Supabase for user ${userId}:`, supabaseError);
      // Return a default if there's an error
      const defaultInfo = { name: 'Unknown User', email: userId };
      userCache[userId] = defaultInfo;
      return defaultInfo;
    }
  }
};

export const ActivityLogs = () => {
  const [selectedType, setSelectedType] = useState<ActivityType | "all">("all");
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);
  const [userDetails, setUserDetails] = useState<Record<string, {name: string, email: string}>>({});
  const [isLoadingUserDetails, setIsLoadingUserDetails] = useState(false);

  // Set page title
  useEffect(() => {
    document.title = "Activity Logs | Academic Dashboard";
    return () => {
      document.title = "Academic Dashboard";
    };
  }, []);

  const { data: logs = [], isLoading, error } = useQuery({
    queryKey: ['activity-logs', selectedType, selectedDate],
    queryFn: async () => {
      try {
        let allLogs;
        
        // If we're not filtering by type, just get all logs without any type filter
        if (selectedType === "all") {
          allLogs = await apiClient.get('/activities') as any[];
        } else {
          // Otherwise, specifically filter by the selected activity type
          allLogs = await apiClient.get(`/activities?activity_type=${selectedType}`) as any[];
        }

        // Debug log the raw data
        console.log("Raw activity logs:", allLogs);

        // Filter by date in memory
        if (selectedDate) {
          const selectedDateStart = new Date(selectedDate);
          selectedDateStart.setHours(0, 0, 0, 0);

          const selectedDateEnd = new Date(selectedDate);
          selectedDateEnd.setHours(23, 59, 59, 999);

          return allLogs.filter((log: any) => {
            // Handle different timestamp formats
            const logDate = timestampToDate(log.created_at);
            
            if (!logDate) {
              console.warn("Could not parse date for log:", log);
              return false;
            }
            
            // Check if the log date is within the selected date range
            return logDate >= selectedDateStart && logDate <= selectedDateEnd;
          });
        }
        
        return allLogs;
      } catch (error) {
        console.error("Error fetching activity logs:", error);
        throw error;
      }
    }
  });

  // Fetch user details for all logs
  useEffect(() => {
    if (!logs || logs.length === 0) return;
    
    const uniqueUserIds = [...new Set(logs.map((log: any) => log.user_id).filter(Boolean))] as string[];
    if (uniqueUserIds.length === 0) return;

    setIsLoadingUserDetails(true);

    const fetchAllUserDetails = async () => {
      const userDetailsMap: Record<string, {name: string, email: string}> = {};

      // Use Promise.all to fetch all user details in parallel
      await Promise.all(
        uniqueUserIds.map(async (userId: string) => {
          if (!userId) return;

          const details = await fetchUserDetails(userId);
          userDetailsMap[userId] = details;
        })
      );
      
      setUserDetails(userDetailsMap);
      setIsLoadingUserDetails(false);
    };
    
    fetchAllUserDetails();
  }, [logs]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[50vh]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4 my-4">
        <h3 className="text-red-800 font-medium mb-2">Error loading activity logs</h3>
        <p className="text-red-700">{error instanceof Error ? error.message : "Unknown error occurred"}</p>
        <p className="text-sm text-red-600 mt-2">
          Please try refreshing the page or contact the system administrator if the problem persists.
        </p>
      </div>
    );
  }

  const formatDate = (timestamp: any) => {
    const date = timestampToDate(timestamp);
    if (!date) return 'Unknown date';
    
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Activity Logs</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <Select
            value={selectedType}
            onValueChange={(value) => {
              console.log("Selected activity type:", value);
              setSelectedType(value as ActivityType | "all");
            }}
          >
            <SelectTrigger className="w-full sm:w-[200px]">
              <SelectValue>
                {selectedType === "all" 
                  ? "All Activities" 
                  : (activityLabels[selectedType as ActivityType] || selectedType)
                }
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Activities</SelectItem>
              {Object.entries(activityLabels).map(([type, label]) => (
                <SelectItem key={type} value={type}>
                  {label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <div className="flex flex-col sm:flex-row gap-4">
            <DatePicker
              selected={selectedDate}
              onSelect={(date) => {
                console.log("Selected date:", date);
                setSelectedDate(date);
              }}
            />
            {selectedDate && (
              <Button 
                variant="outline"
                size="sm"
                onClick={() => setSelectedDate(undefined)}
              >
                Clear Date
              </Button>
            )}
          </div>
        </div>
        
        <div className="space-y-4">
          {logs.length > 0 ? (
            logs.map((log: any) => {
              // Handle missing or malformed data
              if (!log || !log.id) {
                console.warn("Skipping invalid log entry:", log);
                return null;
              }
              
              // Check if activity_type is valid
              const activityType = log.activity_type as ActivityType;
              if (!activityType || !Object.keys(activityLabels).includes(activityType)) {
                console.warn(`Invalid activity type for log: ${log.id}`, log);
              }
              
              // Get user details or placeholder
              const user = userDetails[log.user_id] || { 
                name: isLoadingUserDetails ? 'Loading...' : 'Unknown User', 
                email: log.user_id || 'No User ID' 
              };
              
              return (
                <div key={log.id} className="p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                  <div className="flex flex-col sm:flex-row justify-between items-start mb-2 gap-2">
                    <div className="space-y-1">
                      <Badge 
                        variant="outline"
                        className={getActivityColor(activityType)}
                      >
                        {activityLabels[activityType] || activityType || 'Unknown Activity'}
                      </Badge>
                      <p className="text-sm text-gray-500">
                        {formatDate(log.created_at)}
                      </p>
                    </div>
                    <div className="flex items-center gap-2 text-right">
                      <User className="w-8 h-8 p-1 rounded-full bg-gray-100" />
                      <div>
                        <p className="font-medium">{user.name}</p>
                        <p className="text-xs text-gray-500">{user.email}</p>
                      </div>
                    </div>
                  </div>
                  <p className="text-gray-700">{log.description || 'No description available'}</p>
                  {log.metadata && Object.keys(log.metadata).length > 0 ? (
                    <div className="mt-2 text-sm text-gray-600">
                      <p className="font-medium">Additional Details:</p>
                      <pre className="mt-1 bg-gray-50 p-2 rounded-md overflow-auto text-xs">
                        {JSON.stringify(log.metadata, null, 2)}
                      </pre>
                    </div>
                  ) : null}
                </div>
              );
            })
          ) : (
            <div className="text-center py-8 text-gray-500">
              {selectedType !== "all" || selectedDate ? (
                <div>
                  <p>No activity logs found for your filter criteria</p>
                  <p className="text-sm mt-1">
                    {selectedType !== "all" && `Activity type: ${activityLabels[selectedType as ActivityType] || selectedType}`}
                    {selectedType !== "all" && selectedDate && " | "}
                    {selectedDate && `Date: ${selectedDate.toLocaleDateString()}`}
                  </p>
                </div>
              ) : (
                <p>No activity logs found</p>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
