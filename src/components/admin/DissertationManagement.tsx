import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { DatePicker } from "@/components/ui/date-picker";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Search,
  Plus,
  Edit,
  Trash2,
  Users,
  BookOpen,
  Calendar,
  MoreVertical,
  UserCheck,
  Clock,
  CheckCircle,
  AlertTriangle,
  Eye,
  FileText,
  Filter,
  Download,
  RefreshCw,
  PenTool,
  GraduationCap
} from 'lucide-react';
import { getUsers } from '@/api/users';
import { getStudents } from '@/api/students';
import { getTeachersWithoutSorting } from '@/api/teachers';
import { apiClient } from '@/lib/api-client';
import { DissertationDocument } from '@/types/dissertation';
import { format } from 'date-fns';

interface DissertationTopic {
  id?: string;
  title: string;
  description: string;
  assigned_student_id?: string;
  assigned_student_name?: string;
  supervisor_id?: string;
  supervisor_name?: string;
  status: 'unassigned' | 'assigned' | 'in_progress' | 'completed';
  deadline?: Date;
  created_at: Date;
  updated_at: Date;
}

interface AssignmentFormData {
  topic_title: string;
  topic_description: string;
  student_id: string;
  supervisor_id: string;
  deadline: Date | undefined;
}

const DissertationManagement: React.FC = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [isAssignDialogOpen, setIsAssignDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedDissertation, setSelectedDissertation] = useState<DissertationDocument | null>(null);
  const [assignmentForm, setAssignmentForm] = useState<AssignmentFormData>({
    topic_title: '',
    topic_description: '',
    student_id: '',
    supervisor_id: '',
    deadline: undefined
  });

  // Fetch data
  const { data: students = [], isLoading: isLoadingStudents } = useQuery({
    queryKey: ['students'],
    queryFn: getStudents
  });

  const { data: teachers = [], isLoading: isLoadingTeachers } = useQuery({
    queryKey: ['teachers'],
    queryFn: getTeachersWithoutSorting
  });

  const { data: dissertations = [], isLoading: isLoadingDissertations, refetch: refetchDissertations } = useQuery({
    queryKey: ['admin-dissertations'],
    queryFn: async () => {
      console.log('Fetching dissertations for admin dashboard...');
      // Get all dissertations from all users
      const allUsers = await getUsers();
      console.log('All users:', allUsers);
      console.log('Students found:', allUsers.filter(u => u.role === 'student'));
      console.log('Teachers found:', allUsers.filter(u => u.role === 'teacher'));
      const allDissertations = [];
      
      // Fetch dissertations where users are students
      for (const user of allUsers) {
        if (user.role === 'student') {
          try {
            console.log(`Fetching dissertations for student ${user.id} (${user.name})`);
            const userDissertations = await dissertationService.getUserDissertations(user.id, 'student');
            console.log(`Found ${userDissertations.length} dissertations for student ${user.name}:`, userDissertations);
            allDissertations.push(...userDissertations);
          } catch (error) {
            console.error(`Error fetching dissertations for student ${user.id}:`, error);
          }
        }
      }
      
      // Also fetch dissertations where users are supervisors
      for (const user of allUsers) {
        if (user.role === 'teacher') {
          try {
            console.log(`Fetching dissertations for supervisor ${user.id} (${user.name})`);
            const supervisorDissertations = await dissertationService.getUserDissertations(user.id, 'supervisor');
            console.log(`Found ${supervisorDissertations.length} dissertations for supervisor ${user.name}:`, supervisorDissertations);
            
            // Only add dissertations that aren't already in the list
            for (const dissertation of supervisorDissertations) {
              if (!allDissertations.some(d => d.id === dissertation.id)) {
                allDissertations.push(dissertation);
              }
            }
          } catch (error) {
            console.error(`Error fetching dissertations for supervisor ${user.id}:`, error);
          }
        }
      }
      
      console.log(`Total unique dissertations found: ${allDissertations.length}`);
      console.log('All dissertations:', allDissertations);
      return allDissertations;
    },
    staleTime: 0, // Always refetch
    refetchOnWindowFocus: true,
    refetchOnMount: true
  });

  // Create assignment mutation
  const createAssignmentMutation = useMutation({
    mutationFn: async (data: AssignmentFormData) => {
      console.log('Creating dissertation with data:', data);
      const dissertationData = {
        title: data.topic_title,
        content: '<p>Begin your dissertation here...</p>',
        student_id: data.student_id,
        supervisor_id: data.supervisor_id,
        last_edited_by: data.student_id,
        status: 'draft' as const,
        auto_save_enabled: true,
        submission_deadline: data.deadline,
        abstract: data.topic_description
      };
      
      console.log('Dissertation data to be created:', dissertationData);
      const dissertationId = await dissertationService.createDissertation(dissertationData);
      console.log('Created dissertation with ID:', dissertationId);
      
      // Immediately test if we can fetch it back
      console.log('Testing immediate fetch of created dissertation...');
      try {
        const createdDissertation = await dissertationService.getDissertationById(dissertationId);
        console.log('Immediately fetched dissertation:', createdDissertation);
        
        // Also test fetching by student ID
        const studentDissertations = await dissertationService.getUserDissertations(data.student_id, 'student');
        console.log(`Dissertations for student ${data.student_id}:`, studentDissertations);
      } catch (error) {
        console.error('Error testing immediate fetch:', error);
      }
      
      return dissertationId;
    },
    onSuccess: (dissertationId) => {
      console.log('Mutation success, dissertation ID:', dissertationId);
      toast({
        title: "Success",
        description: "Dissertation assignment created successfully"
      });
      setIsAssignDialogOpen(false);
      resetAssignmentForm();
      // Invalidate cache and refetch
      queryClient.invalidateQueries({ queryKey: ['admin-dissertations'] });
      queryClient.invalidateQueries({ queryKey: ['students'] });
      queryClient.invalidateQueries({ queryKey: ['teachers'] });
      console.log('Triggering refetch...');
      // Use a longer delay to ensure Firebase has propagated the changes
      setTimeout(() => {
        console.log('Performing refetch of dissertation data...');
        refetchDissertations().then(() => {
          console.log('Refetch completed');
        }).catch(err => {
          console.error('Error during refetch:', err);
        });
      }, 2000); // Longer delay to allow Firebase to propagate
    },
    onError: (error) => {
      console.error('Error creating assignment:', error);
      toast({
        title: "Error",
        description: "Failed to create dissertation assignment",
        variant: "destructive"
      });
    }
  });

  // Update assignment mutation
  const updateAssignmentMutation = useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<DissertationDocument> }) => {
      return await dissertationService.updateDissertation(id, data);
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Dissertation updated successfully"
      });
      setIsEditDialogOpen(false);
      setSelectedDissertation(null);
      queryClient.invalidateQueries({ queryKey: ['admin-dissertations'] });
      refetchDissertations();
    },
    onError: (error) => {
      console.error('Error updating assignment:', error);
      toast({
        title: "Error",
        description: "Failed to update dissertation",
        variant: "destructive"
      });
    }
  });

  // Delete assignment mutation
  const deleteAssignmentMutation = useMutation({
    mutationFn: async (id: string) => {
      return await dissertationService.deleteDissertation(id);
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Dissertation deleted successfully"
      });
      queryClient.invalidateQueries({ queryKey: ['admin-dissertations'] });
      refetchDissertations();
    },
    onError: (error) => {
      console.error('Error deleting assignment:', error);
      toast({
        title: "Error",
        description: "Failed to delete dissertation",
        variant: "destructive"
      });
    }
  });

  const resetAssignmentForm = () => {
    setAssignmentForm({
      topic_title: '',
      topic_description: '',
      student_id: '',
      supervisor_id: '',
      deadline: undefined
    });
  };

  // Debug function to manually test data fetching
  const debugDataFetching = async () => {
    console.log('=== DEBUG: Manual data fetching ===');
    try {
      console.log('1. Fetching users...');
      const users = await getUsers();
      console.log('Users:', users);
      
      console.log('2. Fetching students...');
      const studentsData = await getStudents();
      console.log('Students:', studentsData);
      
      console.log('3. Testing dissertation service directly...');
      const students = users.filter(u => u.role === 'student');
      for (const student of students) {
        console.log(`Testing dissertations for student: ${student.name} (${student.id})`);
        try {
          const dissertations = await dissertationService.getUserDissertations(student.id, 'student');
          console.log(`- Found ${dissertations.length} dissertations:`, dissertations);
        } catch (error) {
          console.error(`- Error for student ${student.name}:`, error);
        }
      }
      
      console.log('4. Testing dissertations for teachers (supervisors)...');
      const teachers = users.filter(u => u.role === 'teacher');
      for (const teacher of teachers) {
        console.log(`Testing dissertations for supervisor: ${teacher.name} (${teacher.id})`);
        try {
          const dissertations = await dissertationService.getUserDissertations(teacher.id, 'supervisor');
          console.log(`- Found ${dissertations.length} dissertations:`, dissertations);
        } catch (error) {
          console.error(`- Error for supervisor ${teacher.name}:`, error);
        }
      }
    } catch (error) {
      console.error('Debug error:', error);
    }
    console.log('=== END DEBUG ===');
  };

  const handleCreateAssignment = () => {
    if (!assignmentForm.topic_title.trim()) {
      toast({
        title: "Error",
        description: "Topic title is required",
        variant: "destructive"
      });
      return;
    }

    if (!assignmentForm.student_id) {
      toast({
        title: "Error",
        description: "Please select a student",
        variant: "destructive"
      });
      return;
    }

    if (!assignmentForm.supervisor_id) {
      toast({
        title: "Error",
        description: "Please select a supervisor",
        variant: "destructive"
      });
      return;
    }

    createAssignmentMutation.mutate(assignmentForm);
  };

  const handleEditAssignment = (dissertation: DissertationDocument) => {
    setSelectedDissertation(dissertation);
    setIsEditDialogOpen(true);
  };

  const handleUpdateAssignment = () => {
    if (!selectedDissertation) return;

    updateAssignmentMutation.mutate({
      id: selectedDissertation.id,
      data: {
        title: selectedDissertation.title,
        supervisor_id: selectedDissertation.supervisor_id,
        submission_deadline: selectedDissertation.submission_deadline,
        abstract: selectedDissertation.abstract
      }
    });
  };

  const handleDeleteAssignment = (id: string) => {
    deleteAssignmentMutation.mutate(id);
  };

  // Filter dissertations
  const filteredDissertations = dissertations.filter(dissertation => {
    const matchesSearch = dissertation.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         getStudentName(dissertation.student_id).toLowerCase().includes(searchTerm.toLowerCase()) ||
                         getSupervisorName(dissertation.supervisor_id).toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || dissertation.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const getStudentName = (studentId: string) => {
    const student = students.find(s => s.id === studentId);
    return student?.name || 'Unknown Student';
  };

  const getSupervisorName = (supervisorId: string) => {
    const supervisor = teachers.find(t => t.id === supervisorId);
    return supervisor?.name || 'Unassigned';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft': return 'bg-gray-100 text-gray-800';
      case 'ready_for_review': return 'bg-blue-100 text-blue-800';
      case 'under_review': return 'bg-yellow-100 text-yellow-800';
      case 'approved': return 'bg-green-100 text-green-800';
      case 'needs_revision': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved': return <CheckCircle className="w-4 h-4" />;
      case 'needs_revision': return <AlertTriangle className="w-4 h-4" />;
      case 'ready_for_review': return <Eye className="w-4 h-4" />;
      case 'under_review': return <Clock className="w-4 h-4" />;
      default: return <FileText className="w-4 h-4" />;
    }
  };

  // Calculate statistics
  const totalDissertations = dissertations.length;
  const activeDissertations = dissertations.filter(d => d.status !== 'approved').length;
  const completedDissertations = dissertations.filter(d => d.status === 'approved').length;
  const pendingReviews = dissertations.filter(d => d.status === 'ready_for_review').length;

  if (isLoadingStudents || isLoadingTeachers || isLoadingDissertations) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-700" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Dissertation Management</h2>
          <p className="text-muted-foreground">Assign dissertation topics to students and supervisors</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={debugDataFetching}>
            Debug Data
          </Button>
          <Button onClick={() => setIsAssignDialogOpen(true)}>
            <Plus className="w-4 h-4 mr-2" />
            Assign New Topic
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Dissertations</p>
                <p className="text-2xl font-bold">{totalDissertations}</p>
              </div>
              <PenTool className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Dissertations</p>
                <p className="text-2xl font-bold">{activeDissertations}</p>
              </div>
              <Clock className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Pending Reviews</p>
                <p className="text-2xl font-bold">{pendingReviews}</p>
              </div>
              <Eye className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Completed</p>
                <p className="text-2xl font-bold">{completedDissertations}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search dissertations, students, or supervisors..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="draft">Draft</SelectItem>
            <SelectItem value="ready_for_review">Ready for Review</SelectItem>
            <SelectItem value="under_review">Under Review</SelectItem>
            <SelectItem value="approved">Approved</SelectItem>
            <SelectItem value="needs_revision">Needs Revision</SelectItem>
          </SelectContent>
        </Select>
        <Button variant="outline" onClick={() => refetchDissertations()}>
          <RefreshCw className="w-4 h-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Dissertations List */}
      <div className="grid gap-4">
        {filteredDissertations.map((dissertation) => (
          <Card key={dissertation.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-2">
                    <h3 className="font-semibold text-lg truncate">{dissertation.title}</h3>
                    <Badge className={getStatusColor(dissertation.status)}>
                      {getStatusIcon(dissertation.status)}
                      <span className="ml-1 capitalize">{dissertation.status.replace('_', ' ')}</span>
                    </Badge>
                  </div>
                  
                  {dissertation.abstract && (
                    <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                      {dissertation.abstract}
                    </p>
                  )}
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div className="flex items-center gap-2">
                      <GraduationCap className="w-4 h-4 text-blue-500" />
                      <span className="font-medium">Student:</span>
                      <span>{getStudentName(dissertation.student_id)}</span>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <UserCheck className="w-4 h-4 text-green-500" />
                      <span className="font-medium">Supervisor:</span>
                      <span>{getSupervisorName(dissertation.supervisor_id)}</span>
                    </div>
                    
                    {dissertation.submission_deadline && (
                      <div className="flex items-center gap-2">
                        <Calendar className="w-4 h-4 text-orange-500" />
                        <span className="font-medium">Deadline:</span>
                        <span>{format(new Date(dissertation.submission_deadline), 'MMM dd, yyyy')}</span>
                      </div>
                    )}
                  </div>
                  
                  <div className="flex items-center gap-4 mt-3 text-xs text-muted-foreground">
                    <span>Words: {dissertation.word_count || 0}</span>
                    <span>Pages: {dissertation.page_count || 1}</span>
                    <span>Last updated: {format(new Date(dissertation.updated_at), 'MMM dd, yyyy')}</span>
                  </div>
                </div>
                
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <MoreVertical className="w-4 h-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => handleEditAssignment(dissertation)}>
                      <Edit className="w-4 h-4 mr-2" />
                      Edit Assignment
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => window.open(`/dissertation/${dissertation.id}`, '_blank')}>
                      <Eye className="w-4 h-4 mr-2" />
                      View Dissertation
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      onClick={() => handleDeleteAssignment(dissertation.id)}
                      className="text-red-600"
                    >
                      <Trash2 className="w-4 h-4 mr-2" />
                      Delete Assignment
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </CardContent>
          </Card>
        ))}
        
        {filteredDissertations.length === 0 && (
          <Card>
            <CardContent className="p-8 text-center">
              <PenTool className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="font-semibold mb-2">No dissertations found</h3>
              <p className="text-muted-foreground mb-4">
                {searchTerm || statusFilter !== 'all' 
                  ? 'Try adjusting your search or filter criteria.' 
                  : 'Start by assigning dissertation topics to students.'}
              </p>
              {!searchTerm && statusFilter === 'all' && (
                <Button onClick={() => setIsAssignDialogOpen(true)}>
                  <Plus className="w-4 h-4 mr-2" />
                  Assign First Topic
                </Button>
              )}
            </CardContent>
          </Card>
        )}
      </div>

      {/* Assign Topic Dialog */}
      <Dialog open={isAssignDialogOpen} onOpenChange={setIsAssignDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Assign Dissertation Topic</DialogTitle>
            <DialogDescription>
              Create a new dissertation assignment by selecting a student and supervisor.
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="topic_title">Topic Title</Label>
              <Input
                id="topic_title"
                value={assignmentForm.topic_title}
                onChange={(e) => setAssignmentForm(prev => ({ ...prev, topic_title: e.target.value }))}
                placeholder="Enter dissertation topic title"
              />
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="topic_description">Topic Description</Label>
              <Textarea
                id="topic_description"
                value={assignmentForm.topic_description}
                onChange={(e) => setAssignmentForm(prev => ({ ...prev, topic_description: e.target.value }))}
                placeholder="Provide a detailed description of the dissertation topic"
                rows={4}
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="student_id">Student</Label>
                <Select value={assignmentForm.student_id} onValueChange={(value) => 
                  setAssignmentForm(prev => ({ ...prev, student_id: value }))
                }>
                  <SelectTrigger>
                    <SelectValue placeholder="Select student" />
                  </SelectTrigger>
                  <SelectContent>
                    {students.map((student) => (
                      <SelectItem key={student.id} value={student.id}>
                        {student.name} ({student.student_id})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="grid gap-2">
                <Label htmlFor="supervisor_id">Supervisor</Label>
                <Select value={assignmentForm.supervisor_id} onValueChange={(value) => 
                  setAssignmentForm(prev => ({ ...prev, supervisor_id: value }))
                }>
                  <SelectTrigger>
                    <SelectValue placeholder="Select supervisor" />
                  </SelectTrigger>
                  <SelectContent>
                    {teachers.map((teacher) => (
                      <SelectItem key={teacher.id} value={teacher.id}>
                        {teacher.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="grid gap-2">
              <Label>Submission Deadline (Optional)</Label>
              <DatePicker
                selected={assignmentForm.deadline}
                onSelect={(date) => setAssignmentForm(prev => ({ ...prev, deadline: date }))}
                placeholder="Select deadline date"
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAssignDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateAssignment} disabled={createAssignmentMutation.isPending}>
              {createAssignmentMutation.isPending ? 'Creating...' : 'Create Assignment'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Assignment Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Dissertation Assignment</DialogTitle>
            <DialogDescription>
              Update the dissertation assignment details.
            </DialogDescription>
          </DialogHeader>
          
          {selectedDissertation && (
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="edit_title">Topic Title</Label>
                <Input
                  id="edit_title"
                  value={selectedDissertation.title}
                  onChange={(e) => setSelectedDissertation(prev => 
                    prev ? { ...prev, title: e.target.value } : null
                  )}
                />
              </div>
              
              <div className="grid gap-2">
                <Label htmlFor="edit_description">Topic Description</Label>
                <Textarea
                  id="edit_description"
                  value={selectedDissertation.abstract || ''}
                  onChange={(e) => setSelectedDissertation(prev => 
                    prev ? { ...prev, abstract: e.target.value } : null
                  )}
                  rows={4}
                />
              </div>
              
              <div className="grid gap-2">
                <Label htmlFor="edit_supervisor">Supervisor</Label>
                <Select 
                  value={selectedDissertation.supervisor_id} 
                  onValueChange={(value) => setSelectedDissertation(prev => 
                    prev ? { ...prev, supervisor_id: value } : null
                  )}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select supervisor" />
                  </SelectTrigger>
                  <SelectContent>
                    {teachers.map((teacher) => (
                      <SelectItem key={teacher.id} value={teacher.id}>
                        {teacher.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="grid gap-2">
                <Label>Submission Deadline</Label>
                <DatePicker
                  selected={selectedDissertation.submission_deadline ? new Date(selectedDissertation.submission_deadline) : undefined}
                  onSelect={(date) => setSelectedDissertation(prev => 
                    prev ? { ...prev, submission_deadline: date } : null
                  )}
                  placeholder="Select deadline date"
                />
              </div>
            </div>
          )}
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdateAssignment} disabled={updateAssignmentMutation.isPending}>
              {updateAssignmentMutation.isPending ? 'Updating...' : 'Update Assignment'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default DissertationManagement; 