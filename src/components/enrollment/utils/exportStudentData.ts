import * as XLSX from 'xlsx';
import { toast } from 'sonner';

interface StudentExportData {
  student: any;
  attendanceRecords: any[];
  academicRecords: any[];
  paymentRecords: any[];
}

// New interface for multiple students export
interface StudentsExportData {
  students: any[];
  courseName?: string;
}

export const exportStudentData = async ({
  student,
  attendanceRecords,
  academicRecords,
  paymentRecords,
}: StudentExportData) => {
  let loadingToastId: string | number | undefined;
  
  try {
    // Show loading toast and store the ID
    loadingToastId = toast.loading('Preparing student data for export...');
    console.log('Toast ID:', loadingToastId);
    
    // Basic student info for filename safety
    const studentId = student?.student_id || 'unknown';
    const studentName = student?.name || 'student';
    
    // Create Excel workbook
    const wb = XLSX.utils.book_new();
    
    // Add sheets with basic error handling for each
    try {
      // Personal Info
      const personalData = [
        ['Student ID', student?.student_id || ''],
        ['Full Name', student?.name || ''],
        ['Date of Birth', student?.date_of_birth || ''],
        ['Gender', student?.gender || ''],
        ['Nationality', student?.nationality || ''],
        ['Address', student?.address || ''],
        ['Mobile Number', student?.mobile_number || ''],
        ['WhatsApp', student?.whatsapp_number || ''],
        ['Email', student?.email || ''],
        ['Enrollment Status', student?.enrollment_status || ''],
      ];
      
      const personalWs = XLSX.utils.aoa_to_sheet(personalData);
      XLSX.utils.book_append_sheet(wb, personalWs, 'Personal');
    } catch (e) {
      console.error('Error creating personal sheet:', e);
    }
    
    try {
      // Course Info
      const courseData = [
        ['Course', student?.course?.name || ''],
        ['Course Code', student?.course?.code || ''],
        ['Course Fee', student?.course?.fee || ''],
        ['Level', student?.level?.name || ''],
      ];
      
      const courseWs = XLSX.utils.aoa_to_sheet(courseData);
      XLSX.utils.book_append_sheet(wb, courseWs, 'Course');
    } catch (e) {
      console.error('Error creating course sheet:', e);
    }
    
    try {
      // Attendance
      let attendanceData = [['Date', 'Status', 'Notes']];
      
      if (Array.isArray(attendanceRecords) && attendanceRecords.length > 0) {
        for (const record of attendanceRecords) {
          attendanceData.push([
            record?.date || '',
            record?.status || '',
            record?.notes || ''
          ]);
        }
      } else {
        attendanceData.push(['No attendance records', '', '']);
      }
      
      const attendanceWs = XLSX.utils.aoa_to_sheet(attendanceData);
      XLSX.utils.book_append_sheet(wb, attendanceWs, 'Attendance');
    } catch (e) {
      console.error('Error creating attendance sheet:', e);
    }
    
    try {
      // Academic Records
      let academicData = [['Subject', 'Grade', 'Score']];
      
      if (Array.isArray(academicRecords) && academicRecords.length > 0) {
        for (const record of academicRecords) {
          academicData.push([
            record?.subject || '',
            record?.grade || '',
            record?.score || ''
          ]);
        }
      } else {
        academicData.push(['No academic records', '', '']);
      }
      
      const academicWs = XLSX.utils.aoa_to_sheet(academicData);
      XLSX.utils.book_append_sheet(wb, academicWs, 'Academic');
    } catch (e) {
      console.error('Error creating academic sheet:', e);
    }
    
    try {
      // Payment Records
      let paymentData = [['Date', 'Amount', 'Type', 'Status']];
      
      if (Array.isArray(paymentRecords) && paymentRecords.length > 0) {
        for (const record of paymentRecords) {
          paymentData.push([
            record?.date || '',
            record?.amount || '',
            record?.type || '',
            record?.status || ''
          ]);
        }
      } else {
        paymentData.push(['No payment records', '', '', '']);
      }
      
      const paymentWs = XLSX.utils.aoa_to_sheet(paymentData);
      XLSX.utils.book_append_sheet(wb, paymentWs, 'Payments');
    } catch (e) {
      console.error('Error creating payment sheet:', e);
    }
    
    // Write file
    console.log('Generating Excel file...');
    const buffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
    const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    
    // Force download using a direct approach
    const date = new Date().toISOString().split('T')[0];
    const fileName = `${studentId}_${studentName.replace(/\s+/g, '_')}_${date}.xlsx`;
    
    try {
      console.log('Triggering download...');
      // Use the FileSaver approach which is more reliable
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      
      // Clean up
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      // Success - make sure to dismiss toast
      console.log('Download successful, dismissing toast:', loadingToastId);
      if (loadingToastId) {
        toast.dismiss(loadingToastId);
        toast.success('Student record exported successfully');
      }
      return true;
    } catch (downloadError) {
      console.error('Download error:', downloadError);
      // Make sure to dismiss toast even if download fails
      if (loadingToastId) {
        toast.dismiss(loadingToastId);
        toast.error('Error downloading the file');
      }
      return false;
    }
  } catch (error) {
    console.error('Export error:', error);
    // Ensure toast is dismissed even on general error
    if (loadingToastId) {
      toast.dismiss(loadingToastId);
      toast.error('Failed to export student data');
    }
    return false;
  } finally {
    // Triple-check: dismiss toast in finally block to ensure it happens
    console.log('Finally block, ensuring toast is dismissed:', loadingToastId);
    if (loadingToastId) {
      setTimeout(() => {
        toast.dismiss(loadingToastId);
      }, 500);
    }
  }
};

// New function to export multiple students
export const exportStudentsData = async ({
  students,
  courseName
}: StudentsExportData) => {
  let loadingToastId: string | number | undefined;
  
  try {
    // Show loading toast and store the ID
    loadingToastId = toast.loading('Preparing students data for export...');
    
    // Create Excel workbook
    const wb = XLSX.utils.book_new();
    
    // Create headers for students sheet
    const headers = [
      'Student ID', 
      'Name', 
      'Date of Birth', 
      'Nationality', 
      'Gender', 
      'Address', 
      'Mobile Number', 
      'WhatsApp Number',
      'Parent Name',
      'Parent Mobile',
      'Parent WhatsApp',
      'Parent Email',
      'Parent Occupation',
      'Course',
      'Level',
      'Enrollment Status',
      'Payment Status',
      'Registration Date'
    ];
    
    // Create data rows
    const studentsData = [headers];
    
    if (Array.isArray(students) && students.length > 0) {
      for (const student of students) {
        studentsData.push([
          student?.student_id || '',
          student?.name || '',
          student?.date_of_birth || '',
          student?.nationality || '',
          student?.gender || '',
          student?.address || '',
          student?.mobile_number || '',
          student?.whatsapp_number || '',
          student?.parent_name || '',
          student?.parent_mobile || '',
          student?.parent_whatsapp || '',
          student?.parent_email || '',
          student?.parent_occupation || '',
          student?.course?.name || '',
          student?.level?.name || '',
          student?.enrollment_status || '',
          student?.payment_status || '',
          student?.date_of_registration || ''
        ]);
      }
    } else {
      studentsData.push(Array(headers.length).fill('No student records'));
    }
    
    // Create worksheet and append to workbook
    const ws = XLSX.utils.aoa_to_sheet(studentsData);
    XLSX.utils.book_append_sheet(wb, ws, 'Students');
    
    // Write file
    const buffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
    const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    
    // Generate filename
    const date = new Date().toISOString().split('T')[0];
    let fileName = `Students_${date}.xlsx`;
    
    // If filtering by course, include course name in filename
    if (courseName && courseName !== 'all') {
      fileName = `Students_${courseName.replace(/\s+/g, '_')}_${date}.xlsx`;
    }
    
    try {
      // Trigger download
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      
      // Clean up
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      // Success - dismiss toast
      if (loadingToastId) {
        toast.dismiss(loadingToastId);
        toast.success('Students data exported successfully');
      }
      return true;
    } catch (downloadError) {
      console.error('Download error:', downloadError);
      if (loadingToastId) {
        toast.dismiss(loadingToastId);
        toast.error('Error downloading the file');
      }
      return false;
    }
  } catch (error) {
    console.error('Export error:', error);
    if (loadingToastId) {
      toast.dismiss(loadingToastId);
      toast.error('Failed to export students data');
    }
    return false;
  } finally {
    if (loadingToastId) {
      setTimeout(() => {
        toast.dismiss(loadingToastId);
      }, 500);
    }
  }
}; 