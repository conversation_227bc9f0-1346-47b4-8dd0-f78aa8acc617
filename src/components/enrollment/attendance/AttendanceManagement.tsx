import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import type { Student } from "@/types/student";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Loader2, AlertCircle, FileText } from "lucide-react";
import { toast } from "sonner";
import { DatePicker } from "@/components/ui/date-picker";
import { format, isBefore, startOfDay } from "date-fns";
import { cn } from "@/lib/utils";
import { getStudents } from "@/api/students";
import { getCourses } from "@/api/courses";
import { getLevels } from "@/api/levels";
import { getAttendanceRecords, markStudentAttendance, bulkMarkAttendance } from "@/api/attendance";
import { exportAttendanceToPDF } from "@/utils/attendance-export-pdf";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface BulkAttendanceModalProps {
  students: Student[];
  onClose: () => void;
}

export const AttendanceManagement = () => {
  const [selectedCourse, setSelectedCourse] = useState<string | null>(null);
  const [selectedLevel, setSelectedLevel] = useState<string | null>(null);
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const queryClient = useQueryClient();
  const [isDateInPast, setIsDateInPast] = useState(false);
  const [isDateInFuture, setIsDateInFuture] = useState(false);
  const [isDateDisabled, setIsDateDisabled] = useState(false);

  // Check if selected date is valid for attendance marking (only allow today)
  useEffect(() => {
    const today = startOfDay(new Date());
    const selectedDay = startOfDay(selectedDate);
    
    // Past date check (before today)
    const isPast = isBefore(selectedDay, today);
    setIsDateInPast(isPast);
    
    // Future date check (after today)
    const isFuture = isBefore(today, selectedDay);
    setIsDateInFuture(isFuture);
    
    // Only enable attendance marking for today
    setIsDateDisabled(isPast || isFuture);
    
    console.log(`Date status: Selected=${format(selectedDay, 'yyyy-MM-dd')}, isPast=${isPast}, isFuture=${isFuture}`);
  }, [selectedDate]);

  const { data: students = [], isLoading: studentsLoading } = useQuery({
    queryKey: ['students'],
    queryFn: getStudents
  });

  const { data: levels = [], isLoading: levelsLoading } = useQuery({
    queryKey: ['levels'],
    queryFn: getLevels
  });

  const { data: courses = [], isLoading: coursesLoading } = useQuery({
    queryKey: ['courses'],
    queryFn: getCourses
  });

  const { data: attendanceRecords = [], isLoading: attendanceLoading } = useQuery({
    queryKey: ['attendance', format(selectedDate, 'yyyy-MM-dd')],
    queryFn: () => getAttendanceRecords(format(selectedDate, 'yyyy-MM-dd')),
    enabled: !!selectedDate
  });

  // Filter students based on selected course and level
  const filteredStudents = students.filter(student => {
    if (selectedCourse && student.course_id !== selectedCourse) {
      return false;
    }
    if (selectedLevel && student.level_id !== selectedLevel) {
      return false;
    }
    return true;
  });

  // Create a map of student IDs to attendance status for the selected date
  const attendanceMap = new Map();
  attendanceRecords.forEach(record => {
    // Map using the record ID and student_id as a fallback
    attendanceMap.set(record.student_id, {
      status: record.status,
      recordId: record.id
    });
    console.log(`Mapped student ${record.student_id} with status ${record.status}`);
  });

  const markAttendanceMutation = useMutation({
    mutationFn: ({ studentId, status, notes }: { studentId: string; status: 'present' | 'absent'; notes?: string }) => 
      markStudentAttendance(studentId, format(selectedDate, 'yyyy-MM-dd'), status, notes),
    onSuccess: () => {
      // Force refresh attendance data after mutation
      queryClient.invalidateQueries({ queryKey: ['attendance', format(selectedDate, 'yyyy-MM-dd')] });
      console.log('Invalidated attendance queries after mutation');
    }
  });

  const bulkMarkAttendanceMutation = useMutation({
    mutationFn: ({ studentIds, status }: { studentIds: string[]; status: 'present' | 'absent' }) => 
      bulkMarkAttendance(studentIds, format(selectedDate, 'yyyy-MM-dd'), status),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['attendance', format(selectedDate, 'yyyy-MM-dd')] });
    }
  });

  const handleMarkAttendance = async (studentId: string, status: 'present' | 'absent') => {
    if (isDateInPast) {
      toast.error("Cannot mark attendance for past dates");
      return;
    }
    
    if (isDateInFuture) {
      toast.error("Cannot mark attendance for future dates");
      return;
    }
    
    // Get current status to enable toggling
    const attendanceRecord = attendanceMap.get(studentId);
    const currentStatus = attendanceRecord?.status;
    
    console.log(`Current status for student ${studentId}: ${currentStatus}, trying to set to: ${status}`);
    
    // If clicking the same status that's already set, do nothing
    if (currentStatus === status) {
      console.log(`Student ${studentId} already has status ${status}, skipping update`);
      return;
    }
    
    console.log(`Marking attendance for student ${studentId} as ${status} on ${format(selectedDate, 'yyyy-MM-dd')}`);
    try {
      markAttendanceMutation.mutate(
        { 
          studentId, 
          status,
          notes: '' // Explicitly provide empty string for notes
        },
        {
          onSuccess: () => {
            console.log(`Successfully marked attendance for student ${studentId} as ${status}`);
            
            // Manually update the local map for immediate UI feedback
            attendanceMap.set(studentId, {
              status: status,
              recordId: attendanceRecord?.recordId || 'new-record'
            });
            
            toast.success(`Attendance marked as ${status}`);
          },
          onError: (error) => {
            console.error(`Error marking attendance for student ${studentId}:`, error);
            toast.error('Failed to mark attendance');
          }
        }
      );
    } catch (error) {
      console.error(`Error in handleMarkAttendance for student ${studentId}:`, error);
      toast.error('Failed to mark attendance');
    }
  };

  const handleMarkAllPresent = () => {
    if (isDateInPast) {
      toast.error("Cannot mark attendance for past dates");
      return;
    }
    
    if (isDateInFuture) {
      toast.error("Cannot mark attendance for future dates");
      return;
    }
    
    const studentIds = filteredStudents.map(student => student.id);
    console.log(`Marking all ${studentIds.length} students as present on ${format(selectedDate, 'yyyy-MM-dd')}`);
    
    try {
      bulkMarkAttendanceMutation.mutate({ studentIds, status: 'present' }, {
        onSuccess: () => {
          console.log(`Successfully marked ${studentIds.length} students as present`);
          toast.success(`Marked ${studentIds.length} students as present`);
        },
        onError: (error) => {
          console.error('Error marking all students as present:', error);
          toast.error('Failed to mark attendance');
        }
      });
    } catch (error) {
      console.error('Error in handleMarkAllPresent:', error);
      toast.error('Failed to mark attendance');
    }
  };

  const handleMarkAllAbsent = () => {
    if (isDateInPast) {
      toast.error("Cannot mark attendance for past dates");
      return;
    }
    
    if (isDateInFuture) {
      toast.error("Cannot mark attendance for future dates");
      return;
    }
    
    const studentIds = filteredStudents.map(student => student.id);
    console.log(`Marking all ${studentIds.length} students as absent on ${format(selectedDate, 'yyyy-MM-dd')}`);
    
    try {
      bulkMarkAttendanceMutation.mutate({ studentIds, status: 'absent' }, {
        onSuccess: () => {
          console.log(`Successfully marked ${studentIds.length} students as absent`);
          toast.success(`Marked ${studentIds.length} students as absent`);
        },
        onError: (error) => {
          console.error('Error marking all students as absent:', error);
          toast.error('Failed to mark attendance');
        }
      });
    } catch (error) {
      console.error('Error in handleMarkAllAbsent:', error);
      toast.error('Failed to mark attendance');
    }
  };

  // Export attendance handler
  const handleExportAttendance = async (range: 'day' | 'week' | 'month') => {
    try {
      toast.info(`Preparing ${range} attendance PDF export...`);
      
      // Show export parameters to user
      if (selectedCourse) {
        const course = courses.find(c => c.id === selectedCourse);
        if (course) {
          let courseMsg = `${range} attendance for course "${course.name}"`;
          
          if (selectedLevel) {
            const level = levels.find(l => l.id === selectedLevel);
            if (level) {
              courseMsg += ` - level "${level.name}"`;
            }
          }
          
          toast.info(courseMsg);
        }
      }
      
      // Call the PDF export function
      await exportAttendanceToPDF(range, selectedDate, selectedCourse || undefined, selectedLevel || undefined);
    } catch (error) {
      console.error(`Error exporting ${range} attendance:`, error);
      toast.error(`Failed to export attendance: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const isLoading = studentsLoading || levelsLoading || coursesLoading || attendanceLoading;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold tracking-tight">Attendance Management</h2>

        {/* Export Dropdown Menu */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Export PDF
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => handleExportAttendance('day')}>
              Export Today as PDF
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleExportAttendance('week')}>
              Export This Week as PDF
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleExportAttendance('month')}>
              Export This Month as PDF
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Attendance Controls</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">Date</label>
              <DatePicker
                date={selectedDate}
                onSelect={(date) => date && setSelectedDate(date)}
              />
              {isDateInPast && (
                <p className="text-sm text-orange-500 mt-1">
                  Viewing past attendance - cannot be modified
                </p>
              )}
              {isDateInFuture && (
                <p className="text-sm text-blue-500 mt-1">
                  Future date - attendance can only be marked on the actual day
                </p>
              )}
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Course</label>
              <Select
                value={selectedCourse || "all"}
                onValueChange={(value) => setSelectedCourse(value === "all" ? null : value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All Courses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Courses</SelectItem>
                  {courses.map((course) => (
                    <SelectItem key={course.id} value={course.id}>
                      {course.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Level</label>
              <Select
                value={selectedLevel || "all"}
                onValueChange={(value) => setSelectedLevel(value === "all" ? null : value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All Levels" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Levels</SelectItem>
                  {levels.map((level) => (
                    <SelectItem key={level.id} value={level.id}>
                      {level.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex gap-2 mt-4">
            <Button 
              onClick={handleMarkAllPresent} 
              disabled={isLoading || filteredStudents.length === 0 || isDateDisabled}
            >
              Mark All Present
            </Button>
            <Button 
              onClick={handleMarkAllAbsent} 
              variant="destructive" 
              disabled={isLoading || filteredStudents.length === 0 || isDateDisabled}
            >
              Mark All Absent
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Student Attendance</CardTitle>
        </CardHeader>
        <CardContent>
          {isDateInPast && (
            <Alert className="mb-4 bg-orange-50 text-orange-800 border-orange-200">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                You are viewing attendance for a past date. Attendance records for past dates cannot be modified.
              </AlertDescription>
            </Alert>
          )}
          {isDateInFuture && (
            <Alert className="mb-4 bg-blue-50 text-blue-800 border-blue-200">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                You are viewing a future date. Attendance can only be marked on the actual day.
              </AlertDescription>
            </Alert>
          )}
          
          {isLoading ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : filteredStudents.length === 0 ? (
            <div className="text-center py-8">
              <p>No students found</p>
            </div>
          ) : (
            <div className="border rounded-md">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Student</TableHead>
                    <TableHead>ID</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredStudents.map((student) => {
                    const attendanceStatus = attendanceMap.get(student.id) || null;
                    
                    return (
                      <TableRow key={student.id}>
                        <TableCell className="font-medium">
                          <div className="flex items-center gap-2">
                            <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center">
                              {student.passport_picture ? (
                                <img 
                                  src={student.passport_picture} 
                                  alt={student.name} 
                                  className="w-full h-full rounded-full object-cover"
                                />
                              ) : (
                                <span className="text-gray-500 text-xs font-medium">
                                  {student.name.substring(0, 2).toUpperCase()}
                                </span>
                              )}
                            </div>
                            {student.name}
                          </div>
                        </TableCell>
                        <TableCell className="text-sm text-muted-foreground">
                          {student.student_id}
                        </TableCell>
                        <TableCell>
                          {attendanceStatus ? (
                            <span className={cn(
                              "px-2 py-1 rounded-full text-xs font-medium",
                              attendanceStatus.status === 'present' && "bg-green-100 text-green-800",
                              attendanceStatus.status === 'absent' && "bg-red-100 text-red-800"
                            )}>
                              {attendanceStatus.status.charAt(0).toUpperCase() + attendanceStatus.status.slice(1)}
                            </span>
                          ) : (
                            <span className="text-muted-foreground text-sm">Not marked</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="flex gap-2">
                            <Button
                              variant={attendanceStatus?.status === 'present' ? "default" : "outline"}
                              size="sm"
                              className={cn(
                                attendanceStatus?.status === 'present' && "bg-green-600 hover:bg-green-700"
                              )}
                              onClick={() => handleMarkAttendance(student.id, 'present')}
                              disabled={isDateDisabled}
                            >
                              Present
                            </Button>
                            <Button
                              variant={attendanceStatus?.status === 'absent' ? "default" : "outline"}
                              size="sm"
                              className={cn(
                                attendanceStatus?.status === 'absent' && "bg-red-600 hover:bg-red-700"
                              )}
                              onClick={() => handleMarkAttendance(student.id, 'absent')}
                              disabled={isDateDisabled}
                            >
                              Absent
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export const BulkAttendanceModal: React.FC<BulkAttendanceModalProps> = ({ students, onClose }) => {
  const [status, setStatus] = useState<'present' | 'absent'>('present');
  
  const handleSubmit = async () => {
    try {
      // Get current day info
      const today = startOfDay(new Date());
      const selectedDay = startOfDay(new Date()); // Using today
      
      // Check if selected day is today (should always be true in this context)
      const isPast = isBefore(selectedDay, today);
      const isFuture = isBefore(today, selectedDay);
      
      if (isPast) {
        toast.error("Cannot mark attendance for past dates");
        return;
      }
      
      if (isFuture) {
        toast.error("Cannot mark attendance for future dates");
        return;
      }
      
      const studentIds = students.map(student => student.id);
      
      await bulkMarkAttendance(studentIds, format(new Date(), 'yyyy-MM-dd'), status);
      toast.success(`Marked ${studentIds.length} students as ${status}`);
      onClose();
    } catch (error) {
      console.error('Error marking attendance:', error);
      toast.error('Failed to mark attendance');
    }
  };
  
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Mark Attendance for {students.length} Students</h3>
      
      <div>
        <label className="block text-sm font-medium mb-1">Status</label>
        <Select value={status} onValueChange={(value) => setStatus(value as any)}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="present">Present</SelectItem>
            <SelectItem value="absent">Absent</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      <div className="flex justify-end gap-2">
        <Button variant="outline" onClick={onClose}>Cancel</Button>
        <Button onClick={handleSubmit}>Mark Attendance</Button>
      </div>
    </div>
  );
};
