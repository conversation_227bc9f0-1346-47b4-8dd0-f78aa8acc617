import { Button } from "@/components/ui/button";

// Define the attendance status type
type AttendanceStatus = 'present' | 'absent' | 'late' | 'excused';

// Define the Student type
interface Student {
  id: string;
  student_id: string;
  name: string;
  [key: string]: any;
}

// Define the AttendanceRecord type
interface AttendanceRecord {
  id: string;
  student_id: string;
  date: string;
  status: AttendanceStatus;
  notes?: string;
  marked_by: string;
  created_at: string;
  [key: string]: any;
}

interface StudentAttendanceCardProps {
  student: Student;
  existingRecord?: AttendanceRecord;
  status: AttendanceStatus;
  onStatusChange: (status: AttendanceStatus) => void;
}

export const StudentAttendanceCard = ({
  student,
  existingRecord,
  status,
  onStatusChange,
}: StudentAttendanceCardProps) => {
  return (
    <div className="bg-white rounded-lg shadow p-4">
      <h3 className="font-semibold text-lg">{student.name}</h3>
      <p className="text-sm text-gray-500 mb-4">Student ID: {student.student_id}</p>
      <div className="flex flex-col gap-2">
        <Button
          size="sm"
          variant={status === "present" ? "default" : "outline"}
          onClick={() => onStatusChange("present")}
          className="w-full justify-center"
        >
          Present
        </Button>
        <Button
          size="sm"
          variant={status === "absent" ? "default" : "outline"}
          onClick={() => onStatusChange("absent")}
          className="w-full justify-center"
        >
          Absent
        </Button>
        <Button
          size="sm"
          variant={status === "late" ? "default" : "outline"}
          onClick={() => onStatusChange("late")}
          className="w-full justify-center"
        >
          Late
        </Button>
        <Button
          size="sm"
          variant={status === "excused" ? "default" : "outline"}
          onClick={() => onStatusChange("excused")}
          className="w-full justify-center"
        >
          Excused
        </Button>
      </div>
      {existingRecord && (
        <p className="text-sm text-gray-500 mt-4">
          Last marked: {new Date(existingRecord.created_at).toLocaleTimeString()}
        </p>
      )}
    </div>
  );
};
