
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { DatePicker } from "@/components/ui/date-picker";
import { Loader2 } from "lucide-react";

export const AttendanceReports = () => {
  const [startDate, setStartDate] = useState<Date>();
  const [endDate, setEndDate] = useState<Date>();

  const { data: records = [], isLoading } = useQuery({
    queryKey: ['attendance-reports', startDate, endDate],
    queryFn: async () => {
      let query = supabase
        .from('attendance_records')
        .select('*, student:students(name)');

      if (startDate) {
        query = query.gte('date', startDate.toISOString().split('T')[0]);
      }

      if (endDate) {
        query = query.lte('date', endDate.toISOString().split('T')[0]);
      }

      const { data, error } = await query;

      if (error) throw error;
      return data;
    }
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[50vh]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Attendance Reports</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex gap-4 mb-6">
          <DatePicker
            date={startDate}
            onSelect={setStartDate}
          />
          <DatePicker
            date={endDate}
            onSelect={setEndDate}
          />
        </div>

        <div className="space-y-4">
          {records.map((record: any) => (
            <div key={record.id} className="p-4 border rounded">
              <p className="font-medium">{record.student?.name}</p>
              <p className="text-sm text-gray-500">
                Date: {new Date(record.date).toLocaleDateString()}
              </p>
              <p>Status: {record.status}</p>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};
