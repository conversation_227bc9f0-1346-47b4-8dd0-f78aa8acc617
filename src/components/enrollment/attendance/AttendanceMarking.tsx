import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { getStudentAttendance, markStudentAttendance } from "@/api/attendance";
import { useUser } from "@/hooks/useUser";

interface AttendanceMarkingProps {
  studentId: string;
}

type AttendanceStatus = 'present' | 'absent' | 'late' | 'excused';

export const AttendanceMarking = ({ studentId }: AttendanceMarkingProps) => {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
  const [selectedCourse, setSelectedCourse] = useState<string>("");
  const queryClient = useQueryClient();
  const { user } = useUser();

  const { data: attendance = [], isLoading } = useQuery({
    queryKey: ['student-attendance', studentId],
    queryFn: () => getStudentAttendance(studentId),
    enabled: !!studentId
  });

  const markAttendanceMutation = useMutation({
    mutationFn: async ({ date, status, notes }: { 
      date: string, 
      status: AttendanceStatus, 
      notes?: string 
    }) => {
      if (!user) throw new Error("You must be logged in to mark attendance");
      
      return markStudentAttendance(studentId, date, status, notes);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['student-attendance'] });
      toast.success('Attendance marked successfully');
    },
    onError: (error: any) => {
      console.error('Error marking attendance:', error);
      toast.error(error.message || 'Failed to mark attendance');
    }
  });

  const handleMarkAttendance = async (status: AttendanceStatus) => {
    if (!selectedDate || !selectedCourse) {
      toast.error('Please select a date and course');
      return;
    }

    markAttendanceMutation.mutate({
      date: selectedDate.toISOString().split('T')[0],
      status,
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Attendance Marking</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex gap-4">
            <div className="flex-1">
              <Calendar
                mode="single"
                selected={selectedDate}
                onSelect={setSelectedDate}
                className="rounded-md border"
              />
            </div>
            <div className="flex-1 space-y-4">
              <Select value={selectedCourse} onValueChange={setSelectedCourse}>
                <SelectTrigger>
                  <SelectValue placeholder="Select course" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="course1">Course 1</SelectItem>
                  <SelectItem value="course2">Course 2</SelectItem>
                  <SelectItem value="course3">Course 3</SelectItem>
                </SelectContent>
              </Select>

              <div className="space-y-2">
                <Button 
                  className="w-full" 
                  onClick={() => handleMarkAttendance('present')}
                  disabled={markAttendanceMutation.isPending}
                >
                  Mark Present
                </Button>
                <Button 
                  className="w-full" 
                  variant="outline" 
                  onClick={() => handleMarkAttendance('absent')}
                  disabled={markAttendanceMutation.isPending}
                >
                  Mark Absent
                </Button>
                <Button 
                  className="w-full" 
                  variant="outline" 
                  onClick={() => handleMarkAttendance('late')}
                  disabled={markAttendanceMutation.isPending}
                >
                  Mark Late
                </Button>
                <Button 
                  className="w-full" 
                  variant="outline" 
                  onClick={() => handleMarkAttendance('excused')}
                  disabled={markAttendanceMutation.isPending}
                >
                  Mark Excused
                </Button>
              </div>
            </div>
          </div>

          <div className="mt-4">
            <h3 className="font-medium mb-2">Recent Attendance</h3>
            {isLoading ? (
              <div className="flex justify-center py-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
              </div>
            ) : attendance.length === 0 ? (
              <p className="text-muted-foreground text-center py-4">No attendance records found</p>
            ) : (
              <div className="space-y-2">
                {attendance.slice(0, 5).map((record: any) => (
                  <div key={record.id} className="flex justify-between items-center p-2 border rounded-md">
                    <div>
                      <p className="font-medium">{new Date(record.date).toLocaleDateString()}</p>
                      <p className="text-sm text-muted-foreground">
                        {record.course?.name || 'No course'}
                      </p>
                    </div>
                    <Badge
                      variant={
                        record.status === 'present' ? 'default' :
                        record.status === 'absent' ? 'destructive' :
                        record.status === 'late' ? 'secondary' : 'outline'
                      }
                    >
                      {record.status.charAt(0).toUpperCase() + record.status.slice(1)}
                    </Badge>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
