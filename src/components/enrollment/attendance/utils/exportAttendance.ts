
import { supabase } from "@/integrations/supabase/client";

export type ExportedAttendanceRecord = {
  date: string;
  student_name: string;
  student_id: string;
  status: string;
  time_in: string;
  time_out: string;
  notes: string;
};

export const exportAttendanceRecords = async (startDate: Date, endDate: Date): Promise<Blob> => {
  const { data, error } = await supabase.rpc('export_attendance_records', {
    start_date: startDate.toISOString().split('T')[0],
    end_date: endDate.toISOString().split('T')[0]
  });

  if (error) throw error;

  const csvContent = [
    ['Date', 'Student Name', 'Student ID', 'Status', 'Time In', 'Time Out', 'Notes'],
    ...(data as ExportedAttendanceRecord[]).map((record: ExportedAttendanceRecord) => [
      record.date,
      record.student_name,
      record.student_id,
      record.status,
      record.time_in ? new Date(record.time_in).toLocaleString() : '',
      record.time_out ? new Date(record.time_out).toLocaleString() : '',
      record.notes || ''
    ])
  ].map(row => row.join(',')).join('\n');

  return new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
};
