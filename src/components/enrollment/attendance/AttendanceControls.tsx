
import { DatePicker } from "@/components/ui/date-picker";
import { But<PERSON> } from "@/components/ui/button";
import { Loader2, Download } from "lucide-react";

interface AttendanceControlsProps {
  selectedDate: Date;
  onDateSelect: (date: Date | undefined) => void;
  onSubmit: () => void;
  onDownload: () => void;
  isSubmitting: boolean;
  isDownloading: boolean;
}

export const AttendanceControls = ({
  selectedDate,
  onDateSelect,
  onSubmit,
  onDownload,
  isSubmitting,
  isDownloading,
}: AttendanceControlsProps) => {
  return (
    <div className="flex items-center gap-4 bg-white p-4 rounded-lg shadow">
      <DatePicker
        date={selectedDate}
        onSelect={onDateSelect}
      />
      <Button 
        onClick={onSubmit}
        disabled={isSubmitting}
        className="min-w-[150px]"
      >
        {isSubmitting ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Recording...
          </>
        ) : (
          "Record Attendance"
        )}
      </Button>
      <Button
        onClick={onDownload}
        disabled={isDownloading}
        variant="outline"
        className="min-w-[150px]"
      >
        {isDownloading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Downloading...
          </>
        ) : (
          <>
            <Download className="mr-2 h-4 w-4" />
            Download Records
          </>
        )}
      </Button>
    </div>
  );
};
