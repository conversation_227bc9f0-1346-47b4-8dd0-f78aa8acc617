import { Student } from "@/types/student";
import { AttendanceRecord } from "@/types/attendance";

interface StudentAttendanceDetailProps {
  student: Student;
  attendanceRecords: AttendanceRecord[];
  onClose: () => void;
}

export const StudentAttendanceDetail = ({
  student: _student,
  attendanceRecords,
  onClose: _onClose,
}: StudentAttendanceDetailProps) => {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Attendance Records</h3>
      {attendanceRecords.length === 0 ? (
        <p className="text-muted-foreground">No attendance records found.</p>
      ) : (
        <div className="space-y-2">
          {attendanceRecords.map((record, index) => (
            <div
              key={record.id || index}
              className="flex items-center justify-between p-4 border rounded-lg"
            >
              <div>
                <p className="font-medium">
                  {new Date(record.date).toLocaleDateString()}
                </p>
                <p className="text-sm text-muted-foreground">
                  Status: {record.status.charAt(0).toUpperCase() + record.status.slice(1)}
                </p>
                {record.time_in && (
                  <p className="text-sm text-muted-foreground">
                    Time In: {new Date(record.time_in).toLocaleTimeString()}
                  </p>
                )}
                {record.time_out && (
                  <p className="text-sm text-muted-foreground">
                    Time Out: {new Date(record.time_out).toLocaleTimeString()}
                  </p>
                )}
                {record.notes && (
                  <p className="text-sm text-muted-foreground mt-1">
                    Notes: {record.notes}
                  </p>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}; 