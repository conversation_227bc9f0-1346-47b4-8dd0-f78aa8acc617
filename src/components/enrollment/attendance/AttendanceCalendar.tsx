
import { Calendar } from "@/components/ui/calendar";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Database } from "@/types/supabase";

type Student = Database['public']['Tables']['students']['Row'];
type AttendanceRecord = Database['public']['Tables']['attendance_records']['Row'] & {
  student: Student;
};

interface AttendanceCalendarProps {
  attendanceRecords: AttendanceRecord[];
  selectedDate: Date;
  onDateSelect: (date: Date) => void;
}

export const AttendanceCalendar = ({
  attendanceRecords,
  selectedDate,
  onDateSelect,
}: AttendanceCalendarProps) => {
  const getDateAttendanceStatus = (date: Date) => {
    const record = attendanceRecords.find(
      (record) =>
        new Date(record.date).toDateString() === date.toDateString()
    );
    return record?.status;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Attendance Calendar</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex justify-center">
          <Calendar
            mode="single"
            selected={selectedDate}
            onSelect={(date) => date && onDateSelect(date)}
            modifiers={{
              present: (date) => getDateAttendanceStatus(date) === "present",
              absent: (date) => getDateAttendanceStatus(date) === "absent"
            }}
            modifiersStyles={{
              present: { backgroundColor: "rgb(187 247 208)" },
              absent: { backgroundColor: "rgb(254 202 202)" }
            }}
            className="rounded-md border"
          />
        </div>
      </CardContent>
    </Card>
  );
};
