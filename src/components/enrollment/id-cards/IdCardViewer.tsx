import { useRef, useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { ArrowLeft, Download, Loader2, Edit, Share2, Palette, ChevronDown } from "lucide-react";
import { toast } from "sonner";
import { getStudent } from "@/api/students";
import html2canvas from "html2canvas";
import jsPDF from "jspdf";
import JsBarcode from "jsbarcode";

export const IdCardViewer = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const cardRef = useRef<HTMLDivElement>(null);
  const [isDownloading, setIsDownloading] = useState(false);

  const { data: student, isLoading } = useQuery({
    queryKey: ['student', id],
    queryFn: () => {
      // If it's demo student, return sample data immediately
      if (id === 'demo-student') {
        return Promise.resolve({
          id: 'demo-student',
          name: 'John Doe',
          student_id: 'STU001',
          course: { name: 'Computer Science' },
          level: { name: 'Year 1' },
          email: '<EMAIL>',
          passport_picture: null
        });
      }
      return getStudent(id!);
    },
    enabled: !!id
  });

  // Default settings - load from localStorage if available
  const [settings, setSettings] = useState({
    schoolName: "BEY CENTER",
    schoolLogo: "/logo.svg",
    schoolAddress: "123 Education Street, Learning City, LC 12345",
    headerColor: "#1e40af",
    bodyColor: "#ffffff",
    validUntil: "Dec 2025",
    authorizedBy: "[Title]",
    contactNumber: "+447360680621",
  });

  const [studentImages, setStudentImages] = useState<Record<string, string>>({});

  // Load settings from localStorage on component mount
  useEffect(() => {
    const savedSettings = localStorage.getItem('idCardSettings');
    if (savedSettings) {
      try {
        const parsedSettings = JSON.parse(savedSettings);
        setSettings(prev => ({ ...prev, ...parsedSettings }));
      } catch (error) {
        console.error('Error loading ID card settings:', error);
      }
    }

    // Load student images
    const savedImages = localStorage.getItem('studentImages');
    if (savedImages) {
      try {
        const parsedImages = JSON.parse(savedImages);
        setStudentImages(parsedImages);
      } catch (error) {
        console.error('Error loading student images:', error);
      }
    }
  }, []);

  const downloadPDF = async () => {
    setIsDownloading(true);
    try {
      // Wait for any pending renders
      await new Promise(resolve => setTimeout(resolve, 500));

      // Find the front and back card elements
      const frontCard = document.querySelector('[data-card-side="front"]') as HTMLElement;
      const backCard = document.querySelector('[data-card-side="back"]') as HTMLElement;
      
      if (!frontCard || !backCard) {
        toast.error('Could not find card elements for PDF generation');
        return;
      }

      // Generate canvas for front side
      const frontCanvas = await html2canvas(frontCard, {
        scale: 3,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        width: 400,
        height: 250,
        scrollX: 0,
        scrollY: 0,
        logging: true
      });
      
      // Generate canvas for back side
      const backCanvas = await html2canvas(backCard, {
        scale: 3,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        width: 400,
        height: 250,
        scrollX: 0,
        scrollY: 0,
        logging: true
      });
      
      console.log('Front canvas:', frontCanvas.width, 'x', frontCanvas.height);
      console.log('Back canvas:', backCanvas.width, 'x', backCanvas.height);
      
      // Create PDF in ID card format (85.6mm x 53.98mm - standard credit card size)
      const pdf = new jsPDF({
        orientation: 'landscape',
        unit: 'mm',
        format: [85.6, 53.98]
      });
      
      // Add front side
      const frontImgData = frontCanvas.toDataURL('image/png', 0.95);
      pdf.addImage(frontImgData, 'PNG', 0, 0, 85.6, 53.98);
      
      // Add new page for back side
      pdf.addPage();
      const backImgData = backCanvas.toDataURL('image/png', 0.95);
      pdf.addImage(backImgData, 'PNG', 0, 0, 85.6, 53.98);
      
      // Save the PDF
      pdf.save(`${safeStudent.student_id}_${safeStudent.name.replace(/\s+/g, '_')}_ID_Card.pdf`);
      
      toast.success("ID card PDF generated successfully with both sides!");
    } catch (error) {
      console.error("Error downloading PDF:", error);
      toast.error(`Failed to download ID card: ${error.message}`);
    } finally {
      setIsDownloading(false);
    }
  };

  const downloadFrontOnly = async () => {
    setIsDownloading(true);
    try {
      const frontCard = document.querySelector('[data-card-side="front"]') as HTMLElement;
      if (!frontCard) {
        toast.error('Could not find front card element');
        return;
      }

      const canvas = await html2canvas(frontCard, {
        scale: 3,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        width: 400,
        height: 250
      });

      const pdf = new jsPDF({
        orientation: 'landscape',
        unit: 'mm',
        format: [85.6, 53.98]
      });
      
      const imgData = canvas.toDataURL('image/png', 0.95);
      pdf.addImage(imgData, 'PNG', 0, 0, 85.6, 53.98);
      pdf.save(`${safeStudent.student_id}_${safeStudent.name.replace(/\s+/g, '_')}_ID_Card_Front.pdf`);
      
      toast.success("Front side downloaded successfully!");
    } catch (error) {
      console.error("Error downloading front:", error);
      toast.error(`Failed to download front side: ${error.message}`);
    } finally {
      setIsDownloading(false);
    }
  };

  const downloadBackOnly = async () => {
    setIsDownloading(true);
    try {
      const backCard = document.querySelector('[data-card-side="back"]') as HTMLElement;
      if (!backCard) {
        toast.error('Could not find back card element');
        return;
      }

      const canvas = await html2canvas(backCard, {
        scale: 3,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        width: 400,
        height: 250
      });

      const pdf = new jsPDF({
        orientation: 'landscape',
        unit: 'mm',
        format: [85.6, 53.98]
      });
      
      const imgData = canvas.toDataURL('image/png', 0.95);
      pdf.addImage(imgData, 'PNG', 0, 0, 85.6, 53.98);
      pdf.save(`${safeStudent.student_id}_${safeStudent.name.replace(/\s+/g, '_')}_ID_Card_Back.pdf`);
      
      toast.success("Back side downloaded successfully!");
    } catch (error) {
      console.error("Error downloading back:", error);
      toast.error(`Failed to download back side: ${error.message}`);
    } finally {
      setIsDownloading(false);
    }
  };

  const downloadImage = async () => {
    if (!cardRef.current) return;

    setIsDownloading(true);
    try {
      const canvas = await html2canvas(cardRef.current, {
        scale: 2, // Reduced from 4 to 2 for stability
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        logging: true // Enable logging for debugging
      });

      const link = document.createElement('a');
      link.download = `${student?.student_id}_${student?.name.replace(/\s+/g, '_')}_ID_Card.png`;
      link.href = canvas.toDataURL('image/png', 0.9);
      link.click();
      
      toast.success("ID card image downloaded successfully!");
    } catch (error) {
      console.error("Error downloading image:", error);
      toast.error(`Failed to download ID card image: ${error.message}`);
    } finally {
      setIsDownloading(false);
    }
  };

  const shareIdCard = async () => {
    if (navigator.share && cardRef.current) {
      try {
        const canvas = await html2canvas(cardRef.current, {
          scale: 2, // Reduced from 3 to 2 for stability
          useCORS: true,
          allowTaint: true,
          backgroundColor: '#ffffff',
          logging: true // Enable logging for debugging
        });

        canvas.toBlob(async (blob) => {
          if (blob) {
            const file = new File([blob], `${student?.student_id}_ID_Card.png`, { type: 'image/png' });
            await navigator.share({
              title: `${student?.name} - Student ID Card`,
              text: `Student ID Card for ${student?.name}`,
              files: [file]
            });
          }
        }, 'image/png', 0.9);
      } catch (error) {
        console.error("Error sharing:", error);
        toast.error(`Failed to share ID card: ${error.message}`);
      }
    } else {
      toast.error("Sharing not supported on this device");
    }
  };

  const generateBarcode = (text: string) => {
    const canvas = document.createElement('canvas');
    try {
      JsBarcode(canvas, text, {
        format: "CODE128",
        width: 1,
        height: 25,
        displayValue: false,
        margin: 0,
        background: "transparent"
      });
      return canvas.toDataURL();
    } catch (error) {
      console.error('Error generating barcode:', error);
      return null;
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[50vh]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!student) {
    return (
      <div className="p-6 text-center">
        <h2 className="text-2xl font-bold text-red-600">Student Not Found</h2>
        <p className="text-gray-600 mt-2">The student you are looking for does not exist or Firebase is not configured.</p>
        <p className="text-gray-500 mt-4 text-sm">Please ensure Firebase environment variables are set up correctly.</p>
        <Button
          variant="outline"
          onClick={() => navigate("/dashboard/enrollment/id-cards")}
          className="mt-4"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to ID Cards
        </Button>
      </div>
    );
  }

  // Create a safe student object with fallbacks
  const safeStudent = {
    id: student?.id || 'unknown',
    name: student?.name || 'Student Name',
    student_id: student?.student_id || 'STU001',
    course: {
      name: student?.course?.name || 'General Course'
    },
    level: {
      name: student?.level?.name || 'Level 1'
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4 mb-4">
        <Button
          variant="outline"
          size="icon"
          onClick={() => navigate("/dashboard/enrollment/id-cards")}
          className="h-8 w-8"
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div className="flex-1">
          <h2 className="text-2xl font-bold">View ID Card</h2>
          <p className="text-muted-foreground">ID card for {safeStudent.name}</p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => navigate(`/dashboard/enrollment/id-cards/generate/${id}`)}
          >
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
          <Button
            variant="outline"
            onClick={() => navigate(`/dashboard/enrollment/id-cards/visual-edit/${id}`)}
            className="text-blue-600 hover:text-blue-700"
          >
            <Palette className="h-4 w-4 mr-2" />
            Visual Editor
          </Button>
          <Button
            variant="outline"
            onClick={shareIdCard}
          >
            <Share2 className="h-4 w-4 mr-2" />
            Share
          </Button>
          <Button
            variant="outline"
            onClick={downloadImage}
            disabled={isDownloading}
          >
            {isDownloading ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Download className="h-4 w-4 mr-2" />
            )}
            PNG
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button disabled={isDownloading}>
                {isDownloading ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Download className="h-4 w-4 mr-2" />
                )}
                PDF
                <ChevronDown className="h-4 w-4 ml-2" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={downloadPDF} disabled={isDownloading}>
                <Download className="h-4 w-4 mr-2" />
                Both Sides
              </DropdownMenuItem>
              <DropdownMenuItem onClick={downloadFrontOnly} disabled={isDownloading}>
                <Download className="h-4 w-4 mr-2" />
                Front Only
              </DropdownMenuItem>
              <DropdownMenuItem onClick={downloadBackOnly} disabled={isDownloading}>
                <Download className="h-4 w-4 mr-2" />
                Back Only
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <div className="flex justify-center">
        <Card className="w-fit">
          <CardContent className="p-6">
            <h3 className="text-lg font-semibold mb-4 text-center">ID Card Preview</h3>
            <div ref={cardRef} className="space-y-8">
              {/* FRONT SIDE */}
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-3 text-center">Front Side</h4>
                <div 
                  data-card-side="front"
                  className="w-[400px] h-[250px] bg-white rounded-lg overflow-hidden shadow-lg relative border"
                  style={{ 
                    fontFamily: 'Arial, sans-serif',
                    backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f3f4f6' fill-opacity='0.2'%3E%3Cpath d='m36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
                  }}
                >
                  {/* Header with geometric design */}
                  <div className="relative h-16 overflow-hidden">
                    {/* Orange angled shape - extended to cover full width */}
                    <div 
                      className="absolute top-0 left-0 w-full h-20 transform -skew-x-12 origin-top-left"
                      style={{ backgroundColor: '#f97316', width: '120%' }}
                    ></div>
                    {/* Blue angled shape - extended to cover full width */}
                    <div 
                      className="absolute top-0 right-0 w-full h-20 transform skew-x-12 origin-top-right"
                      style={{ backgroundColor: settings.headerColor, width: '120%' }}
                    ></div>
                    
                    {/* School logo and name on orange section */}
                    <div className="absolute top-3 left-4 flex items-center gap-2 text-white z-10">
                      {settings.schoolLogo ? (
                        <img 
                          src={settings.schoolLogo} 
                          alt="School Logo" 
                          className="w-6 h-6 object-contain bg-white rounded p-0.5"
                        />
                      ) : (
                        <div className="w-6 h-6 bg-white rounded flex items-center justify-center">
                          <span className="text-orange-500 text-sm">🎓</span>
                        </div>
                      )}
                      <span className="font-bold text-sm">{settings.schoolName}</span>
                    </div>
                  </div>

                  {/* Student ID Card Title */}
                  <div className="px-4 py-1">
                    <h2 
                      className="text-xl font-bold tracking-wide"
                      style={{ color: settings.headerColor }}
                    >
                      STUDENT ID CARD
                    </h2>
                  </div>

                  {/* Main content area */}
                  <div className="px-4 pb-3 flex gap-3 h-[160px]">
                    {/* Student details */}
                    <div className="flex-1 space-y-1.5 text-xs">
                      <div className="flex">
                        <span className="font-semibold text-gray-700 w-16">NAME</span>
                        <span className="text-gray-700">: {safeStudent.name}</span>
                      </div>
                      <div className="flex">
                        <span className="font-semibold text-gray-700 w-16">ID</span>
                        <span className="text-gray-700">: {safeStudent.student_id}</span>
                      </div>
                      <div className="block">
                        <div className="flex items-start">
                          <span className="font-semibold text-gray-700 w-16 flex-shrink-0">COURSE</span>
                          <div className="text-gray-700 text-xs leading-tight -ml-16">
                            <div className="ml-16">
                              <span>: {(() => {
                                const courseName = safeStudent.course?.name || 'Not Assigned';
                                const words = courseName.split(' ');
                                // Only show first 3 words
                                const displayName = words.slice(0, 3).join(' ');
                                return displayName;
                              })()}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="flex">
                        <span className="font-semibold text-gray-700 w-16">LEVEL</span>
                        <span className="text-gray-700">: {safeStudent.level?.name || 'Not Assigned'}</span>
                      </div>
                      <div className="flex">
                        <span className="font-semibold text-gray-700 w-16">D.O.B</span>
                        <span className="text-gray-700">: {student?.date_of_birth || 'Not provided'}</span>
                      </div>
                      <div className="flex">
                        <span className="font-semibold text-gray-700 w-16">ADDRESS</span>
                        <div className="text-gray-700 text-xs leading-relaxed flex-1">
                          <span>: {(() => {
                            const address = settings.schoolAddress || 'School Address Not Set';
                            // Split long addresses into multiple lines for better readability
                            if (address.length > 40) {
                              // Try to break at comma or at reasonable word boundaries
                              const parts = address.split(',').map(part => part.trim());
                              if (parts.length > 1) {
                                return (
                                  <span>
                                    {parts[0]}
                                    <br />
                                    <span className="ml-0">{parts.slice(1).join(', ')}</span>
                                  </span>
                                );
                              }
                              // If no comma, break at word boundary around middle
                              const words = address.split(' ');
                              const mid = Math.ceil(words.length / 2);
                              const firstLine = words.slice(0, mid).join(' ');
                              const secondLine = words.slice(mid).join(' ');
                              return (
                                <span>
                                  {firstLine}
                                  <br />
                                  <span className="ml-0">{secondLine}</span>
                                </span>
                              );
                            }
                            return address;
                          })()}</span>
                        </div>
                      </div>
                    </div>

                    {/* Student photo and barcode section */}
                    <div className="w-20 flex-shrink-0 flex flex-col">
                      {/* Student photo */}
                      <div className="w-20 h-24 mb-2">
                        <div 
                          className="w-full h-full border-3 rounded overflow-hidden bg-gray-100"
                          style={{ borderColor: settings.headerColor }}
                        >
                          {studentImages[safeStudent.id] || student?.passport_picture ? (
                            <img 
                              src={studentImages[safeStudent.id] || student?.passport_picture} 
                              alt={safeStudent.name}
                              className="w-full h-full object-cover"
                              onError={(e) => {
                                console.log('Image failed to load:', studentImages[safeStudent.id] || student?.passport_picture);
                                e.currentTarget.style.display = 'none';
                                e.currentTarget.nextElementSibling.style.display = 'flex';
                              }}
                            />
                          ) : null}
                          <div 
                            className={`w-full h-full bg-gray-200 flex items-center justify-center ${(studentImages[safeStudent.id] || student?.passport_picture) ? 'hidden' : 'flex'}`}
                          >
                            <span className="text-gray-500 text-sm font-medium">
                              {safeStudent.name.substring(0, 2).toUpperCase()}
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* Barcode under photo */}
                      <div className="w-20">
                        {(() => {
                          const barcodeDataUrl = generateBarcode(safeStudent.student_id);
                          return barcodeDataUrl ? (
                            <img 
                              src={barcodeDataUrl} 
                              alt="Barcode"
                              className="w-full h-4"
                              style={{ imageRendering: 'pixelated' }}
                            />
                          ) : (
                            <div className="w-full h-4 bg-gray-200 flex items-center justify-center text-xs text-gray-500">
                              Error
                            </div>
                          );
                        })()}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* BACK SIDE */}
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-3 text-center">Back Side</h4>
                <div 
                  data-card-side="back"
                  className="w-[400px] h-[250px] bg-white rounded-lg overflow-hidden shadow-lg relative border"
                  style={{ 
                    fontFamily: 'Arial, sans-serif',
                    backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f3f4f6' fill-opacity='0.2'%3E%3Cpath d='m36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
                  }}
                >
                  {/* Header with geometric design */}
                  <div className="relative h-12 overflow-hidden">
                    {/* Orange angled shape - extended to cover full width */}
                    <div 
                      className="absolute top-0 left-0 w-full h-16 transform -skew-x-12 origin-top-left"
                      style={{ backgroundColor: '#f97316', width: '120%' }}
                    ></div>
                    {/* Blue angled shape - extended to cover full width */}
                    <div 
                      className="absolute top-0 right-0 w-full h-16 transform skew-x-12 origin-top-right"
                      style={{ backgroundColor: settings.headerColor, width: '120%' }}
                    ></div>
                    
                    {/* School logo and name on orange section */}
                    <div className="absolute top-2 left-4 flex items-center gap-2 text-white z-10">
                      {settings.schoolLogo ? (
                        <img 
                          src={settings.schoolLogo} 
                          alt="School Logo" 
                          className="w-5 h-5 object-contain bg-white rounded p-0.5"
                        />
                      ) : (
                        <div className="w-5 h-5 bg-white rounded flex items-center justify-center">
                          <span className="text-orange-500 text-xs">🎓</span>
                        </div>
                      )}
                      <span className="font-bold text-xs">{settings.schoolName}</span>
                    </div>
                  </div>

                  {/* Back side content */}
                  <div className="px-4 py-2 space-y-2 h-[190px] overflow-hidden">
                    <div className="text-center">
                      <h3 
                        className="text-sm font-bold"
                        style={{ color: settings.headerColor }}
                      >
                        IMPORTANT INFORMATION
                      </h3>
                    </div>

                    <div className="space-y-2 text-xs">
                      <div className="flex justify-between">
                        <span className="font-semibold text-gray-700">Valid Until:</span>
                        <span className="text-gray-700">{settings.validUntil}</span>
                      </div>
                      
                      <div className="flex justify-between">
                        <span className="font-semibold text-gray-700">Authorized By:</span>
                        <span className="text-gray-700">{settings.authorizedBy}</span>
                      </div>
                      
                      <div className="flex justify-between">
                        <span className="font-semibold text-gray-700">Contact:</span>
                        <span className="text-gray-700">{settings.contactNumber}</span>
                      </div>
                    </div>

                    {/* Terms and conditions */}
                    <div className="mt-4 pt-3 border-t border-gray-200">
                      <div className="text-xs text-gray-600 leading-tight">
                        <p className="font-semibold mb-2">Terms & Conditions:</p>
                        <ul className="space-y-1 text-xs">
                          <li>• This card is property of {settings.schoolName}</li>
                          <li>• Must be carried at all times on campus</li>
                          <li>• Report immediately if lost or stolen</li>
                          <li>• Non-transferable and valid for current academic year</li>
                          <li>• Replacement fee applies for lost cards</li>
                          <li>• Card must be returned upon graduation or withdrawal</li>
                        </ul>
                      </div>
                      
                      <div className="mt-4 pt-2 border-t border-gray-200 text-center">
                        <div className="w-32 h-8 border border-gray-400 mx-auto mb-1 flex items-center justify-center">
                          <span className="text-xs text-gray-500">Authorized Signature</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Student Information Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Student Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <span className="font-semibold text-gray-600">Full Name:</span>
              <p>{student.name}</p>
            </div>
            <div>
              <span className="font-semibold text-gray-600">Student ID:</span>
              <p>{student.student_id}</p>
            </div>
            <div>
              <span className="font-semibold text-gray-600">Course:</span>
              <p>{student.course?.name || 'Not Assigned'}</p>
            </div>
            <div>
              <span className="font-semibold text-gray-600">Level:</span>
              <p>{student.level?.name || 'Not Assigned'}</p>
            </div>
            <div>
              <span className="font-semibold text-gray-600">Registration Date:</span>
              <p>{new Date(student.date_of_registration).toLocaleDateString()}</p>
            </div>
            <div>
              <span className="font-semibold text-gray-600">Status:</span>
              <p className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                student.enrollment_status === 'Active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
              }`}>
                {student.enrollment_status}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default IdCardViewer; 