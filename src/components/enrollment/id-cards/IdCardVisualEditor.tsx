import { useState, useRef, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { 
  ArrowLeft, 
  Download, 
  Save, 
  Loader2, 
  Move, 
  Type, 
  Palette, 
  Image as ImageIcon,
  RotateCcw,
  Eye,
  EyeOff,
  Lock,
  Unlock,
  Grid,
  Layers,
  Copy,
  Trash2,
  Plus,
  Settings,
  Upload,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Bold,
  Italic,
  Underline
} from 'lucide-react';
import { toast } from 'sonner';
import { getStudent } from '@/api/students';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import JsBarcode from 'jsbarcode';

interface ElementPosition {
  x: number;
  y: number;
  width?: number;
  height?: number;
  fontSize?: number;
  color?: string;
  visible?: boolean;
  locked?: boolean;
  text?: string;
  fontWeight?: 'normal' | 'bold';
  fontStyle?: 'normal' | 'italic';
  textDecoration?: 'none' | 'underline';
  textAlign?: 'left' | 'center' | 'right';
  backgroundColor?: string;
  borderRadius?: number;
  borderWidth?: number;
  borderColor?: string;
  opacity?: number;
  rotation?: number;
  zIndex?: number;
}

interface IdCardElements {
  studentName: ElementPosition;
  studentId: ElementPosition;
  course: ElementPosition;
  level: ElementPosition;
  photo: ElementPosition;
  schoolName: ElementPosition;
  schoolLogo: ElementPosition;
  validUntil: ElementPosition;
  authorizedBy: ElementPosition;
  contactNumber: ElementPosition;
  barcode: ElementPosition;
}

export const IdCardVisualEditor = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const cardRef = useRef<HTMLDivElement>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [selectedElement, setSelectedElement] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [showGrid, setShowGrid] = useState(true);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [editingMode, setEditingMode] = useState<'design' | 'background' | 'advanced'>('design');
  const [backgroundSettings, setBackgroundSettings] = useState({
    frontBackground: '#ffffff',
    backBackground: '#ffffff',
    frontPattern: 'none',
    backPattern: 'none',
    frontGradient: { start: '#ffffff', end: '#f3f4f6', direction: 'to-br' },
    backGradient: { start: '#ffffff', end: '#f3f4f6', direction: 'to-br' }
  });
  const [cardSettings, setCardSettings] = useState({
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    shadowIntensity: 3
  });

  const { data: student, isLoading } = useQuery({
    queryKey: ['student', id],
    queryFn: () => {
      // If it's demo student, return sample data immediately
      if (id === 'demo-student') {
        return Promise.resolve({
          id: 'demo-student',
          name: 'John Doe',
          student_id: 'STU001',
          course: { name: 'Computer Science' },
          level: { name: 'Year 1' },
          email: '<EMAIL>',
          passport_picture: null
        });
      }
      return getStudent(id!);
    },
    enabled: !!id
  });

  // Default settings
  const [settings, setSettings] = useState({
    schoolName: "BEY CENTER",
    schoolLogo: "/logo.svg",
    schoolAddress: "123 Education Street, Learning City, LC 12345",
    headerColor: "#1e40af",
    bodyColor: "#ffffff",
    validUntil: "Dec 2025",
    authorizedBy: "[Title]",
    contactNumber: "+447360680621",
  });

  // Element positions and properties
  const [elements, setElements] = useState<IdCardElements>({
    studentName: { 
      x: 20, y: 80, fontSize: 14, color: '#000000', visible: true, locked: false, text: '',
      fontWeight: 'bold', textAlign: 'left', opacity: 1, zIndex: 1
    },
    studentId: { 
      x: 20, y: 100, fontSize: 12, color: '#666666', visible: true, locked: false, text: '',
      fontWeight: 'normal', textAlign: 'left', opacity: 1, zIndex: 1
    },
    course: { 
      x: 20, y: 120, fontSize: 11, color: '#666666', visible: true, locked: false, text: '',
      fontWeight: 'normal', textAlign: 'left', opacity: 1, zIndex: 1
    },
    level: { 
      x: 20, y: 140, fontSize: 11, color: '#666666', visible: true, locked: false, text: '',
      fontWeight: 'normal', textAlign: 'left', opacity: 1, zIndex: 1
    },
    photo: { 
      x: 280, y: 80, width: 80, height: 100, visible: true, locked: false,
      borderRadius: 4, borderWidth: 2, borderColor: '#e5e7eb', opacity: 1, zIndex: 2
    },
    schoolName: { 
      x: 20, y: 40, fontSize: 16, color: '#ffffff', visible: true, locked: false, text: '',
      fontWeight: 'bold', textAlign: 'left', opacity: 1, zIndex: 3
    },
    schoolLogo: { 
      x: 20, y: 15, width: 30, height: 30, visible: true, locked: false,
      borderRadius: 4, opacity: 1, zIndex: 3
    },
    validUntil: { 
      x: 20, y: 180, fontSize: 10, color: '#666666', visible: true, locked: false, text: '',
      fontWeight: 'normal', textAlign: 'left', opacity: 1, zIndex: 1
    },
    authorizedBy: { 
      x: 20, y: 200, fontSize: 10, color: '#666666', visible: true, locked: false, text: '',
      fontWeight: 'normal', textAlign: 'left', opacity: 1, zIndex: 1
    },
    contactNumber: { 
      x: 20, y: 220, fontSize: 10, color: '#666666', visible: true, locked: false, text: '',
      fontWeight: 'normal', textAlign: 'left', opacity: 1, zIndex: 1
    },
    barcode: { 
      x: 20, y: 240, width: 120, height: 30, visible: true, locked: false,
      opacity: 1, zIndex: 1
    }
  });

  const [studentImages, setStudentImages] = useState<Record<string, string>>({});

  // Load settings and elements from localStorage
  useEffect(() => {
    const savedSettings = localStorage.getItem('idCardSettings');
    if (savedSettings) {
      try {
        const parsedSettings = JSON.parse(savedSettings);
        setSettings(prev => ({ ...prev, ...parsedSettings }));
      } catch (error) {
        console.error('Error loading ID card settings:', error);
      }
    }

    const savedElements = localStorage.getItem('idCardElements');
    if (savedElements) {
      try {
        const parsedElements = JSON.parse(savedElements);
        setElements(prev => ({ ...prev, ...parsedElements }));
      } catch (error) {
        console.error('Error loading ID card elements:', error);
      }
    }

    const savedBackgroundSettings = localStorage.getItem('idCardBackgroundSettings');
    if (savedBackgroundSettings) {
      try {
        const parsedBackgroundSettings = JSON.parse(savedBackgroundSettings);
        setBackgroundSettings(prev => ({ ...prev, ...parsedBackgroundSettings }));
      } catch (error) {
        console.error('Error loading background settings:', error);
      }
    }

    const savedCardSettings = localStorage.getItem('idCardCardSettings');
    if (savedCardSettings) {
      try {
        const parsedCardSettings = JSON.parse(savedCardSettings);
        setCardSettings(prev => ({ ...prev, ...parsedCardSettings }));
      } catch (error) {
        console.error('Error loading card settings:', error);
      }
    }

    const savedImages = localStorage.getItem('studentImages');
    if (savedImages) {
      try {
        const parsedImages = JSON.parse(savedImages);
        setStudentImages(parsedImages);
      } catch (error) {
        console.error('Error loading student images:', error);
      }
    }
  }, []);

  const handleElementMouseDown = (elementKey: string, e: React.MouseEvent) => {
    if (elements[elementKey as keyof IdCardElements].locked) return;
    
    e.preventDefault();
    e.stopPropagation();
    
    const rect = cardRef.current?.getBoundingClientRect();
    if (!rect) return;

    const element = elements[elementKey as keyof IdCardElements];
    setSelectedElement(elementKey);
    setIsDragging(true);
    setDragOffset({
      x: e.clientX - rect.left - element.x,
      y: e.clientY - rect.top - element.y
    });
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging || !selectedElement || !cardRef.current) return;

    const rect = cardRef.current.getBoundingClientRect();
    const newX = e.clientX - rect.left - dragOffset.x;
    const newY = e.clientY - rect.top - dragOffset.y;

    // Constrain to card boundaries
    const constrainedX = Math.max(0, Math.min(newX, 400 - (elements[selectedElement as keyof IdCardElements].width || 100)));
    const constrainedY = Math.max(0, Math.min(newY, 500 - (elements[selectedElement as keyof IdCardElements].height || 20)));

    setElements(prev => ({
      ...prev,
      [selectedElement]: {
        ...prev[selectedElement as keyof IdCardElements],
        x: constrainedX,
        y: constrainedY
      }
    }));
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const updateElementProperty = (elementKey: string, property: string, value: any) => {
    setElements(prev => ({
      ...prev,
      [elementKey]: {
        ...prev[elementKey as keyof IdCardElements],
        [property]: value
      }
    }));
  };

  const toggleElementVisibility = (elementKey: string) => {
    updateElementProperty(elementKey, 'visible', !elements[elementKey as keyof IdCardElements].visible);
  };

  const toggleElementLock = (elementKey: string) => {
    updateElementProperty(elementKey, 'locked', !elements[elementKey as keyof IdCardElements].locked);
  };

  const duplicateElement = (elementKey: string) => {
    const element = elements[elementKey as keyof IdCardElements];
    const newKey = `${elementKey}_copy_${Date.now()}`;
    // Note: This would require extending the interface to support dynamic keys
    // For now, we'll just copy properties to clipboard
    navigator.clipboard.writeText(JSON.stringify(element));
    toast.success('Element properties copied to clipboard');
  };

  const deleteElement = (elementKey: string) => {
    updateElementProperty(elementKey, 'visible', false);
    setSelectedElement(null);
    toast.success('Element hidden');
  };

  const bringToFront = (elementKey: string) => {
    const maxZ = Math.max(...Object.values(elements).map(el => el.zIndex || 0));
    updateElementProperty(elementKey, 'zIndex', maxZ + 1);
  };

  const sendToBack = (elementKey: string) => {
    const minZ = Math.min(...Object.values(elements).map(el => el.zIndex || 0));
    updateElementProperty(elementKey, 'zIndex', Math.max(0, minZ - 1));
  };

  const alignElements = (alignment: 'left' | 'center' | 'right' | 'top' | 'middle' | 'bottom') => {
    if (!selectedElement) return;
    
    const element = elements[selectedElement as keyof IdCardElements];
    let newX = element.x;
    let newY = element.y;
    
    switch (alignment) {
      case 'left':
        newX = 20;
        break;
      case 'center':
        newX = 200 - (element.width || 50) / 2;
        break;
      case 'right':
        newX = 380 - (element.width || 50);
        break;
      case 'top':
        newY = 20;
        break;
      case 'middle':
        newY = 250 - (element.height || 20) / 2;
        break;
      case 'bottom':
        newY = 480 - (element.height || 20);
        break;
    }
    
    updateElementProperty(selectedElement, 'x', newX);
    updateElementProperty(selectedElement, 'y', newY);
  };

  const resetElementPositions = () => {
    setElements({
      studentName: { x: 20, y: 80, fontSize: 14, color: '#000000', visible: true, locked: false, text: '',
        fontWeight: 'bold', textAlign: 'left', opacity: 1, zIndex: 1
      },
      studentId: { x: 20, y: 100, fontSize: 12, color: '#666666', visible: true, locked: false, text: '',
        fontWeight: 'normal', textAlign: 'left', opacity: 1, zIndex: 1
      },
      course: { x: 20, y: 120, fontSize: 11, color: '#666666', visible: true, locked: false, text: '',
        fontWeight: 'normal', textAlign: 'left', opacity: 1, zIndex: 1
      },
      level: { x: 20, y: 140, fontSize: 11, color: '#666666', visible: true, locked: false, text: '',
        fontWeight: 'normal', textAlign: 'left', opacity: 1, zIndex: 1
      },
      photo: { x: 280, y: 80, width: 80, height: 100, visible: true, locked: false,
        borderRadius: 4, borderWidth: 2, borderColor: '#e5e7eb', opacity: 1, zIndex: 2
      },
      schoolName: { x: 20, y: 40, fontSize: 16, color: '#ffffff', visible: true, locked: false, text: '',
        fontWeight: 'bold', textAlign: 'left', opacity: 1, zIndex: 3
      },
      schoolLogo: { x: 20, y: 15, width: 30, height: 30, visible: true, locked: false,
        borderRadius: 4, opacity: 1, zIndex: 3
      },
      validUntil: { x: 20, y: 180, fontSize: 10, color: '#666666', visible: true, locked: false, text: '',
        fontWeight: 'normal', textAlign: 'left', opacity: 1, zIndex: 1
      },
      authorizedBy: { x: 20, y: 200, fontSize: 10, color: '#666666', visible: true, locked: false, text: '',
        fontWeight: 'normal', textAlign: 'left', opacity: 1, zIndex: 1
      },
      contactNumber: { x: 20, y: 220, fontSize: 10, color: '#666666', visible: true, locked: false, text: '',
        fontWeight: 'normal', textAlign: 'left', opacity: 1, zIndex: 1
      },
      barcode: { x: 20, y: 240, width: 120, height: 30, visible: true, locked: false,
        opacity: 1, zIndex: 1
      }
    });
    toast.success('Element positions reset to default');
  };

  const saveDesign = async () => {
    setIsSaving(true);
    try {
      localStorage.setItem('idCardSettings', JSON.stringify(settings));
      localStorage.setItem('idCardElements', JSON.stringify(elements));
      localStorage.setItem('idCardBackgroundSettings', JSON.stringify(backgroundSettings));
      localStorage.setItem('idCardCardSettings', JSON.stringify(cardSettings));
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success('ID card design saved successfully!');
    } catch (error) {
      toast.error('Failed to save design');
    } finally {
      setIsSaving(false);
    }
  };

  const generatePDF = async () => {
    setIsGenerating(true);
    try {
      // Get quality setting
      const qualityRadio = document.querySelector('input[name="quality"]:checked') as HTMLInputElement;
      const quality = qualityRadio?.value || 'standard';
      
      let scale = 2;
      if (quality === 'high') scale = 3;
      if (quality === 'print') scale = 4;

      // Find the front and back card elements
      const frontCard = document.querySelector('[data-card-side="front"]') as HTMLElement;
      const backCard = document.querySelector('[data-card-side="back"]') as HTMLElement;
      
      if (!frontCard || !backCard) {
        toast.error('Could not find card elements for PDF generation');
        return;
      }

      // Generate canvas for front side
      const frontCanvas = await html2canvas(frontCard, {
        scale: scale,
        useCORS: true,
        allowTaint: true,
        backgroundColor: backgroundSettings.frontBackground,
        width: 400,
        height: 250
      });
      
      // Generate canvas for back side
      const backCanvas = await html2canvas(backCard, {
        scale: scale,
        useCORS: true,
        allowTaint: true,
        backgroundColor: backgroundSettings.backBackground,
        width: 400,
        height: 250
      });
      
      // Create PDF in ID card format (85.6mm x 53.98mm - standard credit card size)
      const pdf = new jsPDF({
        orientation: 'landscape',
        unit: 'mm',
        format: [85.6, 53.98]
      });
      
      // Add front side
      const frontImgData = frontCanvas.toDataURL('image/png', 0.95);
      pdf.addImage(frontImgData, 'PNG', 0, 0, 85.6, 53.98);
      
      // Add new page for back side
      pdf.addPage();
      const backImgData = backCanvas.toDataURL('image/png', 0.95);
      pdf.addImage(backImgData, 'PNG', 0, 0, 85.6, 53.98);
      
      // Save the PDF
      pdf.save(`${student?.student_id}_${student?.name.replace(/\s+/g, '_')}_ID_Card.pdf`);
      
      toast.success('ID card PDF generated successfully with both sides!');
    } catch (error) {
      console.error('Error generating PDF:', error);
      toast.error('Failed to generate ID card PDF');
    } finally {
      setIsGenerating(false);
    }
  };

  const generateBarcode = (text: string) => {
    const canvas = document.createElement('canvas');
    try {
      JsBarcode(canvas, text, {
        format: "CODE128",
        width: 1,
        height: 25,
        displayValue: false,
        margin: 0,
        background: "transparent"
      });
      return canvas.toDataURL();
    } catch (error) {
      console.error('Error generating barcode:', error);
      return null;
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[50vh]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!student) {
    return (
      <div className="p-6 text-center">
        <h2 className="text-2xl font-bold text-red-600">Student Not Found</h2>
        <p className="text-gray-600 mt-2">The student you are looking for does not exist or Firebase is not configured.</p>
        <p className="text-gray-500 mt-4 text-sm">Please ensure Firebase environment variables are set up correctly.</p>
        <div className="mt-6 space-y-2">
          <Button
            onClick={() => navigate("/dashboard/enrollment/id-cards/visual-edit/demo-student")}
            className="mr-2"
          >
            Try Demo Student
          </Button>
          <Button
            variant="outline"
            onClick={() => navigate("/dashboard/enrollment/id-cards")}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to ID Cards
          </Button>
        </div>
      </div>
    );
  }

  // Create a safe student object with fallbacks
  const safeStudent = {
    id: student?.id || 'unknown',
    name: student?.name || 'Student Name',
    student_id: student?.student_id || 'STU001',
    course: {
      name: student?.course?.name || 'General Course'
    },
    level: {
      name: student?.level?.name || 'Level 1'
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4 mb-4">
        <Button
          variant="outline"
          size="icon"
          onClick={() => navigate("/dashboard/enrollment/id-cards")}
          className="h-8 w-8"
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div className="flex-1">
          <h2 className="text-2xl font-bold">Visual ID Card Editor</h2>
          <p className="text-muted-foreground">Design ID card for {safeStudent.name}</p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setShowGrid(!showGrid)}
          >
            <Grid className="h-4 w-4 mr-2" />
            {showGrid ? 'Hide' : 'Show'} Grid
          </Button>
          <Button
            variant="outline"
            onClick={resetElementPositions}
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            Reset Layout
          </Button>
          <Button
            variant="outline"
            onClick={saveDesign}
            disabled={isSaving}
          >
            {isSaving ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            Save Design
          </Button>
          <Button
            onClick={generatePDF}
            disabled={isGenerating}
          >
            {isGenerating ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Download className="h-4 w-4 mr-2" />
            )}
            Generate PDF
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Design Controls */}
        <div className="lg:col-span-1 space-y-4">
          <Tabs value={editingMode} onValueChange={(value) => setEditingMode(value as any)}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="design">Design</TabsTrigger>
              <TabsTrigger value="background">Style</TabsTrigger>
              <TabsTrigger value="advanced">Advanced</TabsTrigger>
            </TabsList>

            <TabsContent value="design" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Layers className="h-5 w-5" />
                    Elements
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {Object.entries(elements).map(([key, element]) => (
                    <div 
                      key={key}
                      className={`flex items-center justify-between p-2 rounded border cursor-pointer transition-colors ${
                        selectedElement === key ? 'bg-blue-50 border-blue-300' : 'hover:bg-gray-50'
                      }`}
                      onClick={() => setSelectedElement(key)}
                    >
                      <span className="text-sm font-medium capitalize">
                        {key.replace(/([A-Z])/g, ' $1').trim()}
                      </span>
                      <div className="flex gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0"
                          onClick={(e) => {
                            e.stopPropagation();
                            toggleElementVisibility(key);
                          }}
                        >
                          {element.visible ? (
                            <Eye className="h-3 w-3" />
                          ) : (
                            <EyeOff className="h-3 w-3" />
                          )}
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0"
                          onClick={(e) => {
                            e.stopPropagation();
                            toggleElementLock(key);
                          }}
                        >
                          {element.locked ? (
                            <Lock className="h-3 w-3" />
                          ) : (
                            <Unlock className="h-3 w-3" />
                          )}
                        </Button>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>

              {/* Element Properties */}
              {selectedElement && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Type className="h-5 w-5" />
                      Properties
                    </CardTitle>
                    <div className="flex gap-1">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => duplicateElement(selectedElement)}
                      >
                        <Copy className="h-3 w-3 mr-1" />
                        Copy
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => deleteElement(selectedElement)}
                      >
                        <EyeOff className="h-3 w-3 mr-1" />
                        Hide
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Text editing for text elements */}
                    {elements[selectedElement as keyof IdCardElements].text !== undefined && (
                      <div className="space-y-2">
                        <Label>Custom Text</Label>
                        <Textarea
                          value={elements[selectedElement as keyof IdCardElements].text || ''}
                          onChange={(e) => updateElementProperty(selectedElement, 'text', e.target.value)}
                          placeholder="Enter custom text (leave empty for default)"
                          rows={2}
                        />
                      </div>
                    )}

                    {/* Position Controls */}
                    <div className="grid grid-cols-2 gap-2">
                      <div className="space-y-2">
                        <Label>Position X</Label>
                        <Input
                          type="number"
                          value={elements[selectedElement as keyof IdCardElements].x}
                          onChange={(e) => updateElementProperty(selectedElement, 'x', parseInt(e.target.value))}
                          min="0"
                          max="400"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Position Y</Label>
                        <Input
                          type="number"
                          value={elements[selectedElement as keyof IdCardElements].y}
                          onChange={(e) => updateElementProperty(selectedElement, 'y', parseInt(e.target.value))}
                          min="0"
                          max="500"
                        />
                      </div>
                    </div>

                    {/* Alignment Controls */}
                    <div className="space-y-2">
                      <Label>Quick Align</Label>
                      <div className="grid grid-cols-3 gap-1">
                        <Button variant="outline" size="sm" onClick={() => alignElements('left')}>
                          <AlignLeft className="h-3 w-3" />
                        </Button>
                        <Button variant="outline" size="sm" onClick={() => alignElements('center')}>
                          <AlignCenter className="h-3 w-3" />
                        </Button>
                        <Button variant="outline" size="sm" onClick={() => alignElements('right')}>
                          <AlignRight className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>

                    {/* Font Controls for text elements */}
                    {elements[selectedElement as keyof IdCardElements].fontSize && (
                      <>
                        <div className="space-y-2">
                          <Label>Font Size</Label>
                          <Slider
                            value={[elements[selectedElement as keyof IdCardElements].fontSize || 12]}
                            onValueChange={(value) => updateElementProperty(selectedElement, 'fontSize', value[0])}
                            min={8}
                            max={32}
                            step={1}
                          />
                          <span className="text-sm text-gray-500">
                            {elements[selectedElement as keyof IdCardElements].fontSize}px
                          </span>
                        </div>

                        <div className="space-y-2">
                          <Label>Text Style</Label>
                          <div className="flex gap-1">
                            <Button
                              variant={elements[selectedElement as keyof IdCardElements].fontWeight === 'bold' ? 'default' : 'outline'}
                              size="sm"
                              onClick={() => updateElementProperty(selectedElement, 'fontWeight', 
                                elements[selectedElement as keyof IdCardElements].fontWeight === 'bold' ? 'normal' : 'bold')}
                            >
                              <Bold className="h-3 w-3" />
                            </Button>
                            <Button
                              variant={elements[selectedElement as keyof IdCardElements].fontStyle === 'italic' ? 'default' : 'outline'}
                              size="sm"
                              onClick={() => updateElementProperty(selectedElement, 'fontStyle', 
                                elements[selectedElement as keyof IdCardElements].fontStyle === 'italic' ? 'normal' : 'italic')}
                            >
                              <Italic className="h-3 w-3" />
                            </Button>
                            <Button
                              variant={elements[selectedElement as keyof IdCardElements].textDecoration === 'underline' ? 'default' : 'outline'}
                              size="sm"
                              onClick={() => updateElementProperty(selectedElement, 'textDecoration', 
                                elements[selectedElement as keyof IdCardElements].textDecoration === 'underline' ? 'none' : 'underline')}
                            >
                              <Underline className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label>Text Align</Label>
                          <div className="flex gap-1">
                            <Button
                              variant={elements[selectedElement as keyof IdCardElements].textAlign === 'left' ? 'default' : 'outline'}
                              size="sm"
                              onClick={() => updateElementProperty(selectedElement, 'textAlign', 'left')}
                            >
                              <AlignLeft className="h-3 w-3" />
                            </Button>
                            <Button
                              variant={elements[selectedElement as keyof IdCardElements].textAlign === 'center' ? 'default' : 'outline'}
                              size="sm"
                              onClick={() => updateElementProperty(selectedElement, 'textAlign', 'center')}
                            >
                              <AlignCenter className="h-3 w-3" />
                            </Button>
                            <Button
                              variant={elements[selectedElement as keyof IdCardElements].textAlign === 'right' ? 'default' : 'outline'}
                              size="sm"
                              onClick={() => updateElementProperty(selectedElement, 'textAlign', 'right')}
                            >
                              <AlignRight className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </>
                    )}

                    {/* Color Controls */}
                    {elements[selectedElement as keyof IdCardElements].color && (
                      <div className="space-y-2">
                        <Label>Text Color</Label>
                        <div className="flex gap-2">
                          <Input
                            type="color"
                            value={elements[selectedElement as keyof IdCardElements].color}
                            onChange={(e) => updateElementProperty(selectedElement, 'color', e.target.value)}
                            className="w-16 h-10"
                          />
                          <Input
                            value={elements[selectedElement as keyof IdCardElements].color}
                            onChange={(e) => updateElementProperty(selectedElement, 'color', e.target.value)}
                            placeholder="#000000"
                          />
                        </div>
                      </div>
                    )}

                    {/* Size Controls for images/elements with dimensions */}
                    {(elements[selectedElement as keyof IdCardElements].width !== undefined) && (
                      <>
                        <div className="grid grid-cols-2 gap-2">
                          <div className="space-y-2">
                            <Label>Width</Label>
                            <Input
                              type="number"
                              value={elements[selectedElement as keyof IdCardElements].width}
                              onChange={(e) => updateElementProperty(selectedElement, 'width', parseInt(e.target.value))}
                              min="10"
                              max="300"
                            />
                          </div>
                          <div className="space-y-2">
                            <Label>Height</Label>
                            <Input
                              type="number"
                              value={elements[selectedElement as keyof IdCardElements].height}
                              onChange={(e) => updateElementProperty(selectedElement, 'height', parseInt(e.target.value))}
                              min="10"
                              max="200"
                            />
                          </div>
                        </div>
                      </>
                    )}

                    {/* Opacity Control */}
                    <div className="space-y-2">
                      <Label>Opacity</Label>
                      <Slider
                        value={[elements[selectedElement as keyof IdCardElements].opacity || 1]}
                        onValueChange={(value) => updateElementProperty(selectedElement, 'opacity', value[0])}
                        min={0}
                        max={1}
                        step={0.1}
                      />
                      <span className="text-sm text-gray-500">
                        {Math.round((elements[selectedElement as keyof IdCardElements].opacity || 1) * 100)}%
                      </span>
                    </div>

                    {/* Layer Controls */}
                    <div className="space-y-2">
                      <Label>Layer Order</Label>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm" onClick={() => bringToFront(selectedElement)}>
                          Bring Forward
                        </Button>
                        <Button variant="outline" size="sm" onClick={() => sendToBack(selectedElement)}>
                          Send Back
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="background" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Palette className="h-5 w-5" />
                    Card Style
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label>Card Border Radius</Label>
                    <Slider
                      value={[cardSettings.borderRadius]}
                      onValueChange={(value) => setCardSettings(prev => ({ ...prev, borderRadius: value[0] }))}
                      min={0}
                      max={20}
                      step={1}
                    />
                    <span className="text-sm text-gray-500">{cardSettings.borderRadius}px</span>
                  </div>

                  <div className="space-y-2">
                    <Label>Border Width</Label>
                    <Slider
                      value={[cardSettings.borderWidth]}
                      onValueChange={(value) => setCardSettings(prev => ({ ...prev, borderWidth: value[0] }))}
                      min={0}
                      max={5}
                      step={1}
                    />
                    <span className="text-sm text-gray-500">{cardSettings.borderWidth}px</span>
                  </div>

                  <div className="space-y-2">
                    <Label>Border Color</Label>
                    <div className="flex gap-2">
                      <Input
                        type="color"
                        value={cardSettings.borderColor}
                        onChange={(e) => setCardSettings(prev => ({ ...prev, borderColor: e.target.value }))}
                        className="w-16 h-10"
                      />
                      <Input
                        value={cardSettings.borderColor}
                        onChange={(e) => setCardSettings(prev => ({ ...prev, borderColor: e.target.value }))}
                        placeholder="#e5e7eb"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Shadow Intensity</Label>
                    <Slider
                      value={[cardSettings.shadowIntensity]}
                      onValueChange={(value) => setCardSettings(prev => ({ ...prev, shadowIntensity: value[0] }))}
                      min={0}
                      max={10}
                      step={1}
                    />
                    <span className="text-sm text-gray-500">Level {cardSettings.shadowIntensity}</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Background Settings</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label>Front Background</Label>
                    <div className="flex gap-2">
                      <Input
                        type="color"
                        value={backgroundSettings.frontBackground}
                        onChange={(e) => setBackgroundSettings(prev => ({ ...prev, frontBackground: e.target.value }))}
                        className="w-16 h-10"
                      />
                      <Input
                        value={backgroundSettings.frontBackground}
                        onChange={(e) => setBackgroundSettings(prev => ({ ...prev, frontBackground: e.target.value }))}
                        placeholder="#ffffff"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Back Background</Label>
                    <div className="flex gap-2">
                      <Input
                        type="color"
                        value={backgroundSettings.backBackground}
                        onChange={(e) => setBackgroundSettings(prev => ({ ...prev, backBackground: e.target.value }))}
                        className="w-16 h-10"
                      />
                      <Input
                        value={backgroundSettings.backBackground}
                        onChange={(e) => setBackgroundSettings(prev => ({ ...prev, backBackground: e.target.value }))}
                        placeholder="#ffffff"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Header Color</Label>
                    <div className="flex gap-2">
                      <Input
                        type="color"
                        value={settings.headerColor}
                        onChange={(e) => setSettings(prev => ({ ...prev, headerColor: e.target.value }))}
                        className="w-16 h-10"
                      />
                      <Input
                        value={settings.headerColor}
                        onChange={(e) => setSettings(prev => ({ ...prev, headerColor: e.target.value }))}
                        placeholder="#1e40af"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>School Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label>School Name</Label>
                    <Input
                      value={settings.schoolName}
                      onChange={(e) => setSettings(prev => ({ ...prev, schoolName: e.target.value }))}
                      placeholder="School Name"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Contact Number</Label>
                    <Input
                      value={settings.contactNumber}
                      onChange={(e) => setSettings(prev => ({ ...prev, contactNumber: e.target.value }))}
                      placeholder="+1234567890"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Valid Until</Label>
                    <Input
                      value={settings.validUntil}
                      onChange={(e) => setSettings(prev => ({ ...prev, validUntil: e.target.value }))}
                      placeholder="Dec 2025"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Authorized By</Label>
                    <Input
                      value={settings.authorizedBy}
                      onChange={(e) => setSettings(prev => ({ ...prev, authorizedBy: e.target.value }))}
                      placeholder="Principal/Director"
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="advanced" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="h-5 w-5" />
                    Advanced Tools
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label>Design Templates</Label>
                    <div className="grid grid-cols-2 gap-2">
                      <Button
                        variant="outline"
                        onClick={() => {
                          // Modern template
                          setElements({
                            ...elements,
                            studentName: { ...elements.studentName, x: 30, y: 90, fontSize: 16, fontWeight: 'bold', color: '#1e40af' },
                            studentId: { ...elements.studentId, x: 30, y: 115, fontSize: 12, color: '#64748b' },
                            course: { ...elements.course, x: 30, y: 135, fontSize: 11, color: '#64748b' },
                            level: { ...elements.level, x: 30, y: 155, fontSize: 11, color: '#64748b' },
                            photo: { ...elements.photo, x: 290, y: 85, width: 90, height: 110, borderRadius: 8 },
                            schoolName: { ...elements.schoolName, x: 60, y: 45, fontSize: 18, fontWeight: 'bold' },
                            schoolLogo: { ...elements.schoolLogo, x: 25, y: 20, width: 35, height: 35 }
                          });
                          setSettings(prev => ({ ...prev, headerColor: '#1e40af' }));
                          setBackgroundSettings(prev => ({ ...prev, frontBackground: '#f8fafc' }));
                          toast.success('Modern template applied');
                        }}
                      >
                        Modern
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => {
                          // Classic template
                          setElements({
                            ...elements,
                            studentName: { ...elements.studentName, x: 20, y: 80, fontSize: 14, fontWeight: 'bold', color: '#000000' },
                            studentId: { ...elements.studentId, x: 20, y: 100, fontSize: 12, color: '#666666' },
                            course: { ...elements.course, x: 20, y: 120, fontSize: 11, color: '#666666' },
                            level: { ...elements.level, x: 20, y: 140, fontSize: 11, color: '#666666' },
                            photo: { ...elements.photo, x: 280, y: 80, width: 80, height: 100, borderRadius: 4 },
                            schoolName: { ...elements.schoolName, x: 20, y: 40, fontSize: 16, fontWeight: 'bold' },
                            schoolLogo: { ...elements.schoolLogo, x: 20, y: 15, width: 30, height: 30 }
                          });
                          setSettings(prev => ({ ...prev, headerColor: '#dc2626' }));
                          setBackgroundSettings(prev => ({ ...prev, frontBackground: '#ffffff' }));
                          toast.success('Classic template applied');
                        }}
                      >
                        Classic
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => {
                          // Minimal template
                          setElements({
                            ...elements,
                            studentName: { ...elements.studentName, x: 40, y: 100, fontSize: 18, fontWeight: 'normal', color: '#374151' },
                            studentId: { ...elements.studentId, x: 40, y: 130, fontSize: 14, color: '#6b7280' },
                            course: { ...elements.course, x: 40, y: 155, fontSize: 12, color: '#9ca3af' },
                            level: { ...elements.level, x: 40, y: 175, fontSize: 12, color: '#9ca3af' },
                            photo: { ...elements.photo, x: 300, y: 90, width: 70, height: 90, borderRadius: 35 },
                            schoolName: { ...elements.schoolName, x: 40, y: 50, fontSize: 14, fontWeight: 'normal' },
                            schoolLogo: { ...elements.schoolLogo, x: 40, y: 20, width: 25, height: 25 }
                          });
                          setSettings(prev => ({ ...prev, headerColor: '#6b7280' }));
                          setBackgroundSettings(prev => ({ ...prev, frontBackground: '#fafafa' }));
                          toast.success('Minimal template applied');
                        }}
                      >
                        Minimal
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => {
                          // Corporate template
                          setElements({
                            ...elements,
                            studentName: { ...elements.studentName, x: 25, y: 85, fontSize: 15, fontWeight: 'bold', color: '#1f2937' },
                            studentId: { ...elements.studentId, x: 25, y: 110, fontSize: 13, color: '#4b5563' },
                            course: { ...elements.course, x: 25, y: 130, fontSize: 11, color: '#6b7280' },
                            level: { ...elements.level, x: 25, y: 150, fontSize: 11, color: '#6b7280' },
                            photo: { ...elements.photo, x: 285, y: 80, width: 85, height: 105, borderRadius: 6 },
                            schoolName: { ...elements.schoolName, x: 25, y: 42, fontSize: 17, fontWeight: 'bold' },
                            schoolLogo: { ...elements.schoolLogo, x: 25, y: 18, width: 32, height: 32 }
                          });
                          setSettings(prev => ({ ...prev, headerColor: '#1f2937' }));
                          setBackgroundSettings(prev => ({ ...prev, frontBackground: '#ffffff' }));
                          toast.success('Corporate template applied');
                        }}
                      >
                        Corporate
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>PDF Export Quality</Label>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="quality-standard"
                          name="quality"
                          value="standard"
                          defaultChecked
                          className="w-4 h-4"
                        />
                        <Label htmlFor="quality-standard">Standard (Fast)</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="quality-high"
                          name="quality"
                          value="high"
                          className="w-4 h-4"
                        />
                        <Label htmlFor="quality-high">High Quality (Slower)</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="quality-print"
                          name="quality"
                          value="print"
                          className="w-4 h-4"
                        />
                        <Label htmlFor="quality-print">Print Ready (Slowest)</Label>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Batch Operations</Label>
                    <div className="flex flex-col gap-2">
                      <Button
                        variant="outline"
                        onClick={() => {
                          // Apply current design to all students
                          const designData = {
                            elements,
                            settings,
                            backgroundSettings,
                            cardSettings
                          };
                          localStorage.setItem('batchIdCardDesign', JSON.stringify(designData));
                          toast.success('Design saved for batch application');
                        }}
                      >
                        <Copy className="h-4 w-4 mr-2" />
                        Save as Batch Template
                      </Button>
                      
                      <Button
                        variant="outline"
                        onClick={() => {
                          Object.keys(elements).forEach(key => {
                            updateElementProperty(key, 'fontSize', 
                              Math.min((elements[key as keyof IdCardElements].fontSize || 12) + 1, 24));
                          });
                          toast.success('Font sizes increased');
                        }}
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Increase All Font Sizes
                      </Button>
                      
                      <Button
                        variant="outline"
                        onClick={() => {
                          Object.keys(elements).forEach(key => {
                            updateElementProperty(key, 'fontSize', 
                              Math.max((elements[key as keyof IdCardElements].fontSize || 12) - 1, 8));
                          });
                          toast.success('Font sizes decreased');
                        }}
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Decrease All Font Sizes
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Element Duplication</Label>
                    <div className="flex flex-col gap-2">
                      <Button
                        variant="outline"
                        onClick={() => {
                          if (!selectedElement) {
                            toast.error('Please select an element first');
                            return;
                          }
                          const element = elements[selectedElement as keyof IdCardElements];
                          const newElement = {
                            ...element,
                            x: element.x + 20,
                            y: element.y + 20
                          };
                          // For demonstration, we'll copy to clipboard
                          navigator.clipboard.writeText(JSON.stringify(newElement));
                          toast.success('Element duplicated (copied to clipboard)');
                        }}
                        disabled={!selectedElement}
                      >
                        <Copy className="h-4 w-4 mr-2" />
                        Duplicate Selected Element
                      </Button>
                      
                      <Button
                        variant="outline"
                        onClick={() => {
                          // Mirror elements horizontally
                          Object.keys(elements).forEach(key => {
                            const element = elements[key as keyof IdCardElements];
                            const newX = 400 - element.x - (element.width || 50);
                            updateElementProperty(key, 'x', Math.max(0, newX));
                          });
                          toast.success('Elements mirrored horizontally');
                        }}
                      >
                        <Move className="h-4 w-4 mr-2" />
                        Mirror Elements Horizontally
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Color Schemes</Label>
                    <div className="grid grid-cols-2 gap-2">
                      <Button
                        variant="outline"
                        onClick={() => {
                          setSettings(prev => ({ ...prev, headerColor: '#3b82f6' }));
                          Object.keys(elements).forEach(key => {
                            if (elements[key as keyof IdCardElements].color) {
                              updateElementProperty(key, 'color', '#1e40af');
                            }
                          });
                          toast.success('Blue theme applied');
                        }}
                      >
                        Blue Theme
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => {
                          setSettings(prev => ({ ...prev, headerColor: '#dc2626' }));
                          Object.keys(elements).forEach(key => {
                            if (elements[key as keyof IdCardElements].color) {
                              updateElementProperty(key, 'color', '#991b1b');
                            }
                          });
                          toast.success('Red theme applied');
                        }}
                      >
                        Red Theme
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => {
                          setSettings(prev => ({ ...prev, headerColor: '#059669' }));
                          Object.keys(elements).forEach(key => {
                            if (elements[key as keyof IdCardElements].color) {
                              updateElementProperty(key, 'color', '#047857');
                            }
                          });
                          toast.success('Green theme applied');
                        }}
                      >
                        Green Theme
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => {
                          setSettings(prev => ({ ...prev, headerColor: '#7c3aed' }));
                          Object.keys(elements).forEach(key => {
                            if (elements[key as keyof IdCardElements].color) {
                              updateElementProperty(key, 'color', '#5b21b6');
                            }
                          });
                          toast.success('Purple theme applied');
                        }}
                      >
                        Purple Theme
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Accessibility</Label>
                    <div className="flex flex-col gap-2">
                      <Button
                        variant="outline"
                        onClick={() => {
                          // Increase contrast for better readability
                          Object.keys(elements).forEach(key => {
                            const element = elements[key as keyof IdCardElements];
                            if (element.color && element.color !== '#ffffff') {
                              updateElementProperty(key, 'color', '#000000');
                            }
                          });
                          setBackgroundSettings(prev => ({ 
                            ...prev, 
                            frontBackground: '#ffffff',
                            backBackground: '#ffffff'
                          }));
                          toast.success('High contrast mode applied');
                        }}
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        High Contrast Mode
                      </Button>
                      
                      <Button
                        variant="outline"
                        onClick={() => {
                          // Increase font sizes for better readability
                          Object.keys(elements).forEach(key => {
                            const element = elements[key as keyof IdCardElements];
                            if (element.fontSize) {
                              updateElementProperty(key, 'fontSize', Math.min(element.fontSize + 2, 24));
                            }
                          });
                          toast.success('Large text mode applied');
                        }}
                      >
                        <Type className="h-4 w-4 mr-2" />
                        Large Text Mode
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Import/Export</Label>
                    <div className="flex flex-col gap-2">
                      <Button
                        variant="outline"
                        onClick={() => {
                          const data = {
                            elements,
                            settings,
                            backgroundSettings,
                            cardSettings
                          };
                          const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                          const url = URL.createObjectURL(blob);
                          const a = document.createElement('a');
                          a.href = url;
                          a.download = `id-card-design-${Date.now()}.json`;
                          a.click();
                          URL.revokeObjectURL(url);
                          toast.success('Design exported successfully!');
                        }}
                      >
                        <Download className="h-4 w-4 mr-2" />
                        Export Design
                      </Button>
                      
                      <Button
                        variant="outline"
                        onClick={() => {
                          const input = document.createElement('input');
                          input.type = 'file';
                          input.accept = '.json';
                          input.onchange = (e) => {
                            const file = (e.target as HTMLInputElement).files?.[0];
                            if (file) {
                              const reader = new FileReader();
                              reader.onload = (e) => {
                                try {
                                  const data = JSON.parse(e.target?.result as string);
                                  if (data.elements) setElements(data.elements);
                                  if (data.settings) setSettings(data.settings);
                                  if (data.backgroundSettings) setBackgroundSettings(data.backgroundSettings);
                                  if (data.cardSettings) setCardSettings(data.cardSettings);
                                  toast.success('Design imported successfully!');
                                } catch (error) {
                                  toast.error('Invalid design file');
                                }
                              };
                              reader.readAsText(file);
                            }
                          };
                          input.click();
                        }}
                      >
                        <Upload className="h-4 w-4 mr-2" />
                        Import Design
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Quick Actions</Label>
                    <div className="flex flex-col gap-2">
                      <Button
                        variant="outline"
                        onClick={() => {
                          Object.keys(elements).forEach(key => {
                            updateElementProperty(key, 'visible', true);
                          });
                          toast.success('All elements shown');
                        }}
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        Show All Elements
                      </Button>
                      
                      <Button
                        variant="outline"
                        onClick={() => {
                          Object.keys(elements).forEach(key => {
                            updateElementProperty(key, 'locked', false);
                          });
                          toast.success('All elements unlocked');
                        }}
                      >
                        <Unlock className="h-4 w-4 mr-2" />
                        Unlock All Elements
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Grid Settings</Label>
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={showGrid}
                        onCheckedChange={setShowGrid}
                      />
                      <Label>Show Grid</Label>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Reset Options</Label>
                    <div className="flex flex-col gap-2">
                      <Button
                        variant="outline"
                        onClick={resetElementPositions}
                      >
                        <RotateCcw className="h-4 w-4 mr-2" />
                        Reset Layout
                      </Button>
                      
                      <Button
                        variant="outline"
                        onClick={() => {
                          setBackgroundSettings({
                            frontBackground: '#ffffff',
                            backBackground: '#ffffff',
                            frontPattern: 'none',
                            backPattern: 'none',
                            frontGradient: { start: '#ffffff', end: '#f3f4f6', direction: 'to-br' },
                            backGradient: { start: '#ffffff', end: '#f3f4f6', direction: 'to-br' }
                          });
                          setCardSettings({
                            borderRadius: 8,
                            borderWidth: 1,
                            borderColor: '#e5e7eb',
                            shadowIntensity: 3
                          });
                          toast.success('Styles reset to default');
                        }}
                      >
                        <Palette className="h-4 w-4 mr-2" />
                        Reset Styles
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* ID Card Preview */}
        <div className="lg:col-span-3">
          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold mb-4">ID Card Preview</h3>
              
              <div className="flex justify-center">
                <div className="space-y-6">
                  {/* FRONT SIDE */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 mb-2 text-center">Front Side</h4>
                    <div 
                      ref={cardRef}
                      data-card-side="front"
                      className="relative w-[400px] h-[250px] bg-white rounded-lg overflow-hidden shadow-lg border cursor-crosshair"
                      style={{ 
                        fontFamily: 'Arial, sans-serif',
                        backgroundColor: backgroundSettings.frontBackground,
                        borderRadius: `${cardSettings.borderRadius}px`,
                        borderWidth: `${cardSettings.borderWidth}px`,
                        borderColor: cardSettings.borderColor,
                        boxShadow: `0 ${cardSettings.shadowIntensity}px ${cardSettings.shadowIntensity * 2}px rgba(0,0,0,0.1)`,
                        backgroundImage: showGrid ? `url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23e5e7eb' fill-opacity='0.3'%3E%3Cpath d='M0 0h1v1H0V0zm10 0h1v1h-1V0z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")` : 'none'
                      }}
                      onMouseMove={handleMouseMove}
                      onMouseUp={handleMouseUp}
                      onMouseLeave={handleMouseUp}
                    >
                      {/* Header with geometric design */}
                      <div className="relative h-12 overflow-hidden">
                        <div 
                          className="absolute top-0 left-0 w-[500px] h-16 transform -skew-x-12 -translate-x-20"
                          style={{ backgroundColor: '#f97316' }}
                        ></div>
                        <div 
                          className="absolute top-0 right-0 w-[500px] h-16 transform skew-x-12 translate-x-20"
                          style={{ backgroundColor: settings.headerColor }}
                        ></div>
                      </div>

                      {/* Draggable Elements - sorted by zIndex */}
                      {Object.entries(elements)
                        .sort(([,a], [,b]) => (a.zIndex || 0) - (b.zIndex || 0))
                        .map(([key, element]) => {
                          if (!element.visible || element.y >= 250) return null;

                          const commonStyle = {
                            left: element.x,
                            top: element.y,
                            opacity: element.opacity || 1,
                            zIndex: element.zIndex || 1,
                            transform: element.rotation ? `rotate(${element.rotation}deg)` : undefined
                          };

                          const textStyle = {
                            fontSize: element.fontSize,
                            color: element.color,
                            fontWeight: element.fontWeight || 'normal',
                            fontStyle: element.fontStyle || 'normal',
                            textDecoration: element.textDecoration || 'none',
                            textAlign: element.textAlign || 'left',
                            backgroundColor: element.backgroundColor || 'transparent',
                            borderRadius: element.borderRadius ? `${element.borderRadius}px` : undefined,
                            padding: element.backgroundColor ? '2px 4px' : undefined
                          };

                          const imageStyle = {
                            width: element.width,
                            height: element.height,
                            borderRadius: element.borderRadius ? `${element.borderRadius}px` : undefined,
                            borderWidth: element.borderWidth ? `${element.borderWidth}px` : undefined,
                            borderColor: element.borderColor || 'transparent',
                            borderStyle: element.borderWidth ? 'solid' : 'none'
                          };

                          if (key === 'schoolLogo') {
                            return (
                              <div
                                key={key}
                                className={`absolute cursor-move ${element.locked ? 'cursor-not-allowed' : ''} ${
                                  selectedElement === key ? 'ring-2 ring-blue-400' : ''
                                }`}
                                style={{ ...commonStyle, ...imageStyle }}
                                onMouseDown={(e) => handleElementMouseDown(key, e)}
                              >
                                {settings.schoolLogo ? (
                                  <img 
                                    src={settings.schoolLogo} 
                                    alt="School Logo" 
                                    className="w-full h-full object-contain bg-white rounded p-0.5"
                                  />
                                ) : (
                                  <div className="w-full h-full bg-white rounded flex items-center justify-center">
                                    <span className="text-orange-500 text-sm">🎓</span>
                                  </div>
                                )}
                              </div>
                            );
                          }

                          if (key === 'photo') {
                            return (
                              <div
                                key={key}
                                className={`absolute cursor-move border-2 border-gray-300 bg-gray-100 ${element.locked ? 'cursor-not-allowed' : ''} ${
                                  selectedElement === key ? 'ring-2 ring-blue-400' : ''
                                }`}
                                style={{ ...commonStyle, ...imageStyle }}
                                onMouseDown={(e) => handleElementMouseDown(key, e)}
                              >
                                {studentImages[safeStudent.id] ? (
                                  <img 
                                    src={studentImages[safeStudent.id]} 
                                    alt="Student" 
                                    className="w-full h-full object-cover"
                                  />
                                ) : (
                                  <div className="w-full h-full flex items-center justify-center text-gray-400">
                                    <ImageIcon className="h-8 w-8" />
                                  </div>
                                )}
                              </div>
                            );
                          }

                          if (key === 'barcode') {
                            return (
                              <div
                                key={key}
                                className={`absolute cursor-move ${element.locked ? 'cursor-not-allowed' : ''} ${
                                  selectedElement === key ? 'ring-2 ring-blue-400' : ''
                                }`}
                                style={{ ...commonStyle, ...imageStyle }}
                                onMouseDown={(e) => handleElementMouseDown(key, e)}
                              >
                                {generateBarcode(safeStudent.student_id) && (
                                  <img 
                                    src={generateBarcode(safeStudent.student_id)!} 
                                    alt="Barcode" 
                                    className="w-full h-full object-contain"
                                  />
                                )}
                              </div>
                            );
                          }

                          // Text elements
                          let displayText = '';
                          if (element.text) {
                            displayText = element.text;
                          } else {
                            switch (key) {
                              case 'schoolName':
                                displayText = settings.schoolName;
                                break;
                              case 'studentName':
                                displayText = safeStudent.name;
                                break;
                              case 'studentId':
                                displayText = `ID: ${safeStudent.student_id}`;
                                break;
                              case 'course':
                                displayText = `Course: ${safeStudent.course?.name || 'N/A'}`;
                                break;
                              case 'level':
                                displayText = `Level: ${safeStudent.level?.name || 'N/A'}`;
                                break;
                              case 'validUntil':
                                displayText = `Valid Until: ${settings.validUntil}`;
                                break;
                              case 'authorizedBy':
                                displayText = `Authorized By: ${settings.authorizedBy}`;
                                break;
                              case 'contactNumber':
                                displayText = `Contact: ${settings.contactNumber}`;
                                break;
                            }
                          }

                          return (
                            <div
                              key={key}
                              className={`absolute cursor-move ${element.locked ? 'cursor-not-allowed' : ''} ${
                                selectedElement === key ? 'ring-2 ring-blue-400' : ''
                              }`}
                              style={{ ...commonStyle, ...textStyle }}
                              onMouseDown={(e) => handleElementMouseDown(key, e)}
                            >
                              {displayText}
                            </div>
                          );
                        })}
                    </div>
                  </div>

                  {/* BACK SIDE */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 mb-2 text-center">Back Side</h4>
                    <div 
                      data-card-side="back"
                      className="relative w-[400px] h-[250px] bg-white rounded-lg overflow-hidden shadow-lg border cursor-crosshair"
                      style={{ 
                        fontFamily: 'Arial, sans-serif',
                        backgroundColor: backgroundSettings.backBackground,
                        borderRadius: `${cardSettings.borderRadius}px`,
                        borderWidth: `${cardSettings.borderWidth}px`,
                        borderColor: cardSettings.borderColor,
                        boxShadow: `0 ${cardSettings.shadowIntensity}px ${cardSettings.shadowIntensity * 2}px rgba(0,0,0,0.1)`,
                        backgroundImage: showGrid ? `url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23e5e7eb' fill-opacity='0.3'%3E%3Cpath d='M0 0h1v1H0V0zm10 0h1v1h-1V0z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")` : 'none'
                      }}
                      onMouseMove={handleMouseMove}
                      onMouseUp={handleMouseUp}
                      onMouseLeave={handleMouseUp}
                    >
                      {/* Header with geometric design */}
                      <div className="relative h-12 overflow-hidden">
                        <div 
                          className="absolute top-0 left-0 w-[500px] h-16 transform -skew-x-12 -translate-x-20"
                          style={{ backgroundColor: '#f97316' }}
                        ></div>
                        <div 
                          className="absolute top-0 right-0 w-[500px] h-16 transform skew-x-12 translate-x-20"
                          style={{ backgroundColor: settings.headerColor }}
                        ></div>
                        
                        {/* School logo and name on orange section */}
                        <div className="absolute top-2 left-4 flex items-center gap-2 text-white z-10">
                          {settings.schoolLogo ? (
                            <img 
                              src={settings.schoolLogo} 
                              alt="School Logo" 
                              className="w-5 h-5 object-contain bg-white rounded p-0.5"
                            />
                          ) : (
                            <div className="w-5 h-5 bg-white rounded flex items-center justify-center">
                              <span className="text-orange-500 text-xs">🎓</span>
                            </div>
                          )}
                          <span className="font-bold text-xs">{settings.schoolName}</span>
                        </div>
                      </div>

                      {/* Back side content */}
                      <div className="px-4 py-2 space-y-2 h-[190px] overflow-hidden">
                        <div className="text-center">
                          <h3 
                            className="text-sm font-bold"
                            style={{ color: settings.headerColor }}
                          >
                            IMPORTANT INFORMATION
                          </h3>
                        </div>

                        <div className="space-y-2 text-xs">
                          <div className="flex justify-between">
                            <span className="font-semibold text-gray-700">Valid Until:</span>
                            <span className="text-gray-700">{settings.validUntil}</span>
                          </div>
                          
                          <div className="flex justify-between">
                            <span className="font-semibold text-gray-700">Authorized By:</span>
                            <span className="text-gray-700">{settings.authorizedBy}</span>
                          </div>
                          
                          <div className="flex justify-between">
                            <span className="font-semibold text-gray-700">Contact:</span>
                            <span className="text-gray-700">{settings.contactNumber}</span>
                          </div>
                        </div>

                        {/* Terms and conditions */}
                        <div className="mt-4 pt-3 border-t border-gray-200">
                          <div className="text-xs text-gray-600 leading-tight">
                            <p className="font-semibold mb-2">Terms & Conditions:</p>
                            <ul className="space-y-1 text-xs">
                              <li>• This card is property of {settings.schoolName}</li>
                              <li>• Must be carried at all times on campus</li>
                              <li>• Report immediately if lost or stolen</li>
                              <li>• Non-transferable and valid for current academic year</li>
                              <li>• Replacement fee applies for lost cards</li>
                              <li>• Card must be returned upon graduation or withdrawal</li>
                            </ul>
                          </div>
                          
                          <div className="mt-4 pt-2 border-t border-gray-200 text-center">
                            <div className="w-32 h-8 border border-gray-400 mx-auto mb-1 flex items-center justify-center">
                              <span className="text-xs text-gray-500">Authorized Signature</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Back side draggable elements */}
                      {Object.entries(elements)
                        .sort(([,a], [,b]) => (a.zIndex || 0) - (b.zIndex || 0))
                        .map(([key, element]) => {
                          if (!element.visible || element.y < 250) return null;

                          const adjustedY = element.y - 250; // Adjust for back side positioning
                          const commonStyle = {
                            left: element.x,
                            top: adjustedY,
                            opacity: element.opacity || 1,
                            zIndex: element.zIndex || 1,
                            transform: element.rotation ? `rotate(${element.rotation}deg)` : undefined
                          };

                          const textStyle = {
                            fontSize: element.fontSize,
                            color: element.color,
                            fontWeight: element.fontWeight || 'normal',
                            fontStyle: element.fontStyle || 'normal',
                            textDecoration: element.textDecoration || 'none',
                            textAlign: element.textAlign || 'left',
                            backgroundColor: element.backgroundColor || 'transparent',
                            borderRadius: element.borderRadius ? `${element.borderRadius}px` : undefined,
                            padding: element.backgroundColor ? '2px 4px' : undefined
                          };

                          let displayText = element.text || '';
                          if (!displayText) {
                            switch (key) {
                              case 'schoolName':
                                displayText = settings.schoolName;
                                break;
                              case 'studentName':
                                displayText = safeStudent.name;
                                break;
                              case 'studentId':
                                displayText = `ID: ${safeStudent.student_id}`;
                                break;
                              case 'course':
                                displayText = `Course: ${safeStudent.course?.name || 'N/A'}`;
                                break;
                              case 'level':
                                displayText = `Level: ${safeStudent.level?.name || 'N/A'}`;
                                break;
                              case 'validUntil':
                                displayText = `Valid Until: ${settings.validUntil}`;
                                break;
                              case 'authorizedBy':
                                displayText = `Authorized By: ${settings.authorizedBy}`;
                                break;
                              case 'contactNumber':
                                displayText = `Contact: ${settings.contactNumber}`;
                                break;
                            }
                          }

                          return (
                            <div
                              key={`${key}-back`}
                              className={`absolute cursor-move ${element.locked ? 'cursor-not-allowed' : ''} ${
                                selectedElement === key ? 'ring-2 ring-blue-400' : ''
                              }`}
                              style={{ ...commonStyle, ...textStyle }}
                              onMouseDown={(e) => handleElementMouseDown(key, e)}
                            >
                              {displayText}
                            </div>
                          );
                        })}
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-4 text-center text-sm text-gray-500">
                Click and drag elements to reposition them. Use the controls on the left to adjust properties.
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default IdCardVisualEditor; 