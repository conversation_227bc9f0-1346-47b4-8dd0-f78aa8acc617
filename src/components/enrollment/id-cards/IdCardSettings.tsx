import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ArrowLeft, Save, Loader2, Upload, Trash2 } from "lucide-react";
import { toast } from "sonner";

export const IdCardSettings = () => {
  const navigate = useNavigate();
  const [isSaving, setIsSaving] = useState(false);

  const [settings, setSettings] = useState({
    schoolName: "BEY CENTER",
    schoolLogo: "/logo.svg",
    schoolAddress: "123 Education Street, Learning City, LC 12345",
    headerColor: "#1e40af",
    bodyColor: "#ffffff",
    textColor: "#000000",
    defaultValidityPeriod: "1 year",
    defaultAuthorizedBy: "Principal",
    contactNumber: "+447360680621",
    address: "",
    website: "",
    enableQRCode: false,
    enableBarcode: false,
    cardWidth: 85.6,
    cardHeight: 53.98,
    logoSize: 32,
    fontSize: 12,
    // Course Settings
    showCourseOnCard: true,
    courseFontSize: 12,
    courseMaxLineLength: 25,
    coursePosition: "afterId", // "afterId", "afterName", "beforeLevel"
    abbreviateLongCourses: false,
    customCourseAbbreviations: {},
  });

  // Load settings from localStorage on component mount
  useEffect(() => {
    const savedSettings = localStorage.getItem('idCardSettings');
    if (savedSettings) {
      try {
        const parsedSettings = JSON.parse(savedSettings);
        setSettings(prev => ({ ...prev, ...parsedSettings }));
      } catch (error) {
        console.error('Error loading ID card settings:', error);
      }
    }
  }, []);

  const handleSettingChange = (field: string, value: any) => {
    setSettings(prev => ({ ...prev, [field]: value }));
  };

  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.size > 2 * 1024 * 1024) {
        toast.error("Logo file size should be less than 2MB");
        return;
      }
      
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        handleSettingChange("schoolLogo", result);
      };
      reader.readAsDataURL(file);
    }
  };

  const removeLogo = () => {
    handleSettingChange("schoolLogo", "");
  };

  const saveSettings = async () => {
    setIsSaving(true);
    try {
      localStorage.setItem('idCardSettings', JSON.stringify(settings));
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success("ID card settings saved successfully!");
    } catch (error) {
      toast.error("Failed to save settings");
    } finally {
      setIsSaving(false);
    }
  };

  const resetToDefaults = () => {
    setSettings({
      schoolName: "BEY CENTER",
      schoolLogo: "/logo.svg",
      schoolAddress: "123 Education Street, Learning City, LC 12345",
      headerColor: "#1e40af",
      bodyColor: "#ffffff",
      textColor: "#000000",
      defaultValidityPeriod: "1 year",
      defaultAuthorizedBy: "Principal",
      contactNumber: "+447360680621",
      address: "",
      website: "",
      enableQRCode: false,
      enableBarcode: false,
      cardWidth: 85.6,
      cardHeight: 53.98,
      logoSize: 32,
      fontSize: 12,
      // Course Settings
      showCourseOnCard: true,
      courseFontSize: 12,
      courseMaxLineLength: 25,
      coursePosition: "afterId", // "afterId", "afterName", "beforeLevel"
      abbreviateLongCourses: false,
      customCourseAbbreviations: {},
    });
    toast.success("Settings reset to defaults");
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4 mb-4">
        <Button
          variant="outline"
          size="icon"
          onClick={() => navigate("/dashboard/enrollment/id-cards")}
          className="h-8 w-8"
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div className="flex-1">
          <h2 className="text-2xl font-bold">ID Card Settings</h2>
          <p className="text-muted-foreground">Configure default settings for ID card generation</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={resetToDefaults}>
            Reset to Defaults
          </Button>
          <Button onClick={saveSettings} disabled={isSaving}>
            {isSaving ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            Save Settings
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>School Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="schoolName">School Name</Label>
              <Input
                id="schoolName"
                value={settings.schoolName}
                onChange={(e) => handleSettingChange("schoolName", e.target.value)}
                placeholder="Enter school name"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="schoolLogo">School Logo</Label>
              <div className="flex items-center gap-4">
                {settings.schoolLogo ? (
                  <div className="flex items-center gap-2">
                    <img 
                      src={settings.schoolLogo} 
                      alt="School Logo" 
                      className="w-16 h-16 object-contain border rounded"
                    />
                    <Button variant="outline" size="icon" onClick={removeLogo}>
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ) : (
                  <div className="w-16 h-16 border-2 border-dashed border-gray-300 rounded flex items-center justify-center">
                    <span className="text-gray-400 text-xs">No Logo</span>
                  </div>
                )}
                <div>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleLogoUpload}
                    className="hidden"
                    id="logoUpload"
                  />
                  <Button
                    variant="outline"
                    onClick={() => document.getElementById('logoUpload')?.click()}
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    Upload Logo
                  </Button>
                  <p className="text-xs text-gray-500 mt-1">Max 2MB, PNG/JPG</p>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="contactNumber">Contact Number</Label>
              <Input
                id="contactNumber"
                value={settings.contactNumber}
                onChange={(e) => handleSettingChange("contactNumber", e.target.value)}
                placeholder="+447360680621"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="schoolAddress">School Address</Label>
              <Textarea
                id="schoolAddress"
                value={settings.schoolAddress}
                onChange={(e) => handleSettingChange("schoolAddress", e.target.value)}
                placeholder="Enter complete school address"
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Design Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="headerColor">Header Color</Label>
              <div className="flex gap-2">
                <Input
                  id="headerColor"
                  type="color"
                  value={settings.headerColor}
                  onChange={(e) => handleSettingChange("headerColor", e.target.value)}
                  className="w-16 h-10"
                />
                <Input
                  value={settings.headerColor}
                  onChange={(e) => handleSettingChange("headerColor", e.target.value)}
                  placeholder="#1e40af"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="defaultValidityPeriod">Default Validity Period</Label>
              <Input
                id="defaultValidityPeriod"
                value={settings.defaultValidityPeriod}
                onChange={(e) => handleSettingChange("defaultValidityPeriod", e.target.value)}
                placeholder="e.g., 1 year, Dec 2025"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="defaultAuthorizedBy">Default Authorized By</Label>
              <Input
                id="defaultAuthorizedBy"
                value={settings.defaultAuthorizedBy}
                onChange={(e) => handleSettingChange("defaultAuthorizedBy", e.target.value)}
                placeholder="e.g., Principal, Director"
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Course Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Switch
                  id="showCourseOnCard"
                  checked={settings.showCourseOnCard}
                  onCheckedChange={(checked) => handleSettingChange("showCourseOnCard", checked)}
                />
                <Label htmlFor="showCourseOnCard">Show Course on ID Card</Label>
              </div>
              <p className="text-xs text-gray-500">Display course information on the student ID card</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="courseMaxLineLength">Course Line Length</Label>
              <Input
                id="courseMaxLineLength"
                type="number"
                value={settings.courseMaxLineLength}
                onChange={(e) => handleSettingChange("courseMaxLineLength", parseInt(e.target.value))}
                placeholder="25"
                min="15"
                max="50"
              />
              <p className="text-xs text-gray-500">Maximum characters per line before wrapping</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="coursePosition">Course Position</Label>
              <Select value={settings.coursePosition} onValueChange={(value) => handleSettingChange("coursePosition", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select course position" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="afterId">After Student ID</SelectItem>
                  <SelectItem value="afterName">After Student Name</SelectItem>
                  <SelectItem value="beforeLevel">Before Level</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-xs text-gray-500">Where to display the course field on the ID card</p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Switch
                  id="abbreviateLongCourses"
                  checked={settings.abbreviateLongCourses}
                  onCheckedChange={(checked) => handleSettingChange("abbreviateLongCourses", checked)}
                />
                <Label htmlFor="abbreviateLongCourses">Auto-Abbreviate Long Courses</Label>
              </div>
              <p className="text-xs text-gray-500">Automatically abbreviate very long course names</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="courseFontSize">Course Font Size</Label>
              <Input
                id="courseFontSize"
                type="number"
                value={settings.courseFontSize}
                onChange={(e) => handleSettingChange("courseFontSize", parseInt(e.target.value))}
                placeholder="12"
                min="8"
                max="16"
              />
              <p className="text-xs text-gray-500">Font size for course text (8-16px)</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default IdCardSettings; 