import { Routes, Route, Navigate } from 'react-router-dom';
import { Suspense, lazy } from 'react';
import { Loader2 } from 'lucide-react';

// Lazy load components for better performance
const IdCardManagement = lazy(() => import('./IdCardManagement'));
const IdCardGenerator = lazy(() => import('./IdCardGenerator'));
const IdCardViewer = lazy(() => import('./IdCardViewer'));
const IdCardSettings = lazy(() => import('./IdCardSettings'));
const IdCardVisualEditor = lazy(() => import('./IdCardVisualEditor'));

const LoadingSpinner = () => (
  <div className="flex items-center justify-center h-64">
    <Loader2 className="h-8 w-8 animate-spin" />
  </div>
);

const IdCardDashboard = () => {
  return (
    <Suspense fallback={<LoadingSpinner />}>
      <Routes>
        <Route path="" element={<IdCardManagement />} />
        <Route path="generate/:id" element={<IdCardGenerator />} />
        <Route path="view/:id" element={<IdCardViewer />} />
        <Route path="edit/:id" element={<IdCardGenerator />} />
        <Route path="edit" element={<Navigate to="visual-edit/demo-student" replace />} />
        <Route path="visual-edit/:id" element={<IdCardVisualEditor />} />
        <Route path="visual-edit" element={<Navigate to="visual-edit/demo-student" replace />} />
        <Route path="settings" element={<IdCardSettings />} />
      </Routes>
    </Suspense>
  );
};

export { IdCardDashboard };
export default IdCardDashboard; 