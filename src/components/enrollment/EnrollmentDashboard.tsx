import { Routes, Route, useLocation } from 'react-router-dom';
import { useEffect, lazy, Suspense, memo } from 'react';
import type { Dispatch, SetStateAction } from 'react';

// Use lazy loading for sub-components
const StudentList = lazy(() => import('./StudentList').then(module => ({ default: module.StudentList })));
const AddNewStudent = lazy(() => import('./AddNewStudent').then(module => ({ default: module.AddNewStudent })));
const EditStudent = lazy(() => import('./EditStudent').then(module => ({ default: module.EditStudent })));
const StudentProfile = lazy(() => import('./StudentProfile').then(module => ({ default: module.StudentProfile })));
const AttendanceManagement = lazy(() => import('./attendance/AttendanceManagement').then(module => ({ default: module.AttendanceManagement })));
const IdCardDashboard = lazy(() => import('./id-cards/IdCardDashboard').then(module => ({ default: module.IdCardDashboard })));

// Create memoized components to prevent unnecessary re-renders
const MemoizedStudentList = memo(() => <StudentList />);
const MemoizedAddNewStudent = memo(() => <AddNewStudent />);
const MemoizedEditStudent = memo((props: any) => <EditStudent {...props} />);
const MemoizedStudentProfile = memo((props: any) => <StudentProfile {...props} />);
const MemoizedAttendanceManagement = memo(() => <AttendanceManagement />);
const MemoizedIdCardDashboard = memo(() => <IdCardDashboard />);

interface EnrollmentDashboardProps {
  setSidebarOpen: Dispatch<SetStateAction<boolean>>;
}

const EnrollmentDashboard = ({ setSidebarOpen }: EnrollmentDashboardProps) => {
  const location = useLocation();

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 1024) {
        setSidebarOpen(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [setSidebarOpen]);

  // Get the current path to determine which sub-route is active
  const currentPath = location.pathname.split('/').pop() || '';
  
  // Custom loading message based on current section
  const getLoadingMessage = () => {
    if (currentPath === 'new') return 'Loading new student form...';
    if (currentPath === 'attendance') return 'Loading attendance management...';
    if (currentPath === 'id-cards') return 'Loading ID card management...';
    if (currentPath.includes('edit')) return 'Loading student editor...';
    if (currentPath.includes('student')) return 'Loading student profile...';
    return 'Loading student list...';
  };

  return (
    <div className="space-y-6">
      <Suspense fallback={
        <div className="flex flex-col items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-700 mb-4" />
          <p className="text-green-700">{getLoadingMessage()}</p>
        </div>
      }>
        <Routes>
          <Route index element={<MemoizedStudentList />} />
          <Route path="new" element={<MemoizedAddNewStudent />} />
          <Route path="edit/:id" element={<MemoizedEditStudent />} />
          <Route path="student/:id" element={<MemoizedStudentProfile />} />
          <Route path="attendance" element={<MemoizedAttendanceManagement />} />
          <Route path="id-cards/*" element={<MemoizedIdCardDashboard />} />
        </Routes>
      </Suspense>
    </div>
  );
};

export default EnrollmentDashboard;
