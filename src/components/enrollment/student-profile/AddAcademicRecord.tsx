
import { useState } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

interface AddAcademicRecordProps {
  studentId: string;
  onClose?: () => void;
  onSuccess?: () => void;
}

export const AddAcademicRecord = ({ studentId, onClose, onSuccess }: AddAcademicRecordProps) => {
  const queryClient = useQueryClient();
  const [record, setRecord] = useState({
    subject: '',
    grade: '',
    term: '',
    year: new Date().getFullYear().toString()
  });

  const addRecordMutation = useMutation({
    mutationFn: async (data: typeof record) => {
      const { error } = await supabase
        .from('academic_records')
        .insert([{ ...data, student_id: studentId }]);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['academic-records', studentId] });
      toast.success('Academic record added successfully');
      setRecord({
        subject: '',
        grade: '',
        term: '',
        year: new Date().getFullYear().toString()
      });
      onSuccess?.();
      onClose?.();
    },
    onError: (error) => {
      console.error('Error adding academic record:', error);
      toast.error('Failed to add academic record');
    }
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    addRecordMutation.mutate(record);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <Input
        placeholder="Subject"
        value={record.subject}
        onChange={e => setRecord({ ...record, subject: e.target.value })}
        required
      />
      <Input
        placeholder="Grade"
        value={record.grade}
        onChange={e => setRecord({ ...record, grade: e.target.value })}
        required
      />
      <Input
        placeholder="Term"
        value={record.term}
        onChange={e => setRecord({ ...record, term: e.target.value })}
        required
      />
      <Input
        placeholder="Year"
        value={record.year}
        onChange={e => setRecord({ ...record, year: e.target.value })}
        required
      />
      <Button type="submit" disabled={addRecordMutation.isPending}>
        Add Record
      </Button>
    </form>
  );
};
