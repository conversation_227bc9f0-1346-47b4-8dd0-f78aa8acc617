import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Loader2, Mail, Key, Save, RefreshCw } from "lucide-react";
import { toast } from "sonner";
import { apiClient } from "@/lib/api-client";
import { AuthService } from "@/lib/auth";
import { Student } from "@/types/student";

interface LoginManagementProps {
  student: Student;
}

export const LoginManagement = ({ student }: LoginManagementProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const [hasAccount, setHasAccount] = useState(false);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [dashboardAccess, setDashboardAccess] = useState(false);
  const [userId, setUserId] = useState("");

  // Load existing login details if available
  useEffect(() => {
    const loadLoginDetails = async () => {
      try {
        // Check if student has login_details field
        if (student.login_details?.user_id) {
          setUserId(student.login_details.user_id);
          setHasAccount(true);
          setEmail(student.login_details.email || "");
          setDashboardAccess(student.login_details.dashboard_access || false);
        }
      } catch (error) {
        console.error("Error loading login details:", error);
      }
    };

    loadLoginDetails();
  }, [student]);

  const validateForm = () => {
    if (!email) {
      toast.error("Email is required");
      return false;
    }

    if (!hasAccount && !password) {
      toast.error("Password is required for new accounts");
      return false;
    }

    if (!hasAccount && password !== confirmPassword) {
      toast.error("Passwords do not match");
      return false;
    }

    if (!hasAccount && password.length < 6) {
      toast.error("Password must be at least 6 characters");
      return false;
    }

    return true;
  };

  const handleCreateAccount = async () => {
    if (!validateForm()) return;

    setIsLoading(true);
    try {
      // Create new user account via API
      const response = await AuthService.signUp(student.name, email, password, 'student');
      const newUserId = response.user.id;

      // Update student record with login details
      await apiClient.put(`/students/${student.id}`, {
        login_details: {
          user_id: newUserId,
          email: email,
          dashboard_access: dashboardAccess,
          created_at: new Date().toISOString()
        }
      });

      // Update local state
      setUserId(newUserId);
      setHasAccount(true);
      setPassword("");
      setConfirmPassword("");

      toast.success("Student account created successfully");
    } catch (error: any) {
      console.error("Error creating student account:", error);
      toast.error(error.message || "Failed to create student account");
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpdateAccount = async () => {
    if (!validateForm()) return;

    setIsLoading(true);
    try {
      // Update student record with login details
      await apiClient.put(`/students/${student.id}`, {
        login_details: {
          user_id: userId,
          email: email,
          dashboard_access: dashboardAccess,
          updated_at: new Date().toISOString()
        }
      });

      toast.success("Login details updated successfully");
    } catch (error: any) {
      console.error("Error updating login details:", error);
      toast.error(error.message || "Failed to update login details");
    } finally {
      setIsLoading(false);
    }
  };

  const handleResetPassword = async () => {
    if (!email) {
      toast.error("Email is required to reset password");
      return;
    }

    setIsLoading(true);
    try {
      // Note: This would need to be implemented in the PHP backend
      await apiClient.post('/auth/reset-password', { email });
      toast.success("Password reset email sent");
    } catch (error: any) {
      console.error("Error sending password reset:", error);
      toast.error(error.message || "Failed to send password reset email");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Login Management</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-center space-x-2">
          <Switch
            id="dashboard-access"
            checked={dashboardAccess}
            onCheckedChange={setDashboardAccess}
          />
          <Label htmlFor="dashboard-access">Enable Student Dashboard Access</Label>
        </div>

        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email">Email Address</Label>
            <div className="relative">
              <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 h-4 w-4" />
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>"
                className="pl-10"
                disabled={isLoading}
              />
            </div>
          </div>

          {!hasAccount && (
            <>
              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <div className="relative">
                  <Key className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 h-4 w-4" />
                  <Input
                    id="password"
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="••••••••"
                    className="pl-10"
                    disabled={isLoading}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="confirm-password">Confirm Password</Label>
                <div className="relative">
                  <Key className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 h-4 w-4" />
                  <Input
                    id="confirm-password"
                    type="password"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    placeholder="••••••••"
                    className="pl-10"
                    disabled={isLoading}
                  />
                </div>
              </div>
            </>
          )}

          <div className="flex flex-col sm:flex-row gap-3 pt-2">
            {!hasAccount ? (
              <Button
                onClick={handleCreateAccount}
                disabled={isLoading}
                className="flex-1"
              >
                {isLoading ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Save className="mr-2 h-4 w-4" />
                )}
                Create Account
              </Button>
            ) : (
              <>
                <Button
                  onClick={handleUpdateAccount}
                  disabled={isLoading}
                  className="flex-1"
                >
                  {isLoading ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <Save className="mr-2 h-4 w-4" />
                  )}
                  Update Details
                </Button>
                <Button
                  onClick={handleResetPassword}
                  disabled={isLoading}
                  variant="outline"
                  className="flex-1"
                >
                  {isLoading ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <RefreshCw className="mr-2 h-4 w-4" />
                  )}
                  Reset Password
                </Button>
              </>
            )}
          </div>
        </div>

        {hasAccount && (
          <div className="mt-4 p-4 bg-green-50 rounded-md">
            <p className="text-sm text-green-800">
              This student has an active account. They can log in to the student dashboard using their email and password.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}; 