
import { useQuery } from "@tanstack/react-query";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { getStudents } from '@/api/students';
import { getCourses } from '@/api/courses';

export const EnrollmentStats = () => {
  const { data: students = [] } = useQuery({
    queryKey: ['students'],
    queryFn: getStudents,
  });

  const { data: courses = [] } = useQuery({
    queryKey: ['courses'],
    queryFn: getCourses,
  });

  // Calculate statistics
  const totalStudents = students.length;
  const activeCourses = courses.length;
  const newEnrollments = students.filter(
    student => {
      const enrollmentDate = new Date(student.date_of_registration);
      const currentDate = new Date();
      const monthDiff = currentDate.getMonth() - enrollmentDate.getMonth() + 
        (12 * (currentDate.getFullYear() - enrollmentDate.getFullYear()));
      return monthDiff === 0;
    }
  ).length;

  const completionRate = students.length > 0 
    ? Math.round((students.filter(s => s.payment_status === 'paid').length / students.length) * 100)
    : 0;

  const totalCompletedFees = students.filter(s => s.payment_status === 'paid').length;

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Total Students</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{totalStudents}</div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Active Courses</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{activeCourses}</div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">New Enrollments</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{newEnrollments}</div>
          <p className="text-xs text-muted-foreground">This month</p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Completion Rate</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{completionRate}%</div>
          <p className="text-xs text-muted-foreground">{totalCompletedFees} students completed fees</p>
        </CardContent>
      </Card>
    </div>
  );
};
