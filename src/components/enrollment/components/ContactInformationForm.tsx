
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface ContactInformationFormProps {
  studentDetails: any;
  onStudentDetailsChange: (field: string, value: string) => void;
}

export const ContactInformationForm = ({
  studentDetails,
  onStudentDetailsChange,
}: ContactInformationFormProps) => {
  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="address">Address</Label>
        <Input
          id="address"
          value={studentDetails.address}
          onChange={(e) => onStudentDetailsChange("address", e.target.value)}
          placeholder="Enter address"
          required
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="mobile_number">Mobile Number</Label>
        <Input
          id="mobile_number"
          value={studentDetails.mobile_number}
          onChange={(e) => onStudentDetailsChange("mobile_number", e.target.value)}
          placeholder="Enter mobile number"
          required
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="whatsapp_number">WhatsApp Number</Label>
        <Input
          id="whatsapp_number"
          value={studentDetails.whatsapp_number}
          onChange={(e) => onStudentDetailsChange("whatsapp_number", e.target.value)}
          placeholder="Enter WhatsApp number"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="parent_name">Parent/Guardian Name</Label>
        <Input
          id="parent_name"
          value={studentDetails.parent_name}
          onChange={(e) => onStudentDetailsChange("parent_name", e.target.value)}
          placeholder="Enter parent/guardian name"
          required
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="parent_mobile">Parent/Guardian Mobile</Label>
        <Input
          id="parent_mobile"
          value={studentDetails.parent_mobile}
          onChange={(e) => onStudentDetailsChange("parent_mobile", e.target.value)}
          placeholder="Enter parent/guardian mobile"
          required
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="parent_whatsapp">Parent/Guardian WhatsApp</Label>
        <Input
          id="parent_whatsapp"
          value={studentDetails.parent_whatsapp}
          onChange={(e) => onStudentDetailsChange("parent_whatsapp", e.target.value)}
          placeholder="Enter parent/guardian WhatsApp number"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="parent_email">Parent/Guardian Email</Label>
        <Input
          id="parent_email"
          type="email"
          value={studentDetails.parent_email}
          onChange={(e) => onStudentDetailsChange("parent_email", e.target.value)}
          placeholder="Enter parent/guardian email"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="parent_occupation">Parent/Guardian Occupation</Label>
        <Input
          id="parent_occupation"
          value={studentDetails.parent_occupation}
          onChange={(e) => onStudentDetailsChange("parent_occupation", e.target.value)}
          placeholder="Enter parent/guardian occupation"
        />
      </div>
    </div>
  );
};
