import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { toast } from '@/hooks/use-toast';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { supabase } from "@/integrations/supabase/client";

const formSchema = z.object({
  name: z.string().min(1, "Name is required"),
  student_id: z.string().min(1, "Student ID is required"),
  date_of_birth: z.string().min(1, "Date of Birth is required"),
  nationality: z.string().min(1, "Nationality is required"),
  gender: z.enum(["male", "female", "other"]),
  address: z.string().optional(),
  mobile_number: z.string().optional(),
  whatsapp_number: z.string().optional(),
  parent_name: z.string().optional(),
  parent_mobile: z.string().optional(),
  parent_whatsapp: z.string().optional(),
  parent_email: z.string().optional(),
  parent_occupation: z.string().optional(),
  course_id: z.string().min(1, "Course is required"),
  level_id: z.string().min(1, "Level is required"),
  enrollment_status: z.enum(["Active", "Inactive"]),
});

export const StudentForm = () => {
  const [loading, setLoading] = useState(false);
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      student_id: "",
      date_of_birth: "",
      nationality: "",
      gender: "male",
      address: "",
      mobile_number: "",
      whatsapp_number: "",
      parent_name: "",
      parent_mobile: "",
      parent_whatsapp: "",
      parent_email: "",
      parent_occupation: "",
      course_id: "",
      level_id: "",
      enrollment_status: "Active",
    },
  });

  const handleSubmit = async (values: z.infer<typeof formSchema>) => {
    setLoading(true);
    try {
      const { error } = await supabase.from("students").insert(values);
      if (error) throw error;
      toast({
        title: "Success",
        description: "Student added successfully"
      });
    } catch (error) {
      console.error("Error:", error);
      toast({
        title: "Error",
        description: "Failed to submit form",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
      <div>
        <Label htmlFor="name">Name</Label>
        <Input id="name" {...form.register("name")} />
      </div>
      <div>
        <Label htmlFor="student_id">Student ID</Label>
        <Input id="student_id" {...form.register("student_id")} />
      </div>
      <div>
        <Label htmlFor="date_of_birth">Date of Birth</Label>
        <Input type="date" id="date_of_birth" {...form.register("date_of_birth")} />
      </div>
      <div>
        <Label htmlFor="nationality">Nationality</Label>
        <Input id="nationality" {...form.register("nationality")} />
      </div>
      <div>
        <Label htmlFor="gender">Gender</Label>
        <Select {...form.register("gender")}>
          <SelectTrigger>
            <SelectValue placeholder="Select gender" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="male">Male</SelectItem>
            <SelectItem value="female">Female</SelectItem>
            <SelectItem value="other">Other</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div>
        <Label htmlFor="address">Address</Label>
        <Textarea id="address" {...form.register("address")} />
      </div>
      <div>
        <Label htmlFor="mobile_number">Mobile Number</Label>
        <Input id="mobile_number" {...form.register("mobile_number")} />
      </div>
      <div>
        <Label htmlFor="whatsapp_number">WhatsApp Number</Label>
        <Input id="whatsapp_number" {...form.register("whatsapp_number")} />
      </div>
      <div>
        <Label htmlFor="parent_name">Parent Name</Label>
        <Input id="parent_name" {...form.register("parent_name")} />
      </div>
      <div>
        <Label htmlFor="parent_mobile">Parent Mobile</Label>
        <Input id="parent_mobile" {...form.register("parent_mobile")} />
      </div>
      <div>
        <Label htmlFor="parent_whatsapp">Parent WhatsApp</Label>
        <Input id="parent_whatsapp" {...form.register("parent_whatsapp")} />
      </div>
      <div>
        <Label htmlFor="parent_email">Parent Email</Label>
        <Input id="parent_email" {...form.register("parent_email")} />
      </div>
      <div>
        <Label htmlFor="parent_occupation">Parent Occupation</Label>
        <Input id="parent_occupation" {...form.register("parent_occupation")} />
      </div>
      <div>
        <Label htmlFor="course_id">Course</Label>
        <Input id="course_id" {...form.register("course_id")} />
      </div>
      <div>
        <Label htmlFor="level_id">Level</Label>
        <Input id="level_id" {...form.register("level_id")} />
      </div>
      <div>
        <Label htmlFor="enrollment_status">Enrollment Status</Label>
        <Select {...form.register("enrollment_status")}>
          <SelectTrigger>
            <SelectValue placeholder="Select status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="Active">Active</SelectItem>
            <SelectItem value="Inactive">Inactive</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <Button type="submit" disabled={loading}>
        {loading ? "Submitting..." : "Submit"}
      </Button>
    </form>
  );
};
