import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { Pencil, Trash2, Search, UserCircle, Download, Loader2, CreditCard, RefreshCw } from "lucide-react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { getStudents, deleteStudent } from "@/api/students";
import { getCourses } from "@/api/courses";
import { getLevels } from "@/api/levels";
import { recalculateAllStudentPaymentStatus } from "@/api/payments";
import { exportStudentsData } from "./utils/exportStudentData";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export const StudentList = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCourse, setSelectedCourse] = useState('all');
  const [selectedLevel, setSelectedLevel] = useState('all');
  const [selectedPaymentStatus, setSelectedPaymentStatus] = useState('all');
  const [isExporting, setIsExporting] = useState(false);
  const [isUpdatingPayments, setIsUpdatingPayments] = useState(false);
  const queryClient = useQueryClient();

  const { data: courses = [] } = useQuery({
    queryKey: ['courses'],
    queryFn: getCourses
  });

  const { data: levels = [] } = useQuery({
    queryKey: ['levels'],
    queryFn: getLevels
  });

  const { data: students = [], isLoading, refetch } = useQuery({
    queryKey: ['students'],
    queryFn: getStudents,
    staleTime: 0,
    gcTime: 0  // Using gcTime instead of cacheTime
  });

  const deleteMutation = useMutation({
    mutationFn: async (id: string) => {
      return await deleteStudent(id);
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({ queryKey: ['students'] });
      await refetch();
      toast.success("Student deleted successfully");
    },
    onError: (error) => {
      console.error('Error deleting student:', error);
      toast.error("Failed to delete student. Please try again.");
    },
  });

  const updatePaymentStatusMutation = useMutation({
    mutationFn: async () => {
      return await recalculateAllStudentPaymentStatus();
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({ queryKey: ['students'] });
      await refetch();
      toast.success("Payment statuses updated successfully");
    },
    onError: (error) => {
      console.error('Error updating payment statuses:', error);
      toast.error("Failed to update payment statuses. Please try again.");
    },
  });

  const handleDelete = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this student? This will also delete all associated records (payments, exam results, attendance).')) {
      await deleteMutation.mutateAsync(id);
    }
  };

  const handleUpdatePaymentStatuses = async () => {
    if (window.confirm('This will recalculate payment status for all students based on their payment history. Continue?')) {
      setIsUpdatingPayments(true);
      try {
        await updatePaymentStatusMutation.mutateAsync();
      } finally {
        setIsUpdatingPayments(false);
      }
    }
  };

  // Get payment status badge color
  const getPaymentStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'partial':
        return 'bg-blue-100 text-blue-800';
      case 'overdue':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Use type assertion to help TypeScript understand the structure
  const filteredStudents = (students as any[]).filter((student) => {
    // Filter by search term
    const matchesSearch = 
      student.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.student_id?.toLowerCase().includes(searchTerm.toLowerCase());
    
    // Filter by course
    const matchesCourse = 
      selectedCourse === 'all' || 
      student.course_id === selectedCourse;
    
    // Filter by level
    const matchesLevel = 
      selectedLevel === 'all' || 
      student.level_id === selectedLevel;
    
    // Filter by payment status
    const matchesPaymentStatus = 
      selectedPaymentStatus === 'all' || 
      (student.payment_status && student.payment_status.toLowerCase() === selectedPaymentStatus.toLowerCase());
    
    return matchesSearch && matchesCourse && matchesLevel && matchesPaymentStatus;
  });

  // Handle export to Excel
  const handleExport = async () => {
    if (filteredStudents.length === 0) {
      toast.error('No students to export');
      return;
    }

    setIsExporting(true);
    
    try {
      // Get the selected course name (if applicable)
      let courseName = 'all';
      if (selectedCourse !== 'all') {
        const course = courses.find(c => c.id === selectedCourse);
        if (course) {
          courseName = course.name;
        }
      }
      
      // Get the selected level name (if applicable)
      let levelName = '';
      if (selectedLevel !== 'all') {
        const level = levels.find(l => l.id === selectedLevel);
        if (level) {
          levelName = level.name;
          if (courseName === 'all') {
            courseName = `Level-${levelName}`;
          } else {
            courseName = `${courseName}-${levelName}`;
          }
        }
      }
      
      // Call the export function
      await exportStudentsData({
        students: filteredStudents,
        courseName
      });
    } catch (error) {
      console.error('Error exporting students:', error);
      toast.error('Failed to export students data');
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Students</h2>
          <p className="text-muted-foreground">Manage your students</p>
        </div>
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            onClick={handleUpdatePaymentStatuses} 
            disabled={isUpdatingPayments}
            className="gap-2"
            title="Update payment statuses for all students"
          >
            {isUpdatingPayments ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
            Update Payments
          </Button>
          <Button 
            variant="outline" 
            onClick={handleExport} 
            disabled={isExporting || filteredStudents.length === 0}
            className="gap-2"
          >
            {isExporting ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Download className="h-4 w-4" />
            )}
            Export to Excel
          </Button>
          <Button onClick={() => navigate('/dashboard/enrollment/new')}>
            Add New Student
          </Button>
        </div>
      </div>

      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search students..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <Select
          value={selectedCourse}
          onValueChange={(value) => {
            setSelectedCourse(value);
            // Reset level filter when course changes
            if (value !== 'all') {
              setSelectedLevel('all');
            }
          }}
        >
          <SelectTrigger className="w-full sm:w-[180px]">
            <SelectValue placeholder="Filter by course" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Courses</SelectItem>
            {courses.map((course) => (
              <SelectItem key={course.id} value={course.id}>
                {course.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Select
          value={selectedLevel}
          onValueChange={setSelectedLevel}
        >
          <SelectTrigger className="w-full sm:w-[180px]">
            <SelectValue placeholder="Filter by level" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Levels</SelectItem>
            {levels
              .filter(level => selectedCourse === 'all' || level.course_id === selectedCourse)
              .map((level) => (
                <SelectItem key={level.id} value={level.id}>
                  {level.name}
                </SelectItem>
              ))}
          </SelectContent>
        </Select>
        <Select
          value={selectedPaymentStatus}
          onValueChange={setSelectedPaymentStatus}
        >
          <SelectTrigger className="w-full sm:w-[180px]">
            <SelectValue placeholder="Filter by payment" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Payments</SelectItem>
            <SelectItem value="paid">Fully Paid</SelectItem>
            <SelectItem value="partial">Partially Paid</SelectItem>
            <SelectItem value="pending">Pending</SelectItem>
            <SelectItem value="overdue">Overdue</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {isLoading ? (
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      ) : filteredStudents.length === 0 ? (
        <div className="text-center py-8 border rounded-lg">
          <p className="text-muted-foreground">No students found</p>
        </div>
      ) : (
        <div className="border rounded-lg overflow-hidden">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Student ID</TableHead>
                <TableHead>Name</TableHead>
                <TableHead>Course</TableHead>
                <TableHead>Level</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Payment</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredStudents.map((student) => (
                <TableRow key={student.id}>
                  <TableCell className="font-medium">{student.student_id}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {student.passport_picture ? (
                        <img
                          src={student.passport_picture}
                          alt={student.name}
                          className="w-8 h-8 rounded-full object-cover"
                        />
                      ) : (
                        <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center">
                          <UserCircle className="h-6 w-6 text-gray-500" />
                        </div>
                      )}
                      {student.name}
                    </div>
                  </TableCell>
                  <TableCell>
                    {student.course?.name || 'Not assigned'}
                  </TableCell>
                  <TableCell>
                    {student.level?.name || 'Not assigned'}
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant={student.enrollment_status === 'Active' ? 'default' : 'secondary'}
                    >
                      {student.enrollment_status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant="outline"
                      className={getPaymentStatusColor(student.payment_status)}
                    >
                      {student.payment_status || 'Pending'}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => navigate(`/dashboard/enrollment/student/${student.id}`)}
                        title="View Student"
                      >
                        <UserCircle className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => navigate(`/dashboard/enrollment/edit/${student.id}`)}
                        title="Edit Student"
                      >
                        <Pencil className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleDelete(student.id)}
                        title="Delete Student"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}
    </div>
  );
}

export default StudentList;
