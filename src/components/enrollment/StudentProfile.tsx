import { useState } from "react";
import { useParams } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { PersonalInfo } from "./student-profile/PersonalInfo";
import { CourseInfo } from "./student-profile/CourseInfo";
import { PaymentHistory } from "./student-profile/PaymentHistory";
import { AcademicRecords } from "./student-profile/AcademicRecords";
import { AcademicRecord } from "./student-profile/AcademicRecord";
import { LoginManagement } from "./student-profile/LoginManagement";
import { StudentAttendanceDetail } from "./attendance/StudentAttendanceDetail";
import { Loader2, Download } from "lucide-react";
import { Button } from "@/components/ui/button";
import { exportStudentData } from "./utils/exportStudentData";
import { toast } from "sonner";
import { getStudent } from "@/api/students";
import { getStudentAttendance } from "@/api/attendance";
import { getStudentPayments } from "@/api/payments";

export const StudentProfile = () => {
  const { id } = useParams();
  const [isExporting, setIsExporting] = useState(false);

  const { data: student, isLoading: studentLoading } = useQuery({
    queryKey: ['student', id],
    queryFn: () => getStudent(id!),
    enabled: !!id
  });

  const { data: attendanceRecords = [], isLoading: attendanceLoading } = useQuery({
    queryKey: ['student-attendance', id],
    queryFn: async () => {
      console.log('Fetching attendance records for student ID:', id);
      const records = await getStudentAttendance(id!);
      console.log('Fetched attendance records:', records);
      return records;
    },
    enabled: !!id
  });

  const { data: academicRecords = [] } = useQuery({
    queryKey: ['student-academic-records', id],
    queryFn: async () => {
      // TODO: Replace with Firebase implementation
      return [];
    },
    enabled: !!id
  });

  const { data: paymentRecords = [] } = useQuery({
    queryKey: ['student-payments', id],
    queryFn: () => getStudentPayments(id!),
    enabled: !!id
  });

  // Convert Firebase Timestamp to string for compatibility with components
  const adaptedStudent = student ? {
    ...student,
    created_at: student.created_at ? student.created_at.toDate().toISOString() : new Date().toISOString(),
    updated_at: student.updated_at ? student.updated_at.toDate().toISOString() : new Date().toISOString()
  } : null;

  const handleExport = async () => {
    if (!student) return;
    
    // Set loading state
    setIsExporting(true);
    console.log('Starting export process from StudentProfile');
    
    try {
      // Call export function with proper error handling
      console.log('Calling exportStudentData function');
      
      const result = await exportStudentData({
        student,
        attendanceRecords: attendanceRecords || [],
        academicRecords: academicRecords || [],
        paymentRecords: paymentRecords || []
      });
      
      console.log('Export result:', result);
      
      // If export failed but didn't throw an error
      if (!result) {
        console.warn('Export function returned false');
      }
    } catch (error) {
      // Handle any unexpected errors
      console.error('Export error in component:', error);
      toast.error('Export failed due to an unexpected error');
    } finally {
      // Always reset loading state
      console.log('Resetting export loading state');
      setIsExporting(false);
    }
  };

  if (studentLoading) {
    return (
      <div className="flex items-center justify-center h-[50vh]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!student) {
    return (
      <div className="p-6 text-center">
        <h2 className="text-2xl font-bold text-red-600">Student Not Found</h2>
        <p className="text-gray-600 mt-2">The student you are looking for does not exist or has been removed.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Student Profile</h2>
          <p className="text-muted-foreground">View and manage student information</p>
        </div>
        <Button 
          onClick={handleExport} 
          disabled={isExporting}
          className="gap-2"
        >
          {isExporting ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Download className="h-4 w-4" />
          )}
          Export Complete Record
        </Button>
      </div>

      <Tabs defaultValue="personal" className="space-y-4">
        <TabsList>
          <TabsTrigger value="personal">Personal Information</TabsTrigger>
          <TabsTrigger value="course">Course Information</TabsTrigger>
          <TabsTrigger value="attendance">Attendance Records</TabsTrigger>
          <TabsTrigger value="payments">Payment History</TabsTrigger>
          <TabsTrigger value="academic">Academic Records</TabsTrigger>
          <TabsTrigger value="login">Login Management</TabsTrigger>
        </TabsList>

        <TabsContent value="personal">
          <PersonalInfo student={adaptedStudent as any} />
        </TabsContent>

        <TabsContent value="course">
          <CourseInfo studentId={student.id} />
        </TabsContent>

        <TabsContent value="attendance">
          {attendanceLoading ? (
            <div className="flex items-center justify-center h-[200px]">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : (
            <StudentAttendanceDetail
              student={adaptedStudent as any}
              attendanceRecords={attendanceRecords as any}
              onClose={() => {}}
            />
          )}
        </TabsContent>

        <TabsContent value="payments">
          <PaymentHistory studentId={student.id} />
        </TabsContent>

        <TabsContent value="academic">
          <div className="space-y-6">
            <AcademicRecord studentId={student.id} />
            <AcademicRecords studentId={student.id} />
          </div>
        </TabsContent>

        <TabsContent value="login">
          <LoginManagement student={adaptedStudent as any} />
        </TabsContent>
      </Tabs>
    </div>
  );
};
