
import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { PersonalInformationForm } from './components/PersonalInformationForm';
import { ContactInformationForm } from './components/ContactInformationForm';
import { StudentImageUpload } from './components/StudentImageUpload';
import { getStudent, updateStudent } from '@/api/students';
import { supabase } from '@/integrations/supabase/client';
import type { Student } from '@/types/student';

export const EditStudent = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [studentDetails, setStudentDetails] = useState<Partial<Student>>({});

  // Fetch courses
  const { data: courses = [] } = useQuery({
    queryKey: ['courses'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('courses')
        .select('*')
        .order('name');
      
      if (error) throw error;
      return data;
    }
  });

  // Fetch levels
  const { data: levels = [] } = useQuery({
    queryKey: ['levels'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('levels')
        .select('*')
        .order('name');
      
      if (error) throw error;
      return data;
    }
  });

  // Fetch student data
  const { data: student, isLoading } = useQuery({
    queryKey: ['student', id],
    queryFn: () => getStudent(id!),
    enabled: !!id
  });

  // Update student mutation
  const updateMutation = useMutation({
    mutationFn: async (data: Partial<Student>) => {
      if (!id) throw new Error('No student ID provided');
      return updateStudent(id, data);
    },
    onSuccess: () => {
      toast.success('Student updated successfully');
      queryClient.invalidateQueries({ queryKey: ['students'] });
      queryClient.invalidateQueries({ queryKey: ['student', id] });
      navigate('/dashboard/enrollment');
    },
    onError: (error) => {
      console.error('Error updating student:', error);
      toast.error('Failed to update student');
    }
  });

  useEffect(() => {
    if (student) {
      setStudentDetails(student);
      if (student.passport_picture) {
        setImagePreview(student.passport_picture);
      }
    }
  }, [student]);

  const handleStudentDetailsChange = (field: string, value: any) => {
    setStudentDetails(prev => ({
      ...prev,
      [field]: value,
      updated_at: new Date().toISOString()
    }));
  };

  const handleImageChange = (url: string) => {
    handleStudentDetailsChange('passport_picture', url);
    setImagePreview(url);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    updateMutation.mutate(studentDetails);
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  const filteredLevels = levels.filter(
    level => !studentDetails.course_id || level.course_id === studentDetails.course_id
  );

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4 mb-4">
        <Button
          variant="outline"
          size="icon"
          onClick={() => navigate("/dashboard/enrollment")}
          className="h-8 w-8"
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div className="flex-1">
          <h2 className="text-2xl font-bold">Edit Student</h2>
          <p className="text-muted-foreground">Update student information</p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <Card className="p-6">
          <div className="space-y-6">
            <StudentImageUpload
              imagePreview={imagePreview}
              onImageChange={handleImageChange}
            />
            <PersonalInformationForm
              studentDetails={studentDetails}
              onStudentDetailsChange={handleStudentDetailsChange}
              courses={courses}
              filteredLevels={filteredLevels}
            />
            <ContactInformationForm
              studentDetails={studentDetails}
              onStudentDetailsChange={handleStudentDetailsChange}
            />
            <div className="flex justify-end space-x-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => navigate('/dashboard/enrollment')}
              >
                Cancel
              </Button>
              <Button 
                type="submit" 
                disabled={updateMutation.isPending || Object.keys(studentDetails).length === 0}
              >
                {updateMutation.isPending ? 'Saving...' : 'Save Changes'}
              </Button>
            </div>
          </div>
        </Card>
      </form>
    </div>
  );
};
