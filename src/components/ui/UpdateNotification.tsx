import { useState, useEffect } from 'react';
import { Button } from './button';
import { toast } from 'sonner';

interface UpdateNotificationProps {
  registration: ServiceWorkerRegistration;
}

export function UpdateNotification({ registration }: UpdateNotificationProps) {
  const [waitingWorker, setWaitingWorker] = useState<ServiceWorker | null>(null);
  const [showReload, setShowReload] = useState(false);

  useEffect(() => {
    // Add an event listener to detect when a new service worker is waiting
    const onUpdate = () => {
      const waitingServiceWorker = registration.waiting;
      if (waitingServiceWorker) {
        setWaitingWorker(waitingServiceWorker);
        setShowReload(true);
      }
    };

    registration.addEventListener('updatefound', onUpdate);
    onUpdate();

    return () => {
      registration.removeEventListener('updatefound', onUpdate);
    };
  }, [registration]);

  const reloadPage = () => {
    if (waitingWorker) {
      // Send a message to the service worker to skip waiting
      waitingWorker.postMessage({ type: 'SKIP_WAITING' });
      waitingWorker.addEventListener('statechange', (e) => {
        if ((e.target as ServiceWorker).state === 'activated') {
          window.location.reload();
        }
      });
    }
  };

  useEffect(() => {
    if (showReload) {
      toast.message(
        "New version available!",
        {
          description: "A new version of the app is available.",
          action: {
            label: "Update",
            onClick: reloadPage
          },
          duration: Infinity
        }
      );
    }
  }, [showReload]);

  return null;
} 