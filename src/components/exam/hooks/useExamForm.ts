import { useForm } from 'react-hook-form';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { ExamFormData } from '../types';
import { useEffect } from 'react';
import { getCourses } from '@/api/courses';
import { getLevels } from '@/api/levels';
import { createExam } from '@/api/exams';

export const useExamForm = (onSuccess?: () => void) => {
  const queryClient = useQueryClient();
  const form = useForm<ExamFormData>({
    defaultValues: {
      subject: '',
      class: '',
      name: '',
      type: '',
      course_id: '',
      level_id: '',
      date: '',
      time: ''
    }
  });

  const { data: courses, isLoading: isLoadingCourses } = useQuery({
    queryKey: ['courses'],
    queryFn: async () => {
      console.log('Fetching courses for exam form');
      try {
        return await getCourses();
      } catch (error) {
        console.error('Error fetching courses:', error);
        throw error;
      }
    }
  });

  const { data: levels, isLoading: isLoadingLevels } = useQuery({
    queryKey: ['levels', form.watch('course_id')],
    queryFn: async () => {
      console.log('Fetching levels for course:', form.watch('course_id'));
      try {
        if (!form.watch('course_id')) return [];
        return await levelModel.getLevelsByCourseId(form.watch('course_id'));
      } catch (error) {
        console.error('Error fetching levels:', error);
        throw error;
      }
    },
    enabled: !!form.watch('course_id')
  });

  const createExamMutation = useMutation({
    mutationFn: async (data: ExamFormData) => {
      console.log('Creating exam with data:', data);
      try {
        return await createExam(data);
      } catch (error) {
        console.error('Error creating exam:', error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['exams'] });
      toast.success('Exam created successfully');
      form.reset();
      onSuccess?.();
    },
    onError: (error: any) => {
      console.error('Error creating exam:', error);
      toast.error(error.message || 'Failed to create exam');
    },
  });

  // Reset level when course changes
  useEffect(() => {
    form.setValue('level_id', '');
  }, [form.watch('course_id')]);

  return {
    form,
    courses,
    levels,
    isLoadingCourses,
    isLoadingLevels,
    createExam: createExamMutation
  };
};
