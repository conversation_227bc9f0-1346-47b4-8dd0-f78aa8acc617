import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { PlusCircle, Users, Trash2, Calendar, ClockIcon } from 'lucide-react';
// Exam type - to be defined based on PHP backend structure
interface Exam {
  id: string;
  title: string;
  description?: string;
  date: string;
  duration: string;
  total_marks: number;
  course_id?: string;
  level_id?: string;
}
import { useState } from 'react';
import { AddExamResult } from './AddExamResult';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { getCourses } from '@/api/courses';
import { getLevels } from '@/api/levels';
import { getStudentsForExam, deleteExam } from '@/api/exams';
import { toast } from 'sonner';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface ExamListProps {
  exams: Exam[];
}

export const ExamList = ({ exams }: ExamListProps) => {
  const [selectedExam, setSelectedExam] = useState<Exam | null>(null);
  const [showStudents, setShowStudents] = useState<string | null>(null);
  const [examToDelete, setExamToDelete] = useState<Exam | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  
  const queryClient = useQueryClient();

  // Check if we're showing a specific section or all exams
  const isShowingAllExams = exams.some(exam => new Date(exam.date) > new Date()) && 
                             exams.some(exam => new Date(exam.date) <= new Date());

  // Separate exams into upcoming and past exams
  const upcomingExams = exams.filter(exam => new Date(exam.date) > new Date());
  const pastExams = exams.filter(exam => new Date(exam.date) <= new Date());

  const { data: coursesData = [] } = useQuery({
    queryKey: ['courses'],
    queryFn: getCourses
  });

  const { data: levelsData = [] } = useQuery({
    queryKey: ['levels'],
    queryFn: getLevels
  });

  // Convert arrays to maps for easier lookup
  const coursesMap = Object.fromEntries(
    coursesData.map(course => [course.id, course.name])
  );
  
  const levelsMap = Object.fromEntries(
    levelsData.map(level => [level.id, level.name])
  );

  const { data: studentsWithResults = [], isLoading: isLoadingStudents } = useQuery({
    queryKey: ['exam-students', showStudents],
    enabled: !!showStudents,
    queryFn: async () => {
      if (!showStudents) return [];
      
      try {
        return await getStudentsForExam(showStudents);
      } catch (error) {
        console.error('Error fetching students for exam:', error);
        return [];
      }
    }
  });

  const handleDeleteExam = async () => {
    if (!examToDelete) return;
    
    try {
      setIsDeleting(true);
      await deleteExam(examToDelete.id);
      
      // Refresh the exams list
      queryClient.invalidateQueries({ queryKey: ['exams'] });
      
      toast.success('Exam deleted successfully');
      setShowDeleteConfirm(false);
    } catch (error) {
      console.error('Error deleting exam:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to delete exam');
    } finally {
      setIsDeleting(false);
      setExamToDelete(null);
    }
  };

  // Render an exam card
  const renderExamCard = (exam: Exam) => (
    <Card key={exam.id} className={new Date(exam.date) <= new Date() ? "border-l-4 border-l-amber-500" : ""}>
      <CardHeader>
        <div className="flex justify-between items-start">
          <CardTitle className="text-lg">{exam.name}</CardTitle>
          <Button 
            variant="ghost" 
            size="sm"
            className="text-red-500 hover:text-red-700 hover:bg-red-50 -mt-1 h-8 w-8 p-0"
            onClick={() => {
              setExamToDelete(exam);
              setShowDeleteConfirm(true);
            }}
          >
            <Trash2 className="h-4 w-4" />
            <span className="sr-only">Delete</span>
          </Button>
        </div>
        <div className="text-sm text-muted-foreground">
          Course: {exam.course_id && coursesMap[exam.course_id] ? coursesMap[exam.course_id] : 'Not assigned'}
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className="text-sm">
            <span className="font-medium">Level:</span> {exam.level_id && levelsMap[exam.level_id] ? levelsMap[exam.level_id] : 'Not assigned'}
          </div>
          <div className="text-sm">
            <span className="font-medium">Type:</span> {exam.type}
          </div>
          <div className="text-sm flex items-center">
            <Calendar className="h-3.5 w-3.5 mr-1 text-muted-foreground" /> 
            <span className="font-medium mr-1">Date:</span> {new Date(exam.date).toLocaleDateString()}
          </div>
          <div className="text-sm flex items-center">
            <ClockIcon className="h-3.5 w-3.5 mr-1 text-muted-foreground" />
            <span className="font-medium mr-1">Time:</span> {exam.time}
          </div>
          <div className="text-sm">
            <span className="font-medium">Status:</span> {exam.status}
          </div>
          <div className="flex gap-2 mt-4">
            <Dialog>
              <DialogTrigger asChild>
                <Button 
                  variant="outline" 
                  className="flex-1"
                  onClick={() => setSelectedExam(exam)}
                >
                  <PlusCircle className="h-4 w-4 mr-2" />
                  Add Results
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Add Exam Results - {exam.name}</DialogTitle>
                </DialogHeader>
                {selectedExam && (
                  <AddExamResult examId={selectedExam.id} />
                )}
              </DialogContent>
            </Dialog>
            <Dialog>
              <DialogTrigger asChild>
                <Button 
                  variant="outline"
                  className="flex-1"
                  onClick={() => setShowStudents(exam.id)}
                >
                  <Users className="h-4 w-4 mr-2" />
                  View Students
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-3xl">
                <DialogHeader>
                  <DialogTitle>Students - {exam.name}</DialogTitle>
                </DialogHeader>
                <div className="mt-4">
                  {isLoadingStudents ? (
                    <div className="text-center py-4">Loading students...</div>
                  ) : studentsWithResults.length === 0 ? (
                    <div className="text-center py-4 text-muted-foreground">
                      No students found for this course and level
                    </div>
                  ) : (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Student ID</TableHead>
                          <TableHead>Name</TableHead>
                          <TableHead>Gender</TableHead>
                          <TableHead>Registration Date</TableHead>
                          <TableHead>Marks</TableHead>
                          <TableHead>Grade</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {studentsWithResults.map((student) => (
                          <TableRow key={student.id}>
                            <TableCell>{student.student_id}</TableCell>
                            <TableCell>{student.name}</TableCell>
                            <TableCell>{student.gender}</TableCell>
                            <TableCell>
                              {new Date(student.date_of_registration).toLocaleDateString()}
                            </TableCell>
                            <TableCell>
                              {student.examResult?.marks ?? '-'}
                            </TableCell>
                            <TableCell>
                              {student.examResult?.grade ?? '-'}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  )}
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-8">
      {/* Only show the section headers when we're displaying all exams */}
      {isShowingAllExams ? (
        <>
          {/* Upcoming Exams Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center">
              <Calendar className="h-5 w-5 mr-2 text-green-600" />
              Upcoming Exams
            </h3>
            {upcomingExams.length === 0 ? (
              <div className="text-center py-8 border rounded-md bg-gray-50">
                <p className="text-muted-foreground">No upcoming exams scheduled</p>
              </div>
            ) : (
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {upcomingExams.map(renderExamCard)}
              </div>
            )}
          </div>

          {/* Past Exams Section */}
          <div className="space-y-4 pt-4 border-t">
            <h3 className="text-lg font-semibold flex items-center">
              <ClockIcon className="h-5 w-5 mr-2 text-amber-600" />
              Past Exams
            </h3>
            {pastExams.length === 0 ? (
              <div className="text-center py-8 border rounded-md bg-gray-50">
                <p className="text-muted-foreground">No past exams found</p>
              </div>
            ) : (
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {pastExams.map(renderExamCard)}
              </div>
            )}
          </div>
        </>
      ) : (
        // If showing a specific section, just render the cards without headers
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {exams.map(renderExamCard)}
        </div>
      )}

      {/* Delete confirmation dialog */}
      <Dialog open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Delete</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{examToDelete?.name}"? This action cannot be undone.
              <br />
              <span className="text-red-500 font-medium">
                Note: Exams with existing results cannot be deleted.
              </span>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setShowDeleteConfirm(false)}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleDeleteExam} 
              disabled={isDeleting}
            >
              {isDeleting ? 'Deleting...' : 'Delete Exam'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};
