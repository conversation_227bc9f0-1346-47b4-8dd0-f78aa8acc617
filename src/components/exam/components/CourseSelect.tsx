
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { UseFormReturn } from 'react-hook-form';
import { ExamFormData } from '../types';

interface CourseSelectProps {
  form: UseFormReturn<ExamFormData>;
  courses: any[];
  isLoading: boolean;
}

export const CourseSelect = ({ form, courses, isLoading }: CourseSelectProps) => {
  return (
    <FormField
      control={form.control}
      name="course_id"
      render={({ field }) => (
        <FormItem>
          <FormLabel>Course</FormLabel>
          <Select
            onValueChange={field.onChange}
            defaultValue={field.value}
          >
            <FormControl>
              <SelectTrigger>
                <SelectValue placeholder="Select a course" />
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              {isLoading ? (
                <SelectItem value="loading" disabled>
                  Loading courses...
                </SelectItem>
              ) : (
                courses?.map((course) => (
                  <SelectItem key={course.id} value={course.id}>
                    {course.name}
                  </SelectItem>
                ))
              )}
            </SelectContent>
          </Select>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};
