
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { UseFormReturn } from 'react-hook-form';
import { ExamFormData } from '../types';

interface LevelSelectProps {
  form: UseFormReturn<ExamFormData>;
  levels: any[];
  isLoading: boolean;
}

export const LevelSelect = ({ form, levels, isLoading }: LevelSelectProps) => {
  return (
    <FormField
      control={form.control}
      name="level_id"
      render={({ field }) => (
        <FormItem>
          <FormLabel>Level</FormLabel>
          <Select
            onValueChange={field.onChange}
            defaultValue={field.value}
            disabled={!form.watch('course_id')}
          >
            <FormControl>
              <SelectTrigger>
                <SelectValue placeholder="Select a level" />
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              {!form.watch('course_id') ? (
                <SelectItem value="select-course" disabled>
                  Select a course first
                </SelectItem>
              ) : isLoading ? (
                <SelectItem value="loading" disabled>
                  Loading levels...
                </SelectItem>
              ) : (
                levels?.map((level) => (
                  <SelectItem key={level.id} value={level.id}>
                    {level.name}
                  </SelectItem>
                ))
              )}
            </SelectContent>
          </Select>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};
