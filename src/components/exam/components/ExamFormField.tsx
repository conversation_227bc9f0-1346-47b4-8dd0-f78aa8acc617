
import { Input } from '@/components/ui/input';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { UseFormReturn } from 'react-hook-form';
import { ExamFormData } from '../types';

interface ExamFormFieldProps {
  form: UseFormReturn<ExamFormData>;
  name: keyof ExamFormData;
  label: string;
  placeholder?: string;
  type?: string;
  required?: boolean;
}

export const ExamFormField = ({
  form,
  name,
  label,
  placeholder,
  type = 'text',
  required = true
}: ExamFormFieldProps) => {
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            <Input {...field} type={type} placeholder={placeholder} required={required} />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};
