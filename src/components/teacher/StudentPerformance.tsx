import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { Loader2, BarChart2, TrendingUp, TrendingDown, Users, Target } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { getLevels } from '@/api/levels';
import { getPerformanceByLevel, PerformanceData } from '@/api/performance';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

const StudentPerformance = () => {
  const { userProfile } = useAuth();
  const { toast } = useToast();
  const [selectedLevel, setSelectedLevel] = useState<string>('');

  // Fetch levels data
  const { data: levels = [], isLoading: isLoadingLevels } = useQuery({
    queryKey: ['levels'],
    queryFn: getLevels
  });

  // Filter levels to only show those assigned to the teacher
  const assignedLevels = levels.filter(level => 
    userProfile?.assigned_levels?.includes(level.id)
  );

  // Set default selected level if none is selected
  if (assignedLevels.length > 0 && !selectedLevel) {
    setSelectedLevel(assignedLevels[0].id);
  }

  // Fetch performance data for the selected level
  const { 
    data: performanceData, 
    isLoading: isLoadingPerformance 
  } = useQuery<PerformanceData>({
    queryKey: ['performance', selectedLevel],
    queryFn: () => selectedLevel ? getPerformanceByLevel(selectedLevel) : Promise.resolve({
      averageScore: 0,
      highestScore: 0,
      lowestScore: 0,
      passRate: 0,
      improvementRate: 0,
      students: []
    }),
    enabled: !!selectedLevel,
  });

  const isLoading = isLoadingLevels || isLoadingPerformance;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Student Performance</h2>
          <p className="text-muted-foreground">
            Monitor and analyze student progress
          </p>
        </div>
        <Select
          value={selectedLevel}
          onValueChange={setSelectedLevel}
        >
          <SelectTrigger className="w-[200px]">
            <SelectValue placeholder="Select a level" />
          </SelectTrigger>
          <SelectContent>
            {assignedLevels.map(level => (
              <SelectItem key={level.id} value={level.id}>
                {level.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {!performanceData ? (
        <div className="flex flex-col items-center justify-center h-64 text-muted-foreground">
          <BarChart2 className="h-12 w-12 mb-4" />
          <p>No performance data available</p>
          <p className="text-sm">Select a level to view performance metrics</p>
        </div>
      ) : (
        <>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Average Score</CardTitle>
                <BarChart2 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{performanceData.averageScore}%</div>
                <p className="text-xs text-muted-foreground">
                  Class average performance
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Pass Rate</CardTitle>
                <Target className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{performanceData.passRate}%</div>
                <p className="text-xs text-muted-foreground">
                  Students meeting targets
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Highest Score</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{performanceData.highestScore}%</div>
                <p className="text-xs text-muted-foreground">
                  Top performing student
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Improvement</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">+{performanceData.improvementRate}%</div>
                <p className="text-xs text-muted-foreground">
                  Average improvement rate
                </p>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Student Progress</CardTitle>
              <CardDescription>
                Detailed performance metrics for each student
              </CardDescription>
            </CardHeader>
            <CardContent>
              {performanceData.students.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  No student data available for this level
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Student Name</TableHead>
                      <TableHead>Average Score</TableHead>
                      <TableHead>Attendance</TableHead>
                      <TableHead>Assignments</TableHead>
                      <TableHead>Exams</TableHead>
                      <TableHead>Trend</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {performanceData.students.map((student) => (
                      <TableRow key={student.id}>
                        <TableCell className="font-medium">{student.name}</TableCell>
                        <TableCell>{student.averageScore}%</TableCell>
                        <TableCell>{student.attendance}%</TableCell>
                        <TableCell>{student.assignments}%</TableCell>
                        <TableCell>{student.exams}%</TableCell>
                        <TableCell>
                          {student.trend === 'up' ? (
                            <TrendingUp className="h-4 w-4 text-green-500" />
                          ) : (
                            <TrendingDown className="h-4 w-4 text-red-500" />
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Performance Distribution</CardTitle>
              <CardDescription>
                Score distribution across different assessment types
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[200px] flex items-center justify-center text-muted-foreground">
                Chart placeholder - Will implement with actual chart library
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
};

export default StudentPerformance; 