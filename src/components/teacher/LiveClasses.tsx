import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Calendar,
  Clock,
  Edit,
  ExternalLink,
  Loader2,
  MoreVertical,
  Plus,
  Trash,
  Video,
} from 'lucide-react';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { format } from 'date-fns';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Label } from '@/components/ui/label';
import { createLiveClass, deleteLiveClass, getLiveClasses, updateLiveClass } from '@/api/live-classes';
// Live class types - to be defined based on PHP backend structure
interface LiveClass {
  id: string;
  title: string;
  description?: string;
  date: string;
  time: string;
  status: string;
}
import { useToast } from '@/components/ui/use-toast';

const formSchema = z.object({
  title: z.string().min(3, "Title must be at least 3 characters"),
  description: z.string().min(5, "Description must be at least 5 characters"),
  meetingLink: z.string().url("Please enter a valid URL"),
  startTime: z.date({
    required_error: "Start time is required",
  }),
  endTime: z.date({
    required_error: "End time is required",
  }),
  courseId: z.string().optional(),
  levelId: z.string().optional(),
  isActive: z.boolean().default(true),
}).refine(data => data.endTime > data.startTime, {
  message: "End time must be after start time",
  path: ["endTime"],
});

export function LiveClasses() {
  const [liveClasses, setLiveClasses] = useState<LiveClass[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [openDialog, setOpenDialog] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [editingClass, setEditingClass] = useState<LiveClass | null>(null);
  const { toast } = useToast();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: "",
      description: "",
      meetingLink: "",
      isActive: true,
      courseId: "",
      levelId: "",
    },
  });

  const fetchLiveClasses = async () => {
    setIsLoading(true);
    try {
      const classes = await getLiveClasses();
      setLiveClasses(classes);
    } catch (error) {
      console.error("Error fetching live classes:", error);
      toast({
        variant: "destructive",
        title: "Failed to load live classes",
        description: "Please try again later",
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchLiveClasses();
  }, []);

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    setIsSubmitting(true);
    try {
      if (editingClass) {
        await updateLiveClass(editingClass.id, values);
        toast({
          title: "Live class updated",
          description: "The live class has been updated successfully.",
        });
      } else {
        await createLiveClass(values);
        toast({
          title: "Live class created",
          description: "The live class has been created successfully.",
        });
      }
      setOpenDialog(false);
      form.reset();
      setEditingClass(null);
      fetchLiveClasses();
    } catch (error) {
      console.error("Error saving live class:", error);
      toast({
        variant: "destructive",
        title: "Failed to save live class",
        description: "Please try again later",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async (id: string) => {
    try {
      await deleteLiveClass(id);
      toast({
        title: "Live class deleted",
        description: "The live class has been deleted successfully.",
      });
      fetchLiveClasses();
    } catch (error) {
      console.error("Error deleting live class:", error);
      toast({
        variant: "destructive",
        title: "Failed to delete live class",
        description: "Please try again later",
      });
    }
  };

  const handleEdit = (liveClass: LiveClass) => {
    setEditingClass(liveClass);
    form.reset({
      title: liveClass.title,
      description: liveClass.description,
      meetingLink: liveClass.meetingLink,
      startTime: liveClass.startTime,
      endTime: liveClass.endTime,
      courseId: liveClass.courseId || "",
      levelId: liveClass.levelId || "",
      isActive: liveClass.isActive,
    });
    setOpenDialog(true);
  };

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      form.reset();
      setEditingClass(null);
    }
    setOpenDialog(open);
  };

  // Format date for display
  const formatDate = (date: Date) => {
    return format(date, "PPP");
  };

  // Format time for display
  const formatTime = (date: Date) => {
    return format(date, "h:mm a");
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-3xl font-bold tracking-tight">Live Classes</h2>
        <Dialog open={openDialog} onOpenChange={handleOpenChange}>
          <DialogTrigger asChild>
            <Button className="ml-auto">
              <Plus className="mr-2 h-4 w-4" />
              Create Live Class
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[625px]">
            <DialogHeader>
              <DialogTitle>{editingClass ? "Edit Live Class" : "Create Live Class"}</DialogTitle>
              <DialogDescription>
                {editingClass 
                  ? "Update the details of your live class session." 
                  : "Fill in the details to create a new live class session."}
              </DialogDescription>
            </DialogHeader>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Title</FormLabel>
                      <FormControl>
                        <Input placeholder="Algebra Fundamentals" {...field} />
                      </FormControl>
                      <FormDescription>
                        Enter a descriptive title for your live class.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="In this session, we will cover the fundamental concepts of algebra..."
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="meetingLink"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Zoom Meeting Link</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="https://zoom.us/j/1234567890"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Paste your Zoom meeting link here. Students will use this to join the live class.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="startTime"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>Start Time</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant="outline"
                                className="w-full justify-start text-left font-normal"
                              >
                                {field.value ? (
                                  <span>
                                    {format(field.value, "PPP")} at{" "}
                                    {format(field.value, "h:mm a")}
                                  </span>
                                ) : (
                                  <span>Select date and time</span>
                                )}
                                <Calendar className="ml-auto h-4 w-4 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <CalendarComponent
                              mode="single"
                              selected={field.value}
                              onSelect={(date) => {
                                if (date) {
                                  const currentDate = field.value || new Date();
                                  date.setHours(currentDate.getHours());
                                  date.setMinutes(currentDate.getMinutes());
                                  field.onChange(date);
                                }
                              }}
                              initialFocus
                            />
                            <div className="p-3 border-t border-border">
                              <Label>Time</Label>
                              <div className="flex mt-2">
                                <Input
                                  type="time"
                                  value={field.value ? format(field.value, "HH:mm") : ""}
                                  onChange={(e) => {
                                    const [hours, minutes] = e.target.value.split(':').map(Number);
                                    const date = field.value || new Date();
                                    date.setHours(hours);
                                    date.setMinutes(minutes);
                                    field.onChange(new Date(date));
                                  }}
                                />
                              </div>
                            </div>
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="endTime"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>End Time</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant="outline"
                                className="w-full justify-start text-left font-normal"
                              >
                                {field.value ? (
                                  <span>
                                    {format(field.value, "PPP")} at{" "}
                                    {format(field.value, "h:mm a")}
                                  </span>
                                ) : (
                                  <span>Select date and time</span>
                                )}
                                <Calendar className="ml-auto h-4 w-4 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <CalendarComponent
                              mode="single"
                              selected={field.value}
                              onSelect={(date) => {
                                if (date) {
                                  const currentDate = field.value || new Date();
                                  date.setHours(currentDate.getHours());
                                  date.setMinutes(currentDate.getMinutes());
                                  field.onChange(date);
                                }
                              }}
                              initialFocus
                            />
                            <div className="p-3 border-t border-border">
                              <Label>Time</Label>
                              <div className="flex mt-2">
                                <Input
                                  type="time"
                                  value={field.value ? format(field.value, "HH:mm") : ""}
                                  onChange={(e) => {
                                    const [hours, minutes] = e.target.value.split(':').map(Number);
                                    const date = field.value || new Date();
                                    date.setHours(hours);
                                    date.setMinutes(minutes);
                                    field.onChange(new Date(date));
                                  }}
                                />
                              </div>
                            </div>
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="courseId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Course (Optional)</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          value={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select a course" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="">None</SelectItem>
                            {/* Add your courses here */}
                            <SelectItem value="course1">Mathematics</SelectItem>
                            <SelectItem value="course2">Science</SelectItem>
                            <SelectItem value="course3">English</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="levelId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Level (Optional)</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          value={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select a level" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="">None</SelectItem>
                            {/* Add your levels here */}
                            <SelectItem value="level1">Beginner</SelectItem>
                            <SelectItem value="level2">Intermediate</SelectItem>
                            <SelectItem value="level3">Advanced</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <FormField
                  control={form.control}
                  name="isActive"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                      <FormControl>
                        <input
                          type="checkbox"
                          className="h-4 w-4 rounded border-gray-300 text-green-600 focus:ring-green-500"
                          checked={field.value}
                          onChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Active</FormLabel>
                        <FormDescription>
                          When active, students can see and join this live class.
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />
                <DialogFooter>
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    {editingClass ? "Update Live Class" : "Create Live Class"}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>Manage Live Classes</CardTitle>
          <CardDescription>
            Create, edit, and manage your live class sessions. Students will be able to join the live
            classes through the provided Zoom links.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          ) : liveClasses.length === 0 ? (
            <div className="text-center py-12">
              <Video className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-4 text-lg font-semibold">No live classes</h3>
              <p className="mt-2 text-sm text-muted-foreground">
                You haven't created any live classes yet. Click the button above to create one.
              </p>
            </div>
          ) : (
            <Table>
              <TableCaption>A list of your live classes.</TableCaption>
              <TableHeader>
                <TableRow>
                  <TableHead>Title</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Time</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="w-[100px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {liveClasses.map((liveClass) => (
                  <TableRow key={liveClass.id}>
                    <TableCell className="font-medium">{liveClass.title}</TableCell>
                    <TableCell>{formatDate(liveClass.startTime)}</TableCell>
                    <TableCell>
                      {formatTime(liveClass.startTime)} - {formatTime(liveClass.endTime)}
                    </TableCell>
                    <TableCell>
                      <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          liveClass.isActive
                            ? "bg-green-100 text-green-800"
                            : "bg-gray-100 text-gray-800"
                        }`}
                      >
                        {liveClass.isActive ? "Active" : "Inactive"}
                      </span>
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleEdit(liveClass)}
                        >
                          <Edit className="h-4 w-4" />
                          <span className="sr-only">Edit</span>
                        </Button>
                        <a
                          href={liveClass.meetingLink}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center justify-center h-9 w-9 rounded-md text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground"
                        >
                          <ExternalLink className="h-4 w-4" />
                          <span className="sr-only">Open link</span>
                        </a>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <Trash className="h-4 w-4 text-red-500" />
                              <span className="sr-only">Delete</span>
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                              <AlertDialogDescription>
                                This will permanently delete the live class. This action cannot be undone.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() => handleDelete(liveClass.id)}
                                className="bg-red-500 hover:bg-red-600"
                              >
                                Delete
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
        <CardFooter className="bg-muted/50 p-6 mt-4">
          <div className="flex items-center space-x-2 text-sm text-muted-foreground">
            <Clock className="h-4 w-4" />
            <span>
              Students will be able to join active live classes at the scheduled time.
            </span>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
} 