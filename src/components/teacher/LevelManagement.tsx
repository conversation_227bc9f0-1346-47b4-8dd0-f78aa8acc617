import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/components/ui/use-toast";
import { Loader2, Search, Users, BookOpen, GraduationCap, Eye, Clock, CalendarDays, BookOpenCheck } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { getLevels } from '@/api/levels';
import { getCourses } from '@/api/courses';
import { getSubjects } from '@/api/subjects';
import { getStudentsByLevel } from '@/api/students';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";

const LevelManagement = () => {
  const { userProfile } = useAuth();
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedLevel, setSelectedLevel] = useState<any>(null);
  const [isLevelDialogOpen, setIsLevelDialogOpen] = useState(false);
  const [isStudentDialogOpen, setIsStudentDialogOpen] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState<any>(null);

  // Fetch levels data
  const { data: levels = [], isLoading: isLoadingLevels } = useQuery({
    queryKey: ['levels'],
    queryFn: getLevels
  });

  // Fetch courses data
  const { data: courses = [], isLoading: isLoadingCourses } = useQuery({
    queryKey: ['courses'],
    queryFn: getCourses
  });

  // Fetch subjects data
  const { data: subjects = [], isLoading: isLoadingSubjects } = useQuery({
    queryKey: ['subjects'],
    queryFn: getSubjects
  });

  // Fetch students for selected level
  const { data: levelStudents = [], isLoading: isLoadingStudents } = useQuery({
    queryKey: ['students', selectedLevel?.id],
    queryFn: () => selectedLevel?.id ? getStudentsByLevel(selectedLevel.id) : Promise.resolve([]),
    enabled: !!selectedLevel?.id
  });

  // Filter levels to only show those assigned to the teacher
  const assignedLevels = levels.filter(level => 
    userProfile?.assigned_levels?.includes(level.id)
  );

  // Filter levels based on search term
  const filteredLevels = assignedLevels.filter(level => 
    level.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleViewLevel = (level: any) => {
    setSelectedLevel({
      ...level,
      students: [] // Initialize empty students array
    });
    setIsLevelDialogOpen(true);
  };

  const handleViewStudent = (student: any) => {
    setSelectedStudent(student);
    setIsStudentDialogOpen(true);
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  // Get course details for a level
  const getCourseDetails = (courseId: string) => {
    return courses.find(course => course.id === courseId);
  };

  // Get subjects for a course
  const getCourseSubjects = (courseId: string) => {
    return subjects.filter(subject => subject.course_id === courseId);
  };

  const isLoading = isLoadingLevels || isLoadingCourses || isLoadingSubjects || isLoadingStudents;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Level Management</h2>
          <p className="text-muted-foreground">
            View and manage your assigned levels and students
          </p>
        </div>
        <div className="relative w-[250px]">
          <Search className="absolute left-2 top-3 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search levels..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      {assignedLevels.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center h-64 text-center">
            <GraduationCap className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium">No Levels Assigned</h3>
            <p className="text-sm text-muted-foreground mt-2">
              You don't have any levels assigned to you yet. Please contact an administrator.
            </p>
          </CardContent>
        </Card>
      ) : (
        <>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {filteredLevels.map((level) => {
              const courseDetails = getCourseDetails(level.course_id);
              const courseSubjects = getCourseSubjects(level.course_id);
              
              return (
                <Card key={level.id} className="overflow-hidden">
                  <CardHeader className="pb-3">
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle>{level.name}</CardTitle>
                        <CardDescription>
                          {courseDetails?.name || 'No course assigned'}
                        </CardDescription>
                      </div>
                      <Badge variant="outline" className="ml-2">
                        {level.code || 'No Code'}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-center">
                        <Users className="h-4 w-4 mr-2 text-muted-foreground" />
                        <span className="text-sm">
                          {level.students?.length || 0} Students
                        </span>
                      </div>
                      <div className="flex items-center">
                        <BookOpen className="h-4 w-4 mr-2 text-muted-foreground" />
                        <span className="text-sm">
                          {courseSubjects.length} Subjects
                        </span>
                      </div>
                      {courseDetails && (
                        <>
                          <div className="flex items-center">
                            <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
                            <span className="text-sm">
                              Duration: {courseDetails.duration || 'Not specified'}
                            </span>
                          </div>
                          <div className="flex items-center">
                            <CalendarDays className="h-4 w-4 mr-2 text-muted-foreground" />
                            <span className="text-sm">
                              {courseDetails.status === 'active' ? 'Active' : 'Inactive'}
                            </span>
                          </div>
                        </>
                      )}
                      <div className="mt-4">
                        <Button 
                          variant="outline" 
                          className="w-full"
                          onClick={() => handleViewLevel(level)}
                        >
                          View Level
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          <Dialog open={isLevelDialogOpen} onOpenChange={setIsLevelDialogOpen}>
            <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden flex flex-col">
              <DialogHeader>
                <DialogTitle>{selectedLevel?.name}</DialogTitle>
                <DialogDescription>
                  Level details and student list
                </DialogDescription>
              </DialogHeader>
              <Tabs defaultValue="details" className="flex-1">
                <TabsList>
                  <TabsTrigger value="details">Details</TabsTrigger>
                  <TabsTrigger value="students">
                    Students {isLoadingStudents ? '(Loading...)' : `(${levelStudents.length})`}
                  </TabsTrigger>
                  <TabsTrigger value="subjects">Subjects</TabsTrigger>
                </TabsList>
                <ScrollArea className="flex-1 h-[60vh]">
                  <TabsContent value="details">
                    <div className="space-y-6">
                      <div>
                        <h4 className="text-sm font-medium mb-2">Level Information</h4>
                        <Card>
                          <CardContent className="p-4 space-y-3">
                            <div>
                              <p className="text-sm font-medium">Level Code</p>
                              <p className="text-sm text-muted-foreground">{selectedLevel?.code || 'No code'}</p>
                            </div>
                            <div>
                              <p className="text-sm font-medium">Description</p>
                              <p className="text-sm text-muted-foreground">
                                {selectedLevel?.description || 'No description available'}
                              </p>
                            </div>
                            <div>
                              <p className="text-sm font-medium">Created On</p>
                              <p className="text-sm text-muted-foreground">
                                {selectedLevel?.created_at ? new Date(selectedLevel.created_at).toLocaleDateString() : 'Unknown'}
                              </p>
                            </div>
                          </CardContent>
                        </Card>
                      </div>

                      {selectedLevel?.course_id && (
                        <div>
                          <h4 className="text-sm font-medium mb-2">Course Information</h4>
                          <Card>
                            <CardContent className="p-4 space-y-3">
                              {(() => {
                                const course = getCourseDetails(selectedLevel.course_id);
                                return course ? (
                                  <>
                                    <div>
                                      <p className="text-sm font-medium">Course Name</p>
                                      <p className="text-sm text-muted-foreground">{course.name}</p>
                                    </div>
                                    <div>
                                      <p className="text-sm font-medium">Course Code</p>
                                      <p className="text-sm text-muted-foreground">{course.code}</p>
                                    </div>
                                    <div>
                                      <p className="text-sm font-medium">Duration</p>
                                      <p className="text-sm text-muted-foreground">{course.duration}</p>
                                    </div>
                                    <div>
                                      <p className="text-sm font-medium">Description</p>
                                      <p className="text-sm text-muted-foreground">{course.description}</p>
                                    </div>
                                    <div>
                                      <p className="text-sm font-medium">Status</p>
                                      <Badge variant={course.status === 'active' ? 'default' : 'secondary'}>
                                        {course.status}
                                      </Badge>
                                    </div>
                                  </>
                                ) : (
                                  <p className="text-sm text-muted-foreground">Course information not available</p>
                                );
                              })()}
                            </CardContent>
                          </Card>
                        </div>
                      )}
                    </div>
                  </TabsContent>

                  <TabsContent value="students">
                    {isLoadingStudents ? (
                      <div className="flex items-center justify-center h-32">
                        <Loader2 className="h-6 w-6 animate-spin text-primary" />
                      </div>
                    ) : levelStudents.length > 0 ? (
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Name</TableHead>
                            <TableHead>Email</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead>Action</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {levelStudents.map((student: any) => (
                            <TableRow key={student.id}>
                              <TableCell className="flex items-center space-x-2">
                                <Avatar className="h-8 w-8">
                                  <AvatarImage src={student.profile_image} />
                                  <AvatarFallback>{getInitials(student.name)}</AvatarFallback>
                                </Avatar>
                                <span>{student.name}</span>
                              </TableCell>
                              <TableCell>{student.email}</TableCell>
                              <TableCell>
                                <Badge variant="outline">
                                  {student.status || 'Active'}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                <Button 
                                  variant="outline" 
                                  size="sm"
                                  onClick={() => handleViewStudent(student)}
                                >
                                  <Eye className="h-3 w-3 mr-1" /> View
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    ) : (
                      <div className="flex items-center justify-center h-32 text-muted-foreground">
                        No students in this level
                      </div>
                    )}
                  </TabsContent>

                  <TabsContent value="subjects">
                    {selectedLevel?.course_id ? (
                      <div className="space-y-4">
                        {(() => {
                          const courseSubjects = getCourseSubjects(selectedLevel.course_id);
                          return courseSubjects.length > 0 ? (
                            <div className="grid gap-4 grid-cols-1 md:grid-cols-2">
                              {courseSubjects.map((subject) => (
                                <Card key={subject.id}>
                                  <CardContent className="p-4 flex items-start space-x-3">
                                    <BookOpenCheck className="h-5 w-5 text-primary mt-1" />
                                    <div>
                                      <h4 className="font-medium">{subject.name}</h4>
                                      <p className="text-sm text-muted-foreground">{subject.description}</p>
                                      <div className="flex items-center mt-2 space-x-2">
                                        <Badge variant="outline">{subject.code}</Badge>
                                        {subject.is_core && (
                                          <Badge variant="secondary">Core Subject</Badge>
                                        )}
                                      </div>
                                    </div>
                                  </CardContent>
                                </Card>
                              ))}
                            </div>
                          ) : (
                            <div className="text-center py-8 text-muted-foreground">
                              No subjects assigned to this course
                            </div>
                          );
                        })()}
                      </div>
                    ) : (
                      <div className="flex items-center justify-center h-32 text-muted-foreground">
                        No course assigned to this level
                      </div>
                    )}
                  </TabsContent>
                </ScrollArea>
              </Tabs>
            </DialogContent>
          </Dialog>

          <Dialog open={isStudentDialogOpen} onOpenChange={setIsStudentDialogOpen}>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Student Details</DialogTitle>
                <DialogDescription>
                  Detailed information about the student
                </DialogDescription>
              </DialogHeader>
              
              <div className="space-y-6">
                <div className="flex items-center space-x-4">
                  <Avatar className="h-20 w-20">
                    <AvatarImage src={selectedStudent?.profile_image} />
                    <AvatarFallback className="text-lg">{selectedStudent?.name ? getInitials(selectedStudent.name) : 'NA'}</AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="text-lg font-medium">{selectedStudent?.name}</h3>
                    <p className="text-sm text-muted-foreground">{selectedStudent?.email}</p>
                    <div className="flex items-center mt-2">
                      <Badge variant={selectedStudent?.status === 'active' ? 'default' : 'secondary'}>
                        {selectedStudent?.status || 'Active'}
                      </Badge>
                    </div>
                  </div>
                </div>

                <Tabs defaultValue="info">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="info">Personal Info</TabsTrigger>
                    <TabsTrigger value="academic">Academic Info</TabsTrigger>
                  </TabsList>
                  <TabsContent value="info" className="space-y-4">
                    <Card>
                      <CardContent className="p-4 space-y-3">
                        <div>
                          <p className="text-sm font-medium">Student ID</p>
                          <p className="text-sm text-muted-foreground">{selectedStudent?.student_id || 'Not available'}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium">Phone Number</p>
                          <p className="text-sm text-muted-foreground">{selectedStudent?.phone || 'Not available'}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium">Date of Birth</p>
                          <p className="text-sm text-muted-foreground">
                            {selectedStudent?.date_of_birth ? new Date(selectedStudent.date_of_birth).toLocaleDateString() : 'Not available'}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm font-medium">Address</p>
                          <p className="text-sm text-muted-foreground">{selectedStudent?.address || 'Not available'}</p>
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>
                  <TabsContent value="academic" className="space-y-4">
                    <Card>
                      <CardContent className="p-4 space-y-3">
                        <div>
                          <p className="text-sm font-medium">Enrollment Date</p>
                          <p className="text-sm text-muted-foreground">
                            {selectedStudent?.enrollment_date ? new Date(selectedStudent.enrollment_date).toLocaleDateString() : 'Not available'}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm font-medium">Current Level</p>
                          <p className="text-sm text-muted-foreground">{selectedLevel?.name || 'Not available'}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium">Course</p>
                          <p className="text-sm text-muted-foreground">
                            {getCourseDetails(selectedLevel?.course_id)?.name || 'Not available'}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm font-medium">Academic Status</p>
                          <Badge variant="outline">{selectedStudent?.academic_status || 'Regular'}</Badge>
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>
                </Tabs>
              </div>
            </DialogContent>
          </Dialog>
        </>
      )}
    </div>
  );
};

export default LevelManagement; 