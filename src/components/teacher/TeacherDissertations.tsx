import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Search, 
  Calendar, 
  Clock,
  Eye,
  MessageSquare,
  Users,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  XCircle,
  FileText,
  Filter,
  Download,
  RefreshCw,
  ChevronRight,
  BookOpen,
  BarChart3
} from 'lucide-react';
import { SupervisorDashboardData } from '@/types/dissertation';
import { useSupervisorDashboard } from '@/hooks/useDissertations';
import { formatDistanceToNow, format } from 'date-fns';

interface TeacherDissertationsProps {
  teacher: any;
}

const TeacherDissertations: React.FC<TeacherDissertationsProps> = ({ teacher }) => {
  const navigate = useNavigate();
  const { dashboardData, loading, error, refreshDashboard } = useSupervisorDashboard();
  
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [activityFilter, setActivityFilter] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('last_edited');

  // Filter and sort data
  const filteredData = dashboardData
    .filter(item => {
      const matchesSearch = item.document_title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           item.student_name.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus = statusFilter === 'all' || item.status === statusFilter;
      const matchesActivity = activityFilter === 'all' || 
                             (activityFilter === 'active' && item.days_since_last_update <= 7) ||
                             (activityFilter === 'stale' && item.days_since_last_update > 7);
      
      return matchesSearch && matchesStatus && matchesActivity;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'last_edited':
          return new Date(b.last_edited).getTime() - new Date(a.last_edited).getTime();
        case 'progress':
          return b.progress_percentage - a.progress_percentage;
        case 'word_count':
          return b.word_count - a.word_count;
        case 'student_name':
          return a.student_name.localeCompare(b.student_name);
        default:
          return 0;
      }
    });

  // Get current and overdue dissertations
  const activeDissertations = filteredData.filter(d => 
    d.status === 'draft' || d.status === 'under_review' || d.status === 'ready_for_review' || d.status === 'needs_revision'
  );
  
  const needsAttention = activeDissertations.filter(d => 
    d.status === 'ready_for_review' || d.pending_comments > 0 || d.days_since_last_update > 14
  );

  const completedDissertations = filteredData.filter(d => d.status === 'approved');

  // Calculate statistics
  const totalDissertations = dashboardData.length;
  const avgProgress = dashboardData.length > 0 
    ? Math.round(dashboardData.reduce((sum, d) => sum + d.progress_percentage, 0) / dashboardData.length)
    : 0;
  const totalWords = dashboardData.reduce((sum, d) => sum + d.word_count, 0);
  const pendingReviews = dashboardData.filter(d => d.status === 'ready_for_review').length;

  const handleViewDissertation = (dissertationId: string) => {
    navigate(`/dissertation/${dissertationId}`);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft': return 'bg-gray-100 text-gray-800';
      case 'ready_for_review': return 'bg-blue-100 text-blue-800';
      case 'under_review': return 'bg-yellow-100 text-yellow-800';
      case 'approved': return 'bg-green-100 text-green-800';
      case 'needs_revision': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved': return <CheckCircle className="w-4 h-4" />;
      case 'needs_revision': return <XCircle className="w-4 h-4" />;
      case 'ready_for_review': return <Eye className="w-4 h-4" />;
      case 'under_review': return <MessageSquare className="w-4 h-4" />;
      default: return <FileText className="w-4 h-4" />;
    }
  };

  const getPriorityColor = (item: SupervisorDashboardData) => {
    if (item.status === 'ready_for_review') return 'border-l-blue-500';
    if (item.pending_comments > 0) return 'border-l-orange-500';
    if (item.days_since_last_update > 14) return 'border-l-red-500';
    return 'border-l-gray-200';
  };

  // Manually trigger refresh when needed
  const handleRefresh = () => {
    console.log("Manually refreshing supervisor dashboard...");
    refreshDashboard();
  };

  if (loading && dashboardData.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error && dashboardData.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64 space-y-4">
        <XCircle className="w-12 h-12 text-red-500" />
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900">Error Loading Dashboard</h3>
          <p className="text-gray-600 mt-1">{error}</p>
          <Button onClick={handleRefresh} className="mt-4" variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Supervision Dashboard</h1>
          <p className="text-gray-600 mt-1">Monitor and review student dissertations</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={handleRefresh} disabled={loading}>
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Dissertations</p>
                <p className="text-2xl font-bold">{totalDissertations}</p>
              </div>
              <FileText className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Pending Reviews</p>
                <p className="text-2xl font-bold text-orange-600">{pendingReviews}</p>
              </div>
              <Eye className="w-8 h-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Average Progress</p>
                <p className="text-2xl font-bold text-green-600">{avgProgress}%</p>
              </div>
              <TrendingUp className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Words</p>
                <p className="text-2xl font-bold">{totalWords.toLocaleString()}</p>
              </div>
              <BookOpen className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Search dissertations or students..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <div className="flex items-center space-x-2">
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="draft">Draft</SelectItem>
              <SelectItem value="ready_for_review">Ready for Review</SelectItem>
              <SelectItem value="under_review">Under Review</SelectItem>
              <SelectItem value="needs_revision">Needs Revision</SelectItem>
              <SelectItem value="approved">Approved</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={activityFilter} onValueChange={setActivityFilter}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Activity" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="stale">Stale</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="last_edited">Last Edited</SelectItem>
              <SelectItem value="progress">Progress</SelectItem>
              <SelectItem value="word_count">Word Count</SelectItem>
              <SelectItem value="student_name">Student Name</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Priority Items */}
      {needsAttention.length > 0 && (
        <Card className="border-l-4 border-l-orange-500">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <AlertTriangle className="w-5 h-5 text-orange-500" />
              <span>Needs Attention ({needsAttention.length})</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {needsAttention.slice(0, 3).map((item) => (
                <div key={item.document_id} className="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">{item.document_title}</h4>
                    <p className="text-sm text-gray-600">
                      {item.student_name} • {item.status === 'ready_for_review' ? 'Ready for review' : 
                       item.pending_comments > 0 ? `${item.pending_comments} pending comments` :
                       `Inactive for ${item.days_since_last_update} days`}
                    </p>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleViewDissertation(item.document_id)}
                  >
                    Review
                    <ChevronRight className="w-4 h-4 ml-1" />
                  </Button>
                </div>
              ))}
              {needsAttention.length > 3 && (
                <p className="text-sm text-gray-500 text-center">
                  and {needsAttention.length - 3} more items needing attention
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Main Content */}
      <Tabs defaultValue="active" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="active" className="flex items-center space-x-2">
            <Clock className="w-4 h-4" />
            <span>Active Projects ({activeDissertations.length})</span>
          </TabsTrigger>
          <TabsTrigger value="completed" className="flex items-center space-x-2">
            <CheckCircle className="w-4 h-4" />
            <span>Completed ({completedDissertations.length})</span>
          </TabsTrigger>
        </TabsList>

        {/* Active Dissertations */}
        <TabsContent value="active" className="mt-6">
          {activeDissertations.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No active dissertations found</h3>
                <p className="text-gray-600">
                  {searchTerm || statusFilter !== 'all' || activityFilter !== 'all' 
                    ? 'Try adjusting your filters' 
                    : 'No students are currently working on dissertations under your supervision'}
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {activeDissertations.map((item) => (
                <Card key={item.document_id} className={`hover:shadow-md transition-shadow border-l-4 ${getPriorityColor(item)}`}>
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="text-lg font-semibold text-gray-900 line-clamp-1">
                            {item.document_title}
                          </h3>
                          <Badge className={`${getStatusColor(item.status)} flex items-center space-x-1`}>
                            {getStatusIcon(item.status)}
                            <span>{item.status.replace('_', ' ').toUpperCase()}</span>
                          </Badge>
                        </div>
                        
                        <p className="text-gray-600 mb-3">
                          Student: <span className="font-medium">{item.student_name}</span>
                        </p>
                        
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                          <div className="flex items-center space-x-2 text-sm text-gray-600">
                            <BarChart3 className="w-4 h-4" />
                            <span>{item.progress_percentage}% complete</span>
                          </div>
                          <div className="flex items-center space-x-2 text-sm text-gray-600">
                            <FileText className="w-4 h-4" />
                            <span>{item.word_count.toLocaleString()} words</span>
                          </div>
                          <div className="flex items-center space-x-2 text-sm text-gray-600">
                            <BookOpen className="w-4 h-4" />
                            <span>{item.total_pages} pages</span>
                          </div>
                          <div className="flex items-center space-x-2 text-sm text-gray-600">
                            <Clock className="w-4 h-4" />
                            <span>{formatDistanceToNow(item.last_edited, { addSuffix: true })}</span>
                          </div>
                        </div>

                        {/* Progress Bar */}
                        <div className="mb-3">
                          <div className="flex items-center justify-between text-sm mb-1">
                            <span className="text-gray-600">Progress</span>
                            <span className="font-medium">{item.progress_percentage}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-blue-600 h-2 rounded-full transition-all"
                              style={{ width: `${item.progress_percentage}%` }}
                            />
                          </div>
                        </div>

                        {/* Alerts */}
                        <div className="flex items-center space-x-4 text-sm">
                          {item.pending_comments > 0 && (
                            <div className="flex items-center space-x-1 text-orange-600">
                              <MessageSquare className="w-4 h-4" />
                              <span>{item.pending_comments} pending comments</span>
                            </div>
                          )}
                          {item.chapters_ready_for_review > 0 && (
                            <div className="flex items-center space-x-1 text-blue-600">
                              <Eye className="w-4 h-4" />
                              <span>{item.chapters_ready_for_review} chapters ready</span>
                            </div>
                          )}
                          {item.days_since_last_update > 7 && (
                            <div className="flex items-center space-x-1 text-red-600">
                              <AlertTriangle className="w-4 h-4" />
                              <span>Inactive for {item.days_since_last_update} days</span>
                            </div>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex flex-col space-y-2 ml-4">
                        <Button
                          onClick={() => handleViewDissertation(item.document_id)}
                          size="sm"
                        >
                          <Eye className="w-4 h-4 mr-2" />
                          Review
                        </Button>
                        
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            // TODO: Implement messaging functionality
                            console.log('Message student:', item.student_id);
                          }}
                        >
                          <MessageSquare className="w-4 h-4 mr-2" />
                          Message
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        {/* Completed Dissertations */}
        <TabsContent value="completed" className="mt-6">
          {completedDissertations.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <CheckCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No completed dissertations found</h3>
                <p className="text-gray-600">
                  {searchTerm ? 'Try adjusting your search terms' : 'No dissertations have been completed yet'}
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {completedDissertations.map((item) => (
                <Card key={item.document_id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="space-y-3">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 line-clamp-2">
                          {item.document_title}
                        </h3>
                        <p className="text-gray-600 mt-1">
                          by <span className="font-medium">{item.student_name}</span>
                        </p>
                      </div>
                      
                      <Badge className={`${getStatusColor(item.status)} flex items-center space-x-1 w-fit`}>
                        {getStatusIcon(item.status)}
                        <span>COMPLETED</span>
                      </Badge>
                      
                      <div className="grid grid-cols-2 gap-3 text-sm text-gray-600">
                        <div className="flex items-center space-x-1">
                          <FileText className="w-4 h-4" />
                          <span>{item.word_count.toLocaleString()}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <BookOpen className="w-4 h-4" />
                          <span>{item.total_pages} pages</span>
                        </div>
                      </div>
                      
                      <div className="text-sm text-gray-500">
                        <div className="flex items-center space-x-1">
                          <Calendar className="w-4 h-4" />
                          <span>Completed {format(item.last_edited, 'MMM dd, yyyy')}</span>
                        </div>
                      </div>
                      
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full"
                        onClick={() => handleViewDissertation(item.document_id)}
                      >
                        <Eye className="w-4 h-4 mr-2" />
                        View
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default TeacherDissertations; 