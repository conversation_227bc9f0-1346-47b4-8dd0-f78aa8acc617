import { useEffect } from 'react';
import { Routes, Route, Navigate, useLocation, Link } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useAuth } from '@/contexts/AuthContext';
import { Loader2, Users, BookOpen, Calendar, Clock, CheckCircle2, FileText, BarChart, Bell, MapPin } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { getLevels } from '@/api/levels';
import { getTeacherSchedule } from '@/api/schedule';
import { format } from 'date-fns';
import { User } from '@/lib/api-client';
import { getStudentsByLevel } from '@/api/students';
import { getClassTimetableByTeacher } from '@/api/timetable';
import { getSubjects } from '@/api/subjects';

// Import teacher dashboard components
import TeacherProfile from '@/components/teacher/TeacherProfile';
import LevelManagement from '@/components/teacher/LevelManagement';
import AttendanceManagement from '@/components/teacher/AttendanceManagement';
import TimetableSchedule from '@/components/teacher/TimetableSchedule';
import AssignmentsHomework from '@/components/teacher/AssignmentsHomework';
import AssignmentSubmissions from '@/components/teacher/AssignmentSubmissions';
import ExamsGrading from '@/components/teacher/ExamsGrading';
import StudentPerformance from '@/components/teacher/StudentPerformance';
import Announcements from '@/components/teacher/Announcements';
import CourseMaterials from '@/components/teacher/CourseMaterials';
import TeacherDissertations from '@/components/teacher/TeacherDissertations';

// Extended profile type to include teacher-specific fields
interface TeacherProfileDocument extends ProfileDocument {
  bio?: string;
  contact_number?: string;
  specialization?: string;
  assigned_levels?: string[];
  assigned_courses?: string[];
}

interface TeacherDashboardProps {
  setSidebarOpen?: (open: boolean) => void;
}

const TeacherDashboard = ({ setSidebarOpen }: TeacherDashboardProps) => {
  const { userProfile, isLoading } = useAuth();
  const location = useLocation();
  
  // Use setSidebarOpen when location changes (close sidebar on navigation on mobile)
  useEffect(() => {
    if (setSidebarOpen) {
      setSidebarOpen(false);
    }
  }, [location.pathname, setSidebarOpen]);
  
  // Cast userProfile to TeacherProfileDocument
  const teacherProfile = userProfile as TeacherProfileDocument | null;
  
  // Check if we're on the main teacher dashboard path
  const isMainPath = location.pathname === '/dashboard/teacher' || location.pathname === '/dashboard/teacher/';

  // Fetch levels data
  const { data: levels = [] } = useQuery({
    queryKey: ['levels'],
    queryFn: getLevels
  });

  // Filter levels to only show those assigned to the teacher
  const assignedLevels = levels.filter(level => 
    teacherProfile?.assigned_levels?.includes(level.id)
  );

  // Fetch students for each level
  const { data: levelStudentCounts = {} } = useQuery({
    queryKey: ['levelStudentCounts', assignedLevels.map(level => level.id)],
    queryFn: async () => {
      const counts: Record<string, number> = {};
      
      for (const level of assignedLevels) {
        const students = await getStudentsByLevel(level.id);
        counts[level.id] = students.length;
      }
      
      return counts;
    },
    enabled: assignedLevels.length > 0
  });

  // Fetch teacher's schedule for today
  const { data: schedules = [] } = useQuery({
    queryKey: ['schedules', teacherProfile?.id],
    queryFn: () => teacherProfile?.id ? getTeacherSchedule(teacherProfile.id) : Promise.resolve([]),
    enabled: !!teacherProfile?.id
  });

  // Fetch teacher's timetable for today
  const { data: timetableEntries = [] } = useQuery({
    queryKey: ['timetable', teacherProfile?.id],
    queryFn: () => teacherProfile?.id ? getClassTimetableByTeacher(teacherProfile.id) : Promise.resolve([]),
    enabled: !!teacherProfile?.id
  });

  // Fetch subjects data
  const { data: subjects = [] } = useQuery({
    queryKey: ['subjects'],
    queryFn: getSubjects
  });

  // Get today's schedule
  const todaySchedule = schedules.filter(schedule => 
    schedule.day_of_week === new Date().getDay()
  ).sort((a, b) => 
    parseInt(a.start_time.replace(':', '')) - parseInt(b.start_time.replace(':', ''))
  );

  // Get today's timetable
  const todayTimetable = timetableEntries.filter(entry => 
    entry.day === format(new Date(), 'EEEE')
  ).sort((a, b) => 
    a.startTime.localeCompare(b.startTime)
  );

  // Calculate total students
  const totalStudents = assignedLevels.reduce((sum, level) => sum + (levelStudentCounts[level.id] || 0), 0);

  // Helper function to get subject name
  const getSubjectName = (subjectId: string) => {
    const subject = subjects.find(s => s.id === subjectId);
    return subject ? subject.name : "Subject info unavailable";
  };

  // Calculate teaching hours based on timetable entries
  const calculateTeachingHours = () => {
    let totalMinutes = 0;
    
    timetableEntries.forEach(entry => {
      try {
        const [startHours, startMinutes] = entry.startTime.split(':').map(Number);
        const [endHours, endMinutes] = entry.endTime.split(':').map(Number);
        
        const startTotalMinutes = startHours * 60 + startMinutes;
        const endTotalMinutes = endHours * 60 + endMinutes;
        
        const classDuration = endTotalMinutes - startTotalMinutes;
        totalMinutes += classDuration;
      } catch (error) {
        console.error("Error calculating class duration:", error);
      }
    });
    
    // Convert total minutes to hours
    return (totalMinutes / 60).toFixed(1);
  };

  // Helper function to format time
  const formatTime = (time: string) => {
    try {
      const [hours, minutes] = time.split(':').map(Number);
      const date = new Date();
      date.setHours(hours, minutes, 0, 0);
      return date.toLocaleTimeString('en-US', { 
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      });
    } catch (error) {
      return time;
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
      </div>
    );
  }

  if (!teacherProfile || teacherProfile.role !== 'teacher') {
    return <Navigate to="/dashboard" replace />;
  }

  // Dashboard overview component
  const DashboardOverview = () => (
    <div className="space-y-4">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Students</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalStudents}</div>
            <p className="text-xs text-muted-foreground">
              Across {assignedLevels.length} levels
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Today's Classes</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{todayTimetable.length}</div>
            <p className="text-xs text-muted-foreground">
              {todayTimetable.length > 0 
                ? `Next class: ${getSubjectName(todayTimetable[0].subject)} at ${formatTime(todayTimetable[0].startTime)}`
                : 'No classes today'}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Teaching Hours</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {calculateTeachingHours()}h
            </div>
            <p className="text-xs text-muted-foreground">
              Weekly teaching load
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Assigned Courses</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{teacherProfile?.assigned_courses?.length || 0}</div>
            <p className="text-xs text-muted-foreground">
              Active courses
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <Card className="col-span-4">
          <CardHeader>
            <CardTitle>Today's Schedule</CardTitle>
            <CardDescription>
              Your classes for {format(new Date(), 'EEEE, MMMM d, yyyy')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {todayTimetable.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  No classes scheduled for today
                </div>
              ) : (
                todayTimetable.map((entry, index) => {
                  const level = levels.find(l => l.id === entry.level);
                  return (
                    <div key={index} className="flex items-center p-3 rounded-lg bg-muted">
                      <div className="flex-1">
                        <div className="font-medium">{getSubjectName(entry.subject)}</div>
                        <div className="text-sm text-muted-foreground">
                          <div className="flex items-center space-x-2">
                            <Clock className="h-4 w-4" />
                            <span>{formatTime(entry.startTime)} - {formatTime(entry.endTime)}</span>
                          </div>
                          <div className="flex items-center space-x-2 mt-1">
                            <MapPin className="h-4 w-4" />
                            <span>{entry.room}</span>
                          </div>
                          <div className="flex items-center space-x-2 mt-1">
                            <Users className="h-4 w-4" />
                            <span>{level?.name || 'Unknown Level'}</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center">
                        {(() => {
                          const now = new Date();
                          const [hours, minutes] = entry.startTime.split(':').map(Number);
                          const classTime = new Date(now.setHours(hours, minutes));
                          const status = now > classTime ? 'Completed' : 'Upcoming';
                          return (
                            <span className={`text-sm ${status === 'Completed' ? 'text-muted-foreground' : 'text-green-600'}`}>
                              {status}
                            </span>
                          );
                        })()}
                      </div>
                    </div>
                  );
                })
              )}
            </div>
          </CardContent>
        </Card>

        <Card className="col-span-3">
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Frequently used features and tools
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-3">
              <Link to="/dashboard/teacher/attendance">
                <Card className="cursor-pointer hover:bg-muted">
                  <CardContent className="p-3 flex flex-col items-center justify-center text-center">
                    <CheckCircle2 className="h-5 w-5 mb-1.5 text-primary" />
                    <div className="font-medium text-sm">Mark Attendance</div>
                    <div className="text-xs text-muted-foreground">Record student attendance</div>
                  </CardContent>
                </Card>
              </Link>
              <Link to="/dashboard/teacher/assignments">
                <Card className="cursor-pointer hover:bg-muted">
                  <CardContent className="p-3 flex flex-col items-center justify-center text-center">
                    <FileText className="h-5 w-5 mb-1.5 text-primary" />
                    <div className="font-medium text-sm">Assignments</div>
                    <div className="text-xs text-muted-foreground">Create & grade assignments</div>
                  </CardContent>
                </Card>
              </Link>
              <Link to="/dashboard/teacher/performance">
                <Card className="cursor-pointer hover:bg-muted">
                  <CardContent className="p-3 flex flex-col items-center justify-center text-center">
                    <BarChart className="h-5 w-5 mb-1.5 text-primary" />
                    <div className="font-medium text-sm">Performance</div>
                    <div className="text-xs text-muted-foreground">View student progress</div>
                  </CardContent>
                </Card>
              </Link>
              <Link to="/dashboard/teacher/announcements">
                <Card className="cursor-pointer hover:bg-muted">
                  <CardContent className="p-3 flex flex-col items-center justify-center text-center">
                    <Bell className="h-5 w-5 mb-1.5 text-primary" />
                    <div className="font-medium text-sm">Announcements</div>
                    <div className="text-xs text-muted-foreground">Post class announcements</div>
                  </CardContent>
                </Card>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  return (
    <div className="fixed top-[64px] right-0 bottom-0 left-[240px] overflow-y-auto p-6 bg-background">
      {isMainPath && (
        <div className="flex flex-col space-y-2 mb-6 pt-2">
          <h2 className="text-3xl font-bold tracking-tight">Teacher Dashboard</h2>
          <p className="text-muted-foreground">
            Welcome back, {teacherProfile.displayName}! Here's an overview of your teaching activities.
          </p>
        </div>
      )}

      <Routes>
        <Route path="" element={<DashboardOverview />} />
        <Route path="profile" element={<TeacherProfile />} />
        <Route path="levels" element={<LevelManagement />} />
        <Route path="attendance" element={<AttendanceManagement />} />
        <Route path="timetable" element={<TimetableSchedule />} />
        <Route path="assignments" element={<AssignmentsHomework />} />
        <Route path="assignments/:id/submissions" element={<AssignmentSubmissions />} />
        <Route path="exams" element={<ExamsGrading />} />
        <Route path="performance" element={<StudentPerformance />} />
        <Route path="announcements" element={<Announcements />} />
        <Route path="materials" element={<CourseMaterials />} />
        <Route path="dissertations" element={<TeacherDissertations />} />
        <Route path="*" element={<Navigate to="" replace />} />
      </Routes>
    </div>
  );
};

export default TeacherDashboard; 