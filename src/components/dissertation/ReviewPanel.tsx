import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { 
  Eye, 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Clock,
  FileText,
  MessageSquare,
  ThumbsUp,
  ThumbsDown,
  Flag,
  BookOpen,
  Target
} from 'lucide-react';
import { DissertationDocument, ReviewState } from '@/types/dissertation';

interface ReviewPanelProps {
  document: DissertationDocument;
  onStatusChange: (status: DissertationDocument['status']) => void;
  reviewState: ReviewState;
  onReviewStateChange: (state: ReviewState) => void;
  userRole: 'student' | 'teacher';
}

export const ReviewPanel: React.FC<ReviewPanelProps> = ({
  document,
  onStatusChange,
  reviewState,
  onReviewStateChange,
  userRole
}) => {
  const [reviewNotes, setReviewNotes] = useState('');
  const [selectedPages, setSelectedPages] = useState<number[]>([]);

  const statusOptions = [
    { value: 'draft', label: 'Draft', icon: FileText, color: 'bg-gray-100 text-gray-800' },
    { value: 'ready_for_review', label: 'Ready for Review', icon: Eye, color: 'bg-blue-100 text-blue-800' },
    { value: 'under_review', label: 'Under Review', icon: Clock, color: 'bg-yellow-100 text-yellow-800' },
    { value: 'approved', label: 'Approved', icon: CheckCircle, color: 'bg-green-100 text-green-800' },
    { value: 'needs_revision', label: 'Needs Revision', icon: XCircle, color: 'bg-red-100 text-red-800' },
  ] as const;

  const getProgressPercentage = () => {
    const totalPages = document.page_count || 1;
    const reviewedPages = document.pages_reviewed?.length || 0;
    return Math.round((reviewedPages / totalPages) * 100);
  };

  const handleMarkPageReviewed = (pageNumber: number) => {
    const updatedPages = document.pages_reviewed?.includes(pageNumber)
      ? document.pages_reviewed.filter(p => p !== pageNumber)
      : [...(document.pages_reviewed || []), pageNumber];
    
    // This would typically update the document in the database
    console.log('Updated reviewed pages:', updatedPages);
  };

  const handleStatusChange = (newStatus: DissertationDocument['status']) => {
    onStatusChange(newStatus);
  };

  const handleToggleReviewMode = () => {
    onReviewStateChange({
      ...reviewState,
      isReviewMode: !reviewState.isReviewMode
    });
  };

  const currentStatus = statusOptions.find(s => s.value === document.status);

  return (
    <div className="review-panel h-full flex flex-col">
      {/* Header */}
      <div className="p-4 border-b">
        <h3 className="font-semibold flex items-center mb-4">
          <Eye className="w-4 h-4 mr-2" />
          Review Tools
        </h3>

        {/* Current Status */}
        <div className="mb-4">
          <label className="text-sm font-medium mb-2 block">Current Status</label>
          <div className="flex items-center space-x-2">
            {currentStatus && (
              <Badge className={`${currentStatus.color} flex items-center space-x-1`}>
                <currentStatus.icon className="w-3 h-3" />
                <span>{currentStatus.label}</span>
              </Badge>
            )}
          </div>
        </div>

        {/* Status Change (Teacher Only) */}
        {userRole === 'teacher' && (
          <div className="mb-4">
            <label className="text-sm font-medium mb-2 block">Change Status</label>
            <Select value={document.status} onValueChange={handleStatusChange}>
              <SelectTrigger className="w-full">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {statusOptions.map((status) => (
                  <SelectItem key={status.value} value={status.value}>
                    <div className="flex items-center space-x-2">
                      <status.icon className="w-4 h-4" />
                      <span>{status.label}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        {/* Review Mode Toggle */}
        <div className="mb-4">
          <Button
            variant={reviewState.isReviewMode ? 'default' : 'outline'}
            onClick={handleToggleReviewMode}
            className="w-full"
          >
            <Eye className="w-4 h-4 mr-2" />
            {reviewState.isReviewMode ? 'Exit Review Mode' : 'Enter Review Mode'}
          </Button>
        </div>
      </div>

      {/* Review Progress */}
      <div className="p-4 border-b">
        <h4 className="font-medium mb-3 flex items-center">
          <Target className="w-4 h-4 mr-2" />
          Review Progress
        </h4>
        
        <div className="space-y-3">
          <div className="flex items-center justify-between text-sm">
            <span>Pages Reviewed</span>
            <span>{document.pages_reviewed?.length || 0} / {document.page_count || 1}</span>
          </div>
          
          <Progress value={getProgressPercentage()} className="w-full" />
          
          <div className="text-xs text-gray-600 text-center">
            {getProgressPercentage()}% Complete
          </div>
        </div>

        {/* Chapters Ready for Review */}
        {document.chapters_ready_for_review && document.chapters_ready_for_review.length > 0 && (
          <div className="mt-4">
            <h5 className="font-medium text-sm mb-2">Chapters Ready for Review</h5>
            <div className="space-y-1">
              {document.chapters_ready_for_review.map((chapter, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {chapter}
                </Badge>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Quick Actions (Teacher Only) */}
      {userRole === 'teacher' && (
        <div className="p-4 border-b">
          <h4 className="font-medium mb-3">Quick Actions</h4>
          
          <div className="grid grid-cols-2 gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleStatusChange('approved')}
              className="flex items-center justify-center"
            >
              <ThumbsUp className="w-3 h-3 mr-1" />
              Approve
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleStatusChange('needs_revision')}
              className="flex items-center justify-center"
            >
              <ThumbsDown className="w-3 h-3 mr-1" />
              Revise
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                onReviewStateChange({
                  ...reviewState,
                  filterByTag: 'plagiarized'
                });
              }}
              className="flex items-center justify-center"
            >
              <Flag className="w-3 h-3 mr-1" />
              Flag
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleStatusChange('under_review')}
              className="flex items-center justify-center"
            >
              <Clock className="w-3 h-3 mr-1" />
              Review
            </Button>
          </div>
        </div>
      )}

      {/* Page Review Checklist */}
      <div className="p-4 border-b">
        <h4 className="font-medium mb-3 flex items-center">
          <BookOpen className="w-4 h-4 mr-2" />
          Page Review
        </h4>
        
        <div className="space-y-2 max-h-40 overflow-y-auto">
          {Array.from({ length: document.page_count || 1 }, (_, i) => i + 1).map((pageNumber) => (
            <div key={pageNumber} className="flex items-center justify-between">
              <span className="text-sm">Page {pageNumber}</span>
              <Button
                variant={document.pages_reviewed?.includes(pageNumber) ? 'default' : 'outline'}
                size="sm"
                onClick={() => handleMarkPageReviewed(pageNumber)}
                className="h-6 px-2"
              >
                {document.pages_reviewed?.includes(pageNumber) ? (
                  <CheckCircle className="w-3 h-3" />
                ) : (
                  <Eye className="w-3 h-3" />
                )}
              </Button>
            </div>
          ))}
        </div>
      </div>

      {/* Review Notes */}
      <div className="p-4 flex-1">
        <h4 className="font-medium mb-3 flex items-center">
          <MessageSquare className="w-4 h-4 mr-2" />
          Review Notes
        </h4>
        
        <Textarea
          value={reviewNotes}
          onChange={(e) => setReviewNotes(e.target.value)}
          placeholder="Add your review notes here..."
          className="min-h-[120px] mb-3"
        />
        
        <Button size="sm" className="w-full">
          Save Notes
        </Button>
      </div>

      {/* Document Stats */}
      <div className="p-4 border-t bg-gray-50">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-600">Word Count</span>
            <p className="font-medium">{document.word_count?.toLocaleString() || 0}</p>
          </div>
          <div>
            <span className="text-gray-600">Last Updated</span>
            <p className="font-medium">{new Date(document.updated_at).toLocaleDateString()}</p>
          </div>
          <div>
            <span className="text-gray-600">Version</span>
            <p className="font-medium">v{document.version}</p>
          </div>
          <div>
            <span className="text-gray-600">Auto-save</span>
            <p className="font-medium">{document.auto_save_enabled ? 'On' : 'Off'}</p>
          </div>
        </div>
      </div>
    </div>
  );
}; 