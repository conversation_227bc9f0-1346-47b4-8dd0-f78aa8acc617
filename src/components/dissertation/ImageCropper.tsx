import React, { useState, useRef, useEffect } from 'react';
import <PERSON>actCrop, { Crop, PixelCrop, centerCrop, makeAspectCrop } from 'react-image-crop';
import 'react-image-crop/dist/ReactCrop.css';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface ImageCropperProps {
  image: File;
  onCropComplete: (croppedImage: Blob) => void;
  onCancel: () => void;
}

function centerAspectCrop(
  mediaWidth: number,
  mediaHeight: number,
  aspect: number,
) {
  return centerCrop(
    makeAspectCrop(
      {
        unit: '%',
        width: 90,
      },
      aspect,
      mediaWidth,
      mediaHeight,
    ),
    mediaWidth,
    mediaHeight,
  );
}

export function ImageCropper({ image, onCropComplete, onCancel }: ImageCropperProps) {
  const [crop, setCrop] = useState<Crop>();
  const [completedCrop, setCompletedCrop] = useState<PixelCrop>();
  const [aspect, setAspect] = useState<number | undefined>(16 / 9);
  const [rotation, setRotation] = useState(0);
  const [scale, setScale] = useState(1);
  const [imageUrl, setImageUrl] = useState<string>('');
  const imgRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Create URL for the image
  useEffect(() => {
    const url = URL.createObjectURL(image);
    setImageUrl(url);
    return () => URL.revokeObjectURL(url);
  }, [image]);

  // Set default crop when image loads
  const onImageLoad = (e: React.SyntheticEvent<HTMLImageElement>) => {
    if (aspect) {
      const { width, height } = e.currentTarget;
      setCrop(centerAspectCrop(width, height, aspect));
    }
  };

  // Handle aspect ratio change
  const handleAspectChange = (value: string) => {
    if (value === 'free') {
      setAspect(undefined);
    } else {
      const [width, height] = value.split(':').map(Number);
      const newAspect = width / height;
      setAspect(newAspect);
      
      if (imgRef.current) {
        const { width: imgWidth, height: imgHeight } = imgRef.current;
        setCrop(centerAspectCrop(imgWidth, imgHeight, newAspect));
      }
    }
  };

  // Process the cropped image
  const handleCropClick = async () => {
    if (!completedCrop || !imgRef.current) return;

    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Calculate the size of the crop
    const scaleX = imgRef.current.naturalWidth / imgRef.current.width;
    const scaleY = imgRef.current.naturalHeight / imgRef.current.height;

    canvas.width = completedCrop.width * scaleX;
    canvas.height = completedCrop.height * scaleY;

    // Draw the cropped image
    ctx.translate(canvas.width / 2, canvas.height / 2);
    ctx.rotate((rotation * Math.PI) / 180);
    ctx.scale(scale, scale);
    ctx.translate(-canvas.width / 2, -canvas.height / 2);
    
    ctx.drawImage(
      imgRef.current,
      completedCrop.x * scaleX,
      completedCrop.y * scaleY,
      completedCrop.width * scaleX,
      completedCrop.height * scaleY,
      0,
      0,
      completedCrop.width * scaleX,
      completedCrop.height * scaleY,
    );

    // Convert canvas to blob
    canvas.toBlob((blob) => {
      if (blob) {
        onCropComplete(blob);
      }
    }, 'image/jpeg', 0.95);
  };

  return (
    <div className="space-y-4">
      <div ref={containerRef} className="flex justify-center items-center" style={{ height: '400px', overflow: 'auto' }}>
        <ReactCrop
          crop={crop}
          onChange={(c) => setCrop(c)}
          onComplete={(c) => setCompletedCrop(c)}
          aspect={aspect}
          className="border rounded-md bg-gray-100"
          style={{ maxWidth: '100%', maxHeight: '100%' }}
        >
          <img
            ref={imgRef}
            src={imageUrl}
            alt="Crop preview"
            style={{ 
              transform: `scale(${scale}) rotate(${rotation}deg)`,
              objectFit: 'contain'
            }}
            onLoad={onImageLoad}
          />
        </ReactCrop>
      </div>

      <div className="space-y-4 p-4 border rounded-md bg-gray-50">
        <div className="space-y-2">
          <Label>Aspect Ratio</Label>
          <Select defaultValue="16:9" onValueChange={handleAspectChange}>
            <SelectTrigger>
              <SelectValue placeholder="Select aspect ratio" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="free">Free (no constraint)</SelectItem>
              <SelectItem value="1:1">Square (1:1)</SelectItem>
              <SelectItem value="16:9">Widescreen (16:9)</SelectItem>
              <SelectItem value="4:3">Standard (4:3)</SelectItem>
              <SelectItem value="3:2">Photo (3:2)</SelectItem>
              <SelectItem value="2:3">Portrait (2:3)</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label>Rotation: {rotation}°</Label>
          <Slider
            value={[rotation]}
            min={0}
            max={360}
            step={1}
            onValueChange={(value) => setRotation(value[0])}
          />
        </div>

        <div className="space-y-2">
          <Label>Scale: {scale.toFixed(1)}x</Label>
          <Slider
            value={[scale]}
            min={0.5}
            max={2}
            step={0.1}
            onValueChange={(value) => setScale(value[0])}
          />
        </div>
      </div>

      <div className="flex justify-end space-x-2">
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button onClick={handleCropClick}>
          Apply Crop
        </Button>
      </div>
    </div>
  );
} 