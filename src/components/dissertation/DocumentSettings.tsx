import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Settings, FileText, Type, Layout, Save } from 'lucide-react';
import { DissertationSettings } from '@/types/dissertation';

interface DocumentSettingsProps {
  documentId: string;
  onSettingsChange: (settings: DissertationSettings) => void;
}

export const DocumentSettings: React.FC<DocumentSettingsProps> = ({
  documentId,
  onSettingsChange
}) => {
  const [settings, setSettings] = useState<DissertationSettings>({
    id: documentId,
    document_id: documentId,
    auto_save_interval: 30,
    page_margins: {
      top: 2.5,
      bottom: 2.5,
      left: 2.5,
      right: 2.5
    },
    font_family: 'Times New Roman',
    font_size: 12,
    line_spacing: 2,
    page_orientation: 'portrait',
    citation_style: 'apa',
    show_word_count: true,
    show_page_numbers: true,
    enable_spell_check: true,
    enable_grammar_check: true
  });

  const handleSettingChange = (key: keyof DissertationSettings, value: any) => {
    const updatedSettings = { ...settings, [key]: value };
    setSettings(updatedSettings);
  };

  const handleMarginChange = (side: keyof DissertationSettings['page_margins'], value: number) => {
    const updatedSettings = {
      ...settings,
      page_margins: {
        ...settings.page_margins,
        [side]: value
      }
    };
    setSettings(updatedSettings);
  };

  const handleSaveSettings = () => {
    onSettingsChange(settings);
  };

  const fontFamilies = [
    'Times New Roman',
    'Arial',
    'Calibri',
    'Georgia',
    'Verdana',
    'Helvetica'
  ];

  const citationStyles = [
    { value: 'apa', label: 'APA' },
    { value: 'mla', label: 'MLA' },
    { value: 'chicago', label: 'Chicago' },
    { value: 'harvard', label: 'Harvard' }
  ];

  return (
    <div className="document-settings h-full flex flex-col">
      {/* Header */}
      <div className="p-4 border-b">
        <h3 className="font-semibold flex items-center mb-2">
          <Settings className="w-4 h-4 mr-2" />
          Document Settings
        </h3>
      </div>

      {/* Settings Content */}
      <div className="flex-1 overflow-y-auto p-4 space-y-6">
        {/* Auto-save Settings */}
        <div className="space-y-3">
          <h4 className="font-medium text-sm flex items-center">
            <Save className="w-4 h-4 mr-2" />
            Auto-save
          </h4>
          
          <div className="space-y-2">
            <Label htmlFor="auto-save-interval" className="text-xs">
              Auto-save interval (seconds)
            </Label>
            <Input
              id="auto-save-interval"
              type="number"
              value={settings.auto_save_interval}
              onChange={(e) => handleSettingChange('auto_save_interval', parseInt(e.target.value))}
              min={10}
              max={300}
              className="h-8"
            />
          </div>
        </div>

        <Separator />

        {/* Typography Settings */}
        <div className="space-y-3">
          <h4 className="font-medium text-sm flex items-center">
            <Type className="w-4 h-4 mr-2" />
            Typography
          </h4>
          
          <div className="space-y-3">
            <div>
              <Label htmlFor="font-family" className="text-xs">Font Family</Label>
              <Select value={settings.font_family} onValueChange={(value) => handleSettingChange('font_family', value)}>
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {fontFamilies.map((font) => (
                    <SelectItem key={font} value={font}>
                      <span style={{ fontFamily: font }}>{font}</span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="font-size" className="text-xs">Font Size (pt)</Label>
              <Input
                id="font-size"
                type="number"
                value={settings.font_size}
                onChange={(e) => handleSettingChange('font_size', parseInt(e.target.value))}
                min={8}
                max={72}
                className="h-8"
              />
            </div>

            <div>
              <Label htmlFor="line-spacing" className="text-xs">Line Spacing</Label>
              <Select 
                value={settings.line_spacing.toString()} 
                onValueChange={(value) => handleSettingChange('line_spacing', parseFloat(value))}
              >
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">Single</SelectItem>
                  <SelectItem value="1.15">1.15</SelectItem>
                  <SelectItem value="1.5">1.5</SelectItem>
                  <SelectItem value="2">Double</SelectItem>
                  <SelectItem value="2.5">2.5</SelectItem>
                  <SelectItem value="3">Triple</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <Separator />

        {/* Page Layout */}
        <div className="space-y-3">
          <h4 className="font-medium text-sm flex items-center">
            <Layout className="w-4 h-4 mr-2" />
            Page Layout
          </h4>
          
          <div className="space-y-3">
            <div>
              <Label htmlFor="page-orientation" className="text-xs">Orientation</Label>
              <Select 
                value={settings.page_orientation} 
                onValueChange={(value) => handleSettingChange('page_orientation', value)}
              >
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="portrait">Portrait</SelectItem>
                  <SelectItem value="landscape">Landscape</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-2 gap-2">
              <div>
                <Label htmlFor="margin-top" className="text-xs">Top Margin (cm)</Label>
                <Input
                  id="margin-top"
                  type="number"
                  value={settings.page_margins.top}
                  onChange={(e) => handleMarginChange('top', parseFloat(e.target.value))}
                  min={0.5}
                  max={10}
                  step={0.1}
                  className="h-8"
                />
              </div>
              <div>
                <Label htmlFor="margin-bottom" className="text-xs">Bottom Margin (cm)</Label>
                <Input
                  id="margin-bottom"
                  type="number"
                  value={settings.page_margins.bottom}
                  onChange={(e) => handleMarginChange('bottom', parseFloat(e.target.value))}
                  min={0.5}
                  max={10}
                  step={0.1}
                  className="h-8"
                />
              </div>
              <div>
                <Label htmlFor="margin-left" className="text-xs">Left Margin (cm)</Label>
                <Input
                  id="margin-left"
                  type="number"
                  value={settings.page_margins.left}
                  onChange={(e) => handleMarginChange('left', parseFloat(e.target.value))}
                  min={0.5}
                  max={10}
                  step={0.1}
                  className="h-8"
                />
              </div>
              <div>
                <Label htmlFor="margin-right" className="text-xs">Right Margin (cm)</Label>
                <Input
                  id="margin-right"
                  type="number"
                  value={settings.page_margins.right}
                  onChange={(e) => handleMarginChange('right', parseFloat(e.target.value))}
                  min={0.5}
                  max={10}
                  step={0.1}
                  className="h-8"
                />
              </div>
            </div>
          </div>
        </div>

        <Separator />

        {/* Citation Style */}
        <div className="space-y-3">
          <h4 className="font-medium text-sm flex items-center">
            <FileText className="w-4 h-4 mr-2" />
            Citations
          </h4>
          
          <div>
            <Label htmlFor="citation-style" className="text-xs">Citation Style</Label>
            <Select 
              value={settings.citation_style} 
              onValueChange={(value) => handleSettingChange('citation_style', value)}
            >
              <SelectTrigger className="h-8">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {citationStyles.map((style) => (
                  <SelectItem key={style.value} value={style.value}>
                    {style.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <Separator />

        {/* Display Options */}
        <div className="space-y-3">
          <h4 className="font-medium text-sm">Display Options</h4>
          
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label htmlFor="show-word-count" className="text-xs">Show word count</Label>
              <Switch
                id="show-word-count"
                checked={settings.show_word_count}
                onCheckedChange={(checked) => handleSettingChange('show_word_count', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="show-page-numbers" className="text-xs">Show page numbers</Label>
              <Switch
                id="show-page-numbers"
                checked={settings.show_page_numbers}
                onCheckedChange={(checked) => handleSettingChange('show_page_numbers', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="enable-spell-check" className="text-xs">Enable spell check</Label>
              <Switch
                id="enable-spell-check"
                checked={settings.enable_spell_check}
                onCheckedChange={(checked) => handleSettingChange('enable_spell_check', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="enable-grammar-check" className="text-xs">Enable grammar check</Label>
              <Switch
                id="enable-grammar-check"
                checked={settings.enable_grammar_check}
                onCheckedChange={(checked) => handleSettingChange('enable_grammar_check', checked)}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div className="p-4 border-t">
        <Button onClick={handleSaveSettings} className="w-full">
          <Save className="w-4 h-4 mr-2" />
          Save Settings
        </Button>
      </div>
    </div>
  );
}; 