import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { History, Clock, User, FileText, RotateCcw, Plus } from 'lucide-react';
import { DissertationVersion } from '@/types/dissertation';
import { formatDistanceToNow } from 'date-fns';

interface VersionHistoryProps {
  versions: DissertationVersion[];
  currentVersion: number;
  onCreateVersion: () => void;
  onRestoreVersion: (version: DissertationVersion) => void;
}

export const VersionHistory: React.FC<VersionHistoryProps> = ({
  versions,
  currentVersion,
  onCreateVersion,
  onRestoreVersion
}) => {
  const handlePreviewVersion = (version: DissertationVersion) => {
    // TODO: Implement version preview functionality
    console.log('Previewing version:', version.version_number);
  };

  const getWordCountChange = (currentVersion: DissertationVersion, previousVersion?: DissertationVersion) => {
    if (!previousVersion) return null;
    const change = currentVersion.word_count - previousVersion.word_count;
    return change;
  };

  // Sort versions by version number descending (newest first)
  const sortedVersions = [...versions].sort((a, b) => b.version_number - a.version_number);

  return (
    <div className="version-history h-full flex flex-col">
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between mb-2">
          <h3 className="font-semibold flex items-center">
            <History className="w-4 h-4 mr-2" />
            Version History
          </h3>
          <Button
            variant="outline"
            size="sm"
            onClick={onCreateVersion}
          >
            <Plus className="w-4 h-4 mr-1" />
            Create Version
          </Button>
        </div>
        <p className="text-xs text-gray-600">
          Current version: v{currentVersion}
        </p>
      </div>

      {/* Version List */}
      <ScrollArea className="flex-1">
        <div className="p-4 space-y-4">
          {sortedVersions.map((version, index) => {
            const previousVersion = sortedVersions[index + 1];
            const wordCountChange = getWordCountChange(version, previousVersion);
            const isCurrentVersion = version.version_number === currentVersion;

            return (
              <div 
                key={version.id} 
                className={`border rounded-lg p-3 ${isCurrentVersion ? 'bg-blue-50 border-blue-200' : 'bg-white'}`}
              >
                {/* Version Header */}
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <Badge variant={isCurrentVersion ? 'default' : 'outline'}>
                      v{version.version_number}
                    </Badge>
                    {isCurrentVersion && (
                      <Badge variant="secondary" className="text-xs">
                        Current
                      </Badge>
                    )}
                  </div>
                  
                  <div className="flex items-center space-x-1 text-xs text-gray-500">
                    <Clock className="w-3 h-3" />
                    <span>{formatDistanceToNow(version.created_at, { addSuffix: true })}</span>
                  </div>
                </div>

                {/* Change Summary */}
                <div className="mb-3">
                  <p className="text-sm text-gray-700 mb-2">{version.change_summary}</p>
                  
                  <div className="flex items-center space-x-4 text-xs text-gray-600">
                    <div className="flex items-center space-x-1">
                      <FileText className="w-3 h-3" />
                      <span>{version.word_count.toLocaleString()} words</span>
                      {wordCountChange !== null && (
                        <span className={wordCountChange > 0 ? 'text-green-600' : 'text-red-600'}>
                          ({wordCountChange > 0 ? '+' : ''}{wordCountChange})
                        </span>
                      )}
                    </div>
                    
                    <div>
                      <span>{version.page_count} pages</span>
                    </div>
                  </div>
                </div>

                {/* Version Actions */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-1 text-xs text-gray-500">
                    <User className="w-3 h-3" />
                    <span>by {version.created_by}</span>
                  </div>
                  
                  {!isCurrentVersion && (
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePreviewVersion(version)}
                        className="h-6 px-2 text-xs"
                      >
                        Preview
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onRestoreVersion(version)}
                        className="h-6 px-2 text-xs"
                      >
                        <RotateCcw className="w-3 h-3 mr-1" />
                        Restore
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            );
          })}

          {sortedVersions.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <History className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>No version history</p>
              <p className="text-sm">Create your first version to start tracking changes</p>
              <Button
                variant="outline"
                size="sm"
                onClick={onCreateVersion}
                className="mt-4"
              >
                <Plus className="w-4 h-4 mr-1" />
                Create First Version
              </Button>
            </div>
          )}
        </div>
      </ScrollArea>

      {/* Version Info */}
      <div className="p-4 border-t bg-gray-50">
        <div className="text-xs text-gray-600 space-y-1">
          <p>• Versions capture document snapshots</p>
          <p>• You can restore any previous version</p>
          <p>• Changes are tracked and summarized</p>
        </div>
      </div>
    </div>
  );
}; 