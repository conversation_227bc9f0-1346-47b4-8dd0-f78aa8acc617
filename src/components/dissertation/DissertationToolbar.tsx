import React, { useState, useRef } from 'react';
import { Editor } from '@tiptap/react';
import { But<PERSON> } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Bold, 
  Italic, 
  Underline, 
  Strikethrough,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  List,
  ListOrdered,
  Quote,
  Code,
  Link,
  Image,
  Table,
  Highlighter,
  Type,
  Palette,
  Undo,
  Redo,
  Plus,
  Minus,
  ChevronDown,
  Heading1,
  Heading2,
  Heading3,
  Pilcrow,
  Checkbox,
  Crop as CropIcon,
  Scissors
} from 'lucide-react';
import { Toggle } from '@/components/ui/toggle';
import { ImageCropper } from './ImageCropper';

interface DissertationToolbarProps {
  editor: Editor;
  onInsertPageBreak: () => void;
  onToggleReviewMode: () => void;
  isReviewMode: boolean;
}

export const DissertationToolbar: React.FC<DissertationToolbarProps> = ({
  editor,
  onInsertPageBreak,
  onToggleReviewMode,
  isReviewMode
}) => {
  const [showLinkDialog, setShowLinkDialog] = useState(false);
  const [linkUrl, setLinkUrl] = useState('');
  const [showTableDialog, setShowTableDialog] = useState(false);
  const [tableRows, setTableRows] = useState('3');
  const [tableCols, setTableCols] = useState('3');
  const [tableHeaderRow, setTableHeaderRow] = useState(true);
  const [showImageDialog, setShowImageDialog] = useState(false);
  const [imageUrl, setImageUrl] = useState('');
  const [imageUploadFile, setImageUploadFile] = useState<File | null>(null);
  const [imageAlt, setImageAlt] = useState('');
  const [imageTitle, setImageTitle] = useState('');
  const [imageAlignment, setImageAlignment] = useState('center');
  const [showCropper, setShowCropper] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const fontFamilies = [
    { value: 'Times New Roman', label: 'Times New Roman' },
    { value: 'Arial', label: 'Arial' },
    { value: 'Calibri', label: 'Calibri' },
    { value: 'Georgia', label: 'Georgia' },
    { value: 'Verdana', label: 'Verdana' },
    { value: 'Helvetica', label: 'Helvetica' },
  ];

  const fontSizes = [
    '8', '9', '10', '11', '12', '14', '16', '18', '20', '22', '24', '26', '28', '36', '48', '72'
  ];

  const headingLevels = [
    { value: 'paragraph', label: 'Normal Text' },
    { value: 'heading1', label: 'Heading 1' },
    { value: 'heading2', label: 'Heading 2' },
    { value: 'heading3', label: 'Heading 3' },
    { value: 'heading4', label: 'Heading 4' },
    { value: 'heading5', label: 'Heading 5' },
    { value: 'heading6', label: 'Heading 6' },
  ];

  const colors = [
    '#000000', '#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#FF00FF', '#00FFFF',
    '#800000', '#008000', '#000080', '#808000', '#800080', '#008080', '#C0C0C0',
    '#808080', '#9999FF', '#993366', '#FFFFCC', '#CCFFFF', '#660066', '#FF8080',
    '#0066CC', '#CCCCFF', '#000080', '#FF00FF', '#FFFF00', '#00FFFF', '#800080'
  ];

  const handleSetLink = () => {
    if (linkUrl) {
      editor.chain().focus().setLink({ href: linkUrl }).run();
      setLinkUrl('');
      setShowLinkDialog(false);
    }
  };

  const handleInsertTable = () => {
    const rows = parseInt(tableRows);
    const cols = parseInt(tableCols);
    if (rows > 0 && cols > 0) {
      // Insert table with proper styling
      editor.chain().focus()
        .insertTable({ 
          rows, 
          cols, 
          withHeaderRow: tableHeaderRow 
        })
        .run();
      
      // Give time for the table to render then add proper styling
      setTimeout(() => {
        const tables = document.querySelectorAll('.dissertation-table');
        tables.forEach(table => {
          (table as HTMLElement).style.width = '100%';
          (table as HTMLElement).style.borderCollapse = 'collapse';
          
          // Ensure all cells have borders
          const cells = table.querySelectorAll('td, th');
          cells.forEach(cell => {
            (cell as HTMLElement).style.border = '1px solid #000';
            (cell as HTMLElement).style.padding = '6pt 8pt';
          });
        });
      }, 100);
      
      setShowTableDialog(false);
      setTableRows('3');
      setTableCols('3');
      setTableHeaderRow(true);
    }
  };

  const handleHeadingChange = (value: string) => {
    if (value === 'paragraph') {
      editor.chain().focus().setParagraph().run();
    } else {
      const level = parseInt(value.replace('heading', '')) as 1 | 2 | 3 | 4 | 5 | 6;
      editor.chain().focus().toggleHeading({ level }).run();
    }
  };

  const handleFontFamilyChange = (fontFamily: string) => {
    editor.chain().focus().setFontFamily(fontFamily).run();
  };

  const handleFontSizeChange = (size: string) => {
    // Apply font size using CSS style
    editor.chain().focus().setMark('textStyle', { fontSize: `${size}pt` }).run();
  };

  const handleColorChange = (color: string) => {
    editor.chain().focus().setColor(color).run();
  };

  // Handle image upload from system
  const handleImageUpload = () => {
    setShowImageDialog(true);
    // Reset states
    setImageUploadFile(null);
    setImageUrl('');
    setImageAlt('');
    setImageTitle('');
    setImageAlignment('center');
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setImageUploadFile(file);
      setImageTitle(file.name);
      // Clear the URL field if a file is selected
      setImageUrl('');
      // Show cropper for the selected image
      setShowCropper(true);
    }
  };

  const handleBrowseClick = () => {
    fileInputRef.current?.click();
  };

  const handleInsertImage = () => {
    if (imageUploadFile) {
      // Insert image from file
      const reader = new FileReader();
      reader.onload = (e) => {
        const src = e.target?.result as string;
        if (src) {
          editor.chain().focus()
            .setImage({ 
              src,
              alt: imageAlt || imageTitle || imageUploadFile.name,
              title: imageTitle || imageUploadFile.name
            })
            .run();
          
          // Reset the form and close dialog
          setImageUploadFile(null);
          setImageUrl('');
          setImageAlt('');
          setImageTitle('');
          setShowImageDialog(false);
          
          // Add alignment class if needed
          setTimeout(() => {
            const images = document.querySelectorAll('.dissertation-image');
            const lastImage = images[images.length - 1];
            if (lastImage && imageAlignment !== 'center') {
              (lastImage as HTMLElement).style.marginLeft = imageAlignment === 'left' ? '0' : 'auto';
              (lastImage as HTMLElement).style.marginRight = imageAlignment === 'right' ? '0' : 'auto';
            }
          }, 100);
        }
      };
      reader.readAsDataURL(imageUploadFile);
    } else if (imageUrl && imageUrl.trim() !== '' && imageUrl !== 'https://') {
      // Insert image from URL
      editor.chain().focus()
        .setImage({ 
          src: imageUrl,
          alt: imageAlt || imageTitle || 'Image',
          title: imageTitle || imageUrl
        })
        .run();
      
      // Reset the form and close dialog
      setImageUploadFile(null);
      setImageUrl('');
      setImageAlt('');
      setImageTitle('');
      setShowImageDialog(false);
      
      // Add alignment class if needed
      setTimeout(() => {
        const images = document.querySelectorAll('.dissertation-image');
        const lastImage = images[images.length - 1];
        if (lastImage && imageAlignment !== 'center') {
          (lastImage as HTMLElement).style.marginLeft = imageAlignment === 'left' ? '0' : 'auto';
          (lastImage as HTMLElement).style.marginRight = imageAlignment === 'right' ? '0' : 'auto';
        }
      }, 100);
    } else {
      // If no image is selected, prompt user to browse
      handleBrowseClick();
    }
  };

  // Table operations
  const handleAddTableColumn = (position: 'before' | 'after') => {
    if (position === 'before') {
      editor.chain().focus().addColumnBefore().run();
    } else {
      editor.chain().focus().addColumnAfter().run();
    }
  };

  const handleAddTableRow = (position: 'before' | 'after') => {
    if (position === 'before') {
      editor.chain().focus().addRowBefore().run();
    } else {
      editor.chain().focus().addRowAfter().run();
    }
  };

  const handleDeleteTableColumn = () => {
    editor.chain().focus().deleteColumn().run();
  };

  const handleDeleteTableRow = () => {
    editor.chain().focus().deleteRow().run();
  };

  const handleMergeCells = () => {
    editor.chain().focus().mergeCells().run();
  };

  const handleSplitCell = () => {
    editor.chain().focus().splitCell().run();
  };

  const handleToggleHeaderColumn = () => {
    editor.chain().focus().toggleHeaderColumn().run();
  };

  const handleToggleHeaderRow = () => {
    editor.chain().focus().toggleHeaderRow().run();
  };

  const handleToggleHeaderCell = () => {
    editor.chain().focus().toggleHeaderCell().run();
  };

  // Handle cropped image
  const handleCropComplete = (croppedImage: Blob) => {
    // Create a new File object from the Blob
    const croppedFile = new File(
      [croppedImage], 
      imageUploadFile?.name || 'cropped-image.jpg', 
      { type: 'image/jpeg' }
    );
    
    // Update the image upload file with the cropped version
    setImageUploadFile(croppedFile);
    setShowCropper(false);
    
    // Create a preview URL for the cropped image
    const reader = new FileReader();
    reader.onload = (e) => {
      if (e.target?.result) {
        const dataUrl = e.target.result as string;
        // You could set a preview URL here if needed
      }
    };
    reader.readAsDataURL(croppedFile);
  };
  
  // Handle crop cancel
  const handleCropCancel = () => {
    setShowCropper(false);
  };

  return (
    <div className="dissertation-toolbar border-b bg-white p-3">
      <div className="flex items-center space-x-2 flex-wrap">
        {/* Undo/Redo */}
        <div className="flex items-center space-x-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().undo().run()}
            disabled={!editor.can().undo()}
            className="h-8 w-8 p-0"
            title="Undo"
          >
            <Undo className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().redo().run()}
            disabled={!editor.can().redo()}
            className="h-8 w-8 p-0"
            title="Redo"
          >
            <Redo className="h-4 w-4" />
          </Button>
        </div>

        <Separator orientation="vertical" className="h-6" />

        {/* Font Family */}
        <Select onValueChange={handleFontFamilyChange} defaultValue="Times New Roman">
          <SelectTrigger className="w-40 h-8">
            <SelectValue placeholder="Font" />
          </SelectTrigger>
          <SelectContent>
            {fontFamilies.map((font) => (
              <SelectItem key={font.value} value={font.value}>
                <span style={{ fontFamily: font.value }}>{font.label}</span>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Font Size */}
        <Select onValueChange={handleFontSizeChange} defaultValue="12">
          <SelectTrigger className="w-16 h-8">
            <SelectValue placeholder="12" />
          </SelectTrigger>
          <SelectContent>
            {fontSizes.map((size) => (
              <SelectItem key={size} value={size}>
                {size}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Separator orientation="vertical" className="h-6" />

        {/* Heading Styles */}
        <Select onValueChange={handleHeadingChange}>
          <SelectTrigger className="w-36 h-8">
            <SelectValue placeholder="Style" />
          </SelectTrigger>
          <SelectContent>
            {headingLevels.map((heading) => (
              <SelectItem key={heading.value} value={heading.value}>
                {heading.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Separator orientation="vertical" className="h-6" />

        {/* Text Formatting */}
        <div className="flex items-center space-x-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().toggleBold().run()}
            className={`h-8 w-8 p-0 ${editor.isActive('bold') ? 'bg-gray-200' : ''}`}
            title="Bold"
          >
            <Bold className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().toggleItalic().run()}
            className={`h-8 w-8 p-0 ${editor.isActive('italic') ? 'bg-gray-200' : ''}`}
            title="Italic"
          >
            <Italic className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().toggleUnderline().run()}
            className={`h-8 w-8 p-0 ${editor.isActive('underline') ? 'bg-gray-200' : ''}`}
            title="Underline"
          >
            <Underline className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().toggleStrike().run()}
            className={`h-8 w-8 p-0 ${editor.isActive('strike') ? 'bg-gray-200' : ''}`}
            title="Strikethrough"
          >
            <Strikethrough className="h-4 w-4" />
          </Button>
        </div>

        <Separator orientation="vertical" className="h-6" />

        {/* Text Color */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0" title="Text Color">
              <Palette className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-48">
            <div className="p-2">
              <div className="text-sm font-medium mb-2">Text Color</div>
              <div className="grid grid-cols-7 gap-1">
                {colors.map((color) => (
                  <button
                    key={color}
                    className="w-6 h-6 rounded border border-gray-300 hover:scale-110 transition-transform"
                    style={{ backgroundColor: color }}
                    onClick={() => handleColorChange(color)}
                    title={color}
                  />
                ))}
              </div>
            </div>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Highlight */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleHighlight().run()}
          className={`h-8 w-8 p-0 ${editor.isActive('highlight') ? 'bg-gray-200' : ''}`}
          title="Highlight"
        >
          <Highlighter className="h-4 w-4" />
        </Button>

        <Separator orientation="vertical" className="h-6" />

        {/* Text Alignment */}
        <div className="flex items-center space-x-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().setTextAlign('left').run()}
            className={`h-8 w-8 p-0 ${editor.isActive({ textAlign: 'left' }) ? 'bg-gray-200' : ''}`}
            title="Align Left"
          >
            <AlignLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().setTextAlign('center').run()}
            className={`h-8 w-8 p-0 ${editor.isActive({ textAlign: 'center' }) ? 'bg-gray-200' : ''}`}
            title="Align Center"
          >
            <AlignCenter className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().setTextAlign('right').run()}
            className={`h-8 w-8 p-0 ${editor.isActive({ textAlign: 'right' }) ? 'bg-gray-200' : ''}`}
            title="Align Right"
          >
            <AlignRight className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().setTextAlign('justify').run()}
            className={`h-8 w-8 p-0 ${editor.isActive({ textAlign: 'justify' }) ? 'bg-gray-200' : ''}`}
            title="Justify"
          >
            <AlignJustify className="h-4 w-4" />
          </Button>
        </div>

        <Separator orientation="vertical" className="h-6" />

        {/* Lists */}
        <div className="flex items-center space-x-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().toggleBulletList().run()}
            className={`h-8 w-8 p-0 ${editor.isActive('bulletList') ? 'bg-gray-200' : ''}`}
            title="Bullet List"
          >
            <List className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().toggleOrderedList().run()}
            className={`h-8 w-8 p-0 ${editor.isActive('orderedList') ? 'bg-gray-200' : ''}`}
            title="Numbered List"
          >
            <ListOrdered className="h-4 w-4" />
          </Button>
        </div>

        <Separator orientation="vertical" className="h-6" />

        {/* Insert Elements */}
        <div className="flex items-center space-x-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowLinkDialog(true)}
            className="h-8 w-8 p-0"
            title="Insert Link"
          >
            <Link className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleImageUpload}
            className="h-8 w-8 p-0"
            title="Insert Image"
          >
            <Image className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowTableDialog(true)}
            className="h-8 w-8 p-0"
            title="Insert Table"
          >
            <Table className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().toggleBlockquote().run()}
            className={`h-8 w-8 p-0 ${editor.isActive('blockquote') ? 'bg-gray-200' : ''}`}
            title="Quote"
          >
            <Quote className="h-4 w-4" />
          </Button>
        </div>

        {/* Table operations context menu - only visible when table is selected */}
        {editor.isActive('table') && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 p-1">
                <span className="text-xs mr-1">Table</span>
                <ChevronDown className="h-3 w-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuLabel>Table Operations</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => handleAddTableRow('before')}>
                Add Row Above
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleAddTableRow('after')}>
                Add Row Below
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleAddTableColumn('before')}>
                Add Column Before
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleAddTableColumn('after')}>
                Add Column After
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleDeleteTableRow}>
                Delete Row
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleDeleteTableColumn}>
                Delete Column
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleMergeCells}>
                Merge Cells
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleSplitCell}>
                Split Cell
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleToggleHeaderRow}>
                Toggle Header Row
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleToggleHeaderColumn}>
                Toggle Header Column
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleToggleHeaderCell}>
                Toggle Header Cell
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}

        {/* Image operations context menu - only visible when image is selected */}
        {editor.isActive('image') && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 p-1">
                <span className="text-xs mr-1">Image</span>
                <ChevronDown className="h-3 w-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuLabel>Image Operations</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => {
                const node = editor.state.selection.node;
                if (node && node.type.name === 'image') {
                  // Set image alignment to left
                  const imgs = document.querySelectorAll('.ProseMirror-selectednode');
                  imgs.forEach(img => {
                    (img as HTMLElement).style.marginLeft = '0';
                    (img as HTMLElement).style.marginRight = 'auto';
                  });
                }
              }}>
                Align Left
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => {
                const node = editor.state.selection.node;
                if (node && node.type.name === 'image') {
                  // Set image alignment to center
                  const imgs = document.querySelectorAll('.ProseMirror-selectednode');
                  imgs.forEach(img => {
                    (img as HTMLElement).style.marginLeft = 'auto';
                    (img as HTMLElement).style.marginRight = 'auto';
                  });
                }
              }}>
                Align Center
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => {
                const node = editor.state.selection.node;
                if (node && node.type.name === 'image') {
                  // Set image alignment to right
                  const imgs = document.querySelectorAll('.ProseMirror-selectednode');
                  imgs.forEach(img => {
                    (img as HTMLElement).style.marginLeft = 'auto';
                    (img as HTMLElement).style.marginRight = '0';
                  });
                }
              }}>
                Align Right
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => {
                editor.chain().focus().deleteSelection().run();
              }}>
                Remove Image
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>

      {/* Link Dialog */}
      {showLinkDialog && (
        <div className="mt-3 p-3 border rounded-md bg-gray-50">
          <div className="flex items-center space-x-2">
            <Label htmlFor="link-url" className="text-sm font-medium">
              URL:
            </Label>
            <Input
              id="link-url"
              type="url"
              placeholder="https://example.com"
              value={linkUrl}
              onChange={(e) => setLinkUrl(e.target.value)}
              className="flex-1"
            />
            <Button size="sm" onClick={handleSetLink}>
              Insert
            </Button>
            <Button size="sm" variant="outline" onClick={() => setShowLinkDialog(false)}>
              Cancel
            </Button>
          </div>
        </div>
      )}

      {/* Table Dialog */}
      <Dialog open={showTableDialog} onOpenChange={setShowTableDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Insert Table</DialogTitle>
            <DialogDescription>
              Choose the number of rows and columns for your table.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="table-rows">Rows</Label>
                <Select value={tableRows} onValueChange={setTableRows}>
                  <SelectTrigger id="table-rows">
                    <SelectValue placeholder="Select rows" />
                  </SelectTrigger>
                  <SelectContent>
                    {Array.from({ length: 20 }, (_, i) => i + 1).map(num => (
                      <SelectItem key={num} value={num.toString()}>
                        {num}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="table-cols">Columns</Label>
                <Select value={tableCols} onValueChange={setTableCols}>
                  <SelectTrigger id="table-cols">
                    <SelectValue placeholder="Select columns" />
                  </SelectTrigger>
                  <SelectContent>
                    {Array.from({ length: 10 }, (_, i) => i + 1).map(num => (
                      <SelectItem key={num} value={num.toString()}>
                        {num}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="header-row"
                checked={tableHeaderRow}
                onChange={(e) => setTableHeaderRow(e.target.checked)}
                className="rounded"
              />
              <Label htmlFor="header-row" className="text-sm">
                Include header row
              </Label>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowTableDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleInsertTable}>
              Insert Table
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Image Dialog */}
      <Dialog open={showImageDialog} onOpenChange={setShowImageDialog}>
        <DialogContent className={showCropper ? "sm:max-w-xl" : "sm:max-w-md"}>
          <DialogHeader>
            <DialogTitle>{showCropper ? "Crop Image" : "Insert Image"}</DialogTitle>
            <DialogDescription>
              {showCropper 
                ? "Adjust the crop area, rotation, and scale of your image." 
                : "Choose how you want to insert the image."}
            </DialogDescription>
          </DialogHeader>
          
          {showCropper && imageUploadFile ? (
            <ImageCropper 
              image={imageUploadFile} 
              onCropComplete={handleCropComplete} 
              onCancel={handleCropCancel} 
            />
          ) : (
            <div className="grid gap-4 py-4">
              <div className="flex items-center justify-center space-x-4">
                <Button 
                  variant={imageUrl === '' ? "default" : "outline"} 
                  onClick={() => {
                    setImageUrl('');
                    setTimeout(() => handleBrowseClick(), 100);
                  }}
                  className="flex-1"
                >
                  From Computer
                </Button>
                <Button 
                  variant={imageUrl !== '' ? "default" : "outline"} 
                  onClick={() => {
                    setImageUrl('https://');
                    setImageUploadFile(null);
                  }}
                  className="flex-1"
                >
                  From URL
                </Button>
              </div>

              {/* Hidden file input for image upload */}
              <input
                type="file"
                accept="image/*"
                onChange={handleFileSelect}
                ref={fileInputRef}
                style={{ display: 'none' }}
              />

              {imageUrl === '' && !imageUploadFile && (
                <div className="flex items-center space-x-2">
                  <Button onClick={handleBrowseClick} className="w-full">
                    Browse for image
                  </Button>
                </div>
              )}

              {imageUrl !== '' && (
                <div className="flex items-center space-x-2">
                  <Label htmlFor="image-url" className="text-sm font-medium">
                    URL:
                  </Label>
                  <Input
                    id="image-url"
                    type="url"
                    placeholder="https://example.com/image.jpg"
                    value={imageUrl}
                    onChange={(e) => setImageUrl(e.target.value)}
                    className="flex-1"
                  />
                </div>
              )}

              {imageUploadFile && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="text-sm font-medium">Selected File:</Label>
                    <span className="text-sm">{imageUploadFile.name}</span>
                  </div>
                  <div className="border rounded-md p-2 bg-gray-50">
                    {(() => {
                      const previewUrl = URL.createObjectURL(imageUploadFile);
                      return (
                        <img 
                          src={previewUrl}
                          alt="Preview" 
                          className="max-h-32 mx-auto"
                          onLoad={() => {
                            // Add a cleanup function for the component
                            const cleanup = () => {
                              URL.revokeObjectURL(previewUrl);
                            };
                            // Clean up when dialog closes
                            const handleDialogClose = () => {
                              if (!showImageDialog) {
                                cleanup();
                              }
                            };
                            handleDialogClose();
                            return cleanup;
                          }}
                        />
                      );
                    })()}
                  </div>
                  <div className="flex space-x-2">
                    <Button 
                      variant="outline" 
                      size="sm"
                      className="flex-1"
                      onClick={() => {
                        setImageUploadFile(null);
                        handleBrowseClick();
                      }}
                    >
                      Change Image
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm"
                      className="flex-1"
                      onClick={() => setShowCropper(true)}
                    >
                      <CropIcon className="w-4 h-4 mr-1" />
                      Crop Image
                    </Button>
                  </div>
                </div>
              )}

              <div className="flex items-center space-x-2">
                <Label htmlFor="image-alt" className="text-sm font-medium">
                  Alt Text:
                </Label>
                <Input
                  id="image-alt"
                  type="text"
                  placeholder="Description of the image"
                  value={imageAlt}
                  onChange={(e) => setImageAlt(e.target.value)}
                  className="flex-1"
                />
              </div>

              <div className="flex items-center space-x-2">
                <Label htmlFor="image-title" className="text-sm font-medium">
                  Title:
                </Label>
                <Input
                  id="image-title"
                  type="text"
                  placeholder="Title of the image"
                  value={imageTitle}
                  onChange={(e) => setImageTitle(e.target.value)}
                  className="flex-1"
                />
              </div>

              <div className="flex items-center space-x-2">
                <Label htmlFor="image-alignment" className="text-sm font-medium">
                  Alignment:
                </Label>
                <Select value={imageAlignment} onValueChange={setImageAlignment}>
                  <SelectTrigger id="image-alignment">
                    <SelectValue placeholder="Select alignment" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="left">Left</SelectItem>
                    <SelectItem value="center">Center</SelectItem>
                    <SelectItem value="right">Right</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}
          
          {!showCropper && (
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowImageDialog(false)}>
                Cancel
              </Button>
              <Button onClick={handleInsertImage}>
                Insert Image
              </Button>
            </DialogFooter>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}; 