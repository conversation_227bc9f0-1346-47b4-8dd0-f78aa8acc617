import React, { useState, useEffect, useRef } from 'react';
import { NodeViewWrapper, NodeViewContent, NodeViewProps } from '@tiptap/react';

export const DraggableTable: React.FC<NodeViewProps> = (props) => {
  const [isDragging, setIsDragging] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [startPosition, setStartPosition] = useState({ x: 0, y: 0 });
  const [size, setSize] = useState({
    width: props.node.attrs.width || '100%',
    height: props.node.attrs.height || 'auto',
  });
  const tableRef = useRef<HTMLDivElement>(null);

  // Set initial position based on attrs if available
  useEffect(() => {
    if (props.node.attrs.x !== undefined && props.node.attrs.y !== undefined) {
      setPosition({
        x: props.node.attrs.x,
        y: props.node.attrs.y
      });
    }
  }, [props.node.attrs]);

  // Handle mouse down for dragging
  const handleMouseDown = (e: React.MouseEvent) => {
    if (props.editor.isEditable && !isResizing && e.target === tableRef.current) {
      e.preventDefault();
      setIsDragging(true);
      setStartPosition({
        x: e.clientX - position.x,
        y: e.clientY - position.y
      });
    }
  };

  // Handle mouse move for dragging
  const handleMouseMove = (e: MouseEvent) => {
    if (isDragging && !isResizing) {
      const newX = e.clientX - startPosition.x;
      const newY = e.clientY - startPosition.y;
      
      setPosition({ x: newX, y: newY });
      
      // Update node attributes with new position
      props.updateAttributes({
        x: newX,
        y: newY
      });
    }
  };

  // Handle mouse up to stop dragging
  const handleMouseUp = () => {
    setIsDragging(false);
    setIsResizing(false);
  };

  // Add and remove event listeners for dragging
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    } else {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    }
    
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging]);

  // Handle resize start
  const handleResizeStart = (e: React.MouseEvent, direction: string) => {
    e.preventDefault();
    e.stopPropagation();
    setIsResizing(true);
    setStartPosition({
      x: e.clientX,
      y: e.clientY
    });
  };

  // Handle resize
  const handleResize = (e: MouseEvent) => {
    if (isResizing && tableRef.current) {
      const startWidth = tableRef.current.offsetWidth;
      const startX = startPosition.x;
      
      const currentX = e.clientX;
      const widthChange = currentX - startX;
      
      let newWidth = startWidth + widthChange;
      
      // Minimum size constraints
      if (newWidth < 200) {
        newWidth = 200;
      }
      
      // Maximum size constraints (container width)
      const containerWidth = tableRef.current.parentElement?.offsetWidth || window.innerWidth;
      if (newWidth > containerWidth - 40) {
        newWidth = containerWidth - 40;
      }
      
      setSize({
        ...size,
        width: newWidth
      });
      
      // Update node attributes with new dimensions
      props.updateAttributes({
        width: newWidth
      });
      
      // Update start position for continuous resizing
      setStartPosition({
        x: currentX,
        y: startPosition.y
      });
    }
  };

  // Add and remove event listeners for resizing
  useEffect(() => {
    if (isResizing) {
      document.addEventListener('mousemove', handleResize);
      document.addEventListener('mouseup', handleMouseUp);
    } else {
      document.removeEventListener('mousemove', handleResize);
      document.removeEventListener('mouseup', handleMouseUp);
    }
    
    return () => {
      document.removeEventListener('mousemove', handleResize);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isResizing]);

  return (
    <NodeViewWrapper 
      className="draggable-table-wrapper" 
      ref={tableRef}
      data-drag-handle
      onMouseDown={handleMouseDown}
    >
      <div 
        className={`draggable-table-container ${isDragging ? 'dragging' : ''} ${isResizing ? 'resizing' : ''}`}
        style={{
          position: 'relative',
          width: typeof size.width === 'number' ? `${size.width}px` : size.width,
          cursor: isDragging ? 'grabbing' : 'default'
        }}
      >
        {/* Table header for dragging */}
        {props.editor.isEditable && (
          <div 
            className="table-drag-handle"
            style={{
              position: 'absolute',
              top: '-20px',
              left: '0',
              right: '0',
              height: '20px',
              background: '#f0f0f0',
              borderTopLeftRadius: '4px',
              borderTopRightRadius: '4px',
              borderBottom: '1px solid #ddd',
              cursor: 'grab',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '10px',
              color: '#666'
            }}
          >
            Drag table • Click to select
          </div>
        )}
        
        {/* Table content */}
        <NodeViewContent className="dissertation-table" />
        
        {/* Resize handles */}
        {props.editor.isEditable && (
          <>
            {/* Right resize handle */}
            <div
              className="resize-handle-right"
              onMouseDown={(e) => handleResizeStart(e, 'right')}
              style={{
                position: 'absolute',
                top: '0',
                bottom: '0',
                right: '-5px',
                width: '10px',
                cursor: 'col-resize',
                zIndex: 10
              }}
            />
            
            {/* Bottom-right corner resize handle */}
            <div
              className="resize-handle-corner"
              onMouseDown={(e) => handleResizeStart(e, 'corner')}
              style={{
                position: 'absolute',
                bottom: '-5px',
                right: '-5px',
                width: '10px',
                height: '10px',
                background: '#0077cc',
                cursor: 'nwse-resize',
                borderRadius: '50%',
                zIndex: 11
              }}
            />
          </>
        )}
      </div>
    </NodeViewWrapper>
  );
}; 