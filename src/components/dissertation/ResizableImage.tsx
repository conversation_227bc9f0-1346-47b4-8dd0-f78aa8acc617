import React, { useState, useEffect, useRef } from 'react';
import { NodeViewWrapper, NodeViewProps } from '@tiptap/react';

export const ResizableImage: React.FC<NodeViewProps> = (props) => {
  const [size, setSize] = useState({
    width: props.node.attrs.width || 'auto',
    height: props.node.attrs.height || 'auto',
  });
  const [isDragging, setIsDragging] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [startPosition, setStartPosition] = useState({ x: 0, y: 0 });
  const [showContextMenu, setShowContextMenu] = useState(false);
  const [contextMenuPosition, setContextMenuPosition] = useState({ x: 0, y: 0 });
  const imageRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const contextMenuRef = useRef<HTMLDivElement>(null);

  // Set initial position based on attrs if available
  useEffect(() => {
    if (props.node.attrs.x !== undefined && props.node.attrs.y !== undefined) {
      setPosition({
        x: props.node.attrs.x,
        y: props.node.attrs.y
      });
    }
  }, [props.node.attrs]);

  // Handle image load to get natural dimensions
  const handleImageLoad = () => {
    if (imageRef.current && !props.node.attrs.width) {
      // If no width is specified, use natural width but cap at container width
      const maxWidth = containerRef.current?.clientWidth || 500;
      const naturalWidth = imageRef.current.naturalWidth;
      const width = Math.min(naturalWidth, maxWidth);
      const height = (imageRef.current.naturalHeight / imageRef.current.naturalWidth) * width;
      
      setSize({ width, height });
      
      // Update node attributes with new dimensions
      props.updateAttributes({
        width,
        height
      });
    }
  };

  // Handle mouse down for dragging
  const handleMouseDown = (e: React.MouseEvent) => {
    if (props.editor.isEditable && !isResizing) {
      e.preventDefault();
      setIsDragging(true);
      setStartPosition({
        x: e.clientX - position.x,
        y: e.clientY - position.y
      });
    }
  };

  // Handle mouse move for dragging
  const handleMouseMove = (e: MouseEvent) => {
    if (isDragging && !isResizing) {
      const newX = e.clientX - startPosition.x;
      const newY = e.clientY - startPosition.y;
      
      setPosition({ x: newX, y: newY });
      
      // Update node attributes with new position
      props.updateAttributes({
        x: newX,
        y: newY
      });
    }
  };

  // Handle mouse up to stop dragging
  const handleMouseUp = () => {
    setIsDragging(false);
    setIsResizing(false);
  };

  // Add and remove event listeners for dragging
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    } else {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    }
    
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging]);

  // Handle resize start
  const handleResizeStart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsResizing(true);
  };

  // Handle resize
  const handleResize = (e: MouseEvent) => {
    if (isResizing && imageRef.current) {
      const startWidth = size.width as number;
      const startHeight = size.height as number;
      const startX = startPosition.x;
      const startY = startPosition.y;
      
      const currentX = e.clientX;
      const currentY = e.clientY;
      
      const widthChange = currentX - startX;
      const heightChange = currentY - startY;
      
      const aspectRatio = startWidth / startHeight;
      
      // Maintain aspect ratio
      let newWidth = startWidth + widthChange;
      let newHeight = newWidth / aspectRatio;
      
      // Minimum size constraints
      if (newWidth < 100) {
        newWidth = 100;
        newHeight = newWidth / aspectRatio;
      }
      
      setSize({
        width: newWidth,
        height: newHeight
      });
      
      // Update node attributes with new dimensions
      props.updateAttributes({
        width: newWidth,
        height: newHeight
      });
    }
  };

  // Add and remove event listeners for resizing
  useEffect(() => {
    if (isResizing) {
      document.addEventListener('mousemove', handleResize);
      document.addEventListener('mouseup', handleMouseUp);
    } else {
      document.removeEventListener('mousemove', handleResize);
      document.removeEventListener('mouseup', handleMouseUp);
    }
    
    return () => {
      document.removeEventListener('mousemove', handleResize);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isResizing]);

  // Handle right click to show context menu
  const handleContextMenu = (e: React.MouseEvent) => {
    if (props.editor.isEditable) {
      e.preventDefault();
      setContextMenuPosition({ x: e.clientX, y: e.clientY });
      setShowContextMenu(true);
    }
  };

  // Close context menu when clicking outside
  const handleClickOutside = (e: MouseEvent) => {
    if (contextMenuRef.current && !contextMenuRef.current.contains(e.target as Node)) {
      setShowContextMenu(false);
    }
  };

  // Add and remove event listeners for context menu
  useEffect(() => {
    if (showContextMenu) {
      document.addEventListener('click', handleClickOutside);
    } else {
      document.removeEventListener('click', handleClickOutside);
    }
    
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [showContextMenu]);

  // Context menu actions
  const handleDeleteImage = () => {
    props.deleteNode();
    setShowContextMenu(false);
  };

  const handleAlignLeft = () => {
    const img = imageRef.current;
    if (img) {
      img.style.marginLeft = '0';
      img.style.marginRight = 'auto';
    }
    setShowContextMenu(false);
  };

  const handleAlignCenter = () => {
    const img = imageRef.current;
    if (img) {
      img.style.marginLeft = 'auto';
      img.style.marginRight = 'auto';
    }
    setShowContextMenu(false);
  };

  const handleAlignRight = () => {
    const img = imageRef.current;
    if (img) {
      img.style.marginLeft = 'auto';
      img.style.marginRight = '0';
    }
    setShowContextMenu(false);
  };

  const handleResetSize = () => {
    if (imageRef.current) {
      const naturalWidth = imageRef.current.naturalWidth;
      const naturalHeight = imageRef.current.naturalHeight;
      
      // Cap at container width
      const maxWidth = containerRef.current?.clientWidth || 500;
      const width = Math.min(naturalWidth, maxWidth);
      const height = (naturalHeight / naturalWidth) * width;
      
      setSize({ width, height });
      
      // Update node attributes with new dimensions
      props.updateAttributes({
        width,
        height
      });
    }
    setShowContextMenu(false);
  };

  return (
    <NodeViewWrapper className="resizable-image-wrapper" ref={containerRef}>
      <div 
        className={`resizable-image-container ${isDragging ? 'dragging' : ''} ${isResizing ? 'resizing' : ''}`}
        style={{
          position: 'relative',
          display: 'inline-block',
          cursor: isDragging ? 'grabbing' : 'grab'
        }}
      >
        <img
          ref={imageRef}
          src={props.node.attrs.src}
          alt={props.node.attrs.alt || ''}
          title={props.node.attrs.title || ''}
          className="dissertation-image"
          style={{
            width: size.width,
            height: size.height,
            maxWidth: '100%',
            display: 'block'
          }}
          onLoad={handleImageLoad}
          onMouseDown={handleMouseDown}
          onContextMenu={handleContextMenu}
          draggable={false}
        />
        
        {/* Resize handle */}
        {props.editor.isEditable && (
          <div
            className="resize-handle"
            onMouseDown={(e) => {
              setStartPosition({
                x: e.clientX,
                y: e.clientY
              });
              handleResizeStart(e);
            }}
            style={{
              position: 'absolute',
              bottom: '2px',
              right: '2px',
              width: '10px',
              height: '10px',
              background: '#0077cc',
              cursor: 'nwse-resize',
              borderRadius: '2px'
            }}
          />
        )}

        {/* Context Menu */}
        {showContextMenu && props.editor.isEditable && (
          <div
            ref={contextMenuRef}
            className="image-context-menu"
            style={{
              position: 'fixed',
              top: `${contextMenuPosition.y}px`,
              left: `${contextMenuPosition.x}px`,
              zIndex: 1000,
              backgroundColor: 'white',
              border: '1px solid #ccc',
              borderRadius: '4px',
              boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
              padding: '4px 0'
            }}
          >
            <div 
              onClick={handleAlignLeft}
              style={{
                padding: '8px 16px',
                cursor: 'pointer',
                fontSize: '14px',
                whiteSpace: 'nowrap',
                userSelect: 'none',
                hoverBgColor: '#f5f5f5'
              }}
              onMouseOver={(e) => (e.currentTarget.style.backgroundColor = '#f5f5f5')}
              onMouseOut={(e) => (e.currentTarget.style.backgroundColor = 'transparent')}
            >
              Align Left
            </div>
            <div 
              onClick={handleAlignCenter}
              style={{
                padding: '8px 16px',
                cursor: 'pointer',
                fontSize: '14px',
                whiteSpace: 'nowrap',
                userSelect: 'none'
              }}
              onMouseOver={(e) => (e.currentTarget.style.backgroundColor = '#f5f5f5')}
              onMouseOut={(e) => (e.currentTarget.style.backgroundColor = 'transparent')}
            >
              Align Center
            </div>
            <div 
              onClick={handleAlignRight}
              style={{
                padding: '8px 16px',
                cursor: 'pointer',
                fontSize: '14px',
                whiteSpace: 'nowrap',
                userSelect: 'none'
              }}
              onMouseOver={(e) => (e.currentTarget.style.backgroundColor = '#f5f5f5')}
              onMouseOut={(e) => (e.currentTarget.style.backgroundColor = 'transparent')}
            >
              Align Right
            </div>
            <hr style={{ margin: '4px 0', border: 'none', borderTop: '1px solid #eee' }} />
            <div 
              onClick={handleResetSize}
              style={{
                padding: '8px 16px',
                cursor: 'pointer',
                fontSize: '14px',
                whiteSpace: 'nowrap',
                userSelect: 'none'
              }}
              onMouseOver={(e) => (e.currentTarget.style.backgroundColor = '#f5f5f5')}
              onMouseOut={(e) => (e.currentTarget.style.backgroundColor = 'transparent')}
            >
              Reset Size
            </div>
            <hr style={{ margin: '4px 0', border: 'none', borderTop: '1px solid #eee' }} />
            <div 
              onClick={handleDeleteImage}
              style={{
                padding: '8px 16px',
                cursor: 'pointer',
                fontSize: '14px',
                color: '#e11d48',
                whiteSpace: 'nowrap',
                userSelect: 'none'
              }}
              onMouseOver={(e) => (e.currentTarget.style.backgroundColor = '#f5f5f5')}
              onMouseOut={(e) => (e.currentTarget.style.backgroundColor = 'transparent')}
            >
              Delete Image
            </div>
          </div>
        )}
      </div>
    </NodeViewWrapper>
  );
}; 