import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Save, Clock, CheckCircle, AlertCircle } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

interface AutoSaveIndicatorProps {
  isAutoSaving: boolean;
  lastSaved: Date;
  hasUnsavedChanges: boolean;
}

export const AutoSaveIndicator: React.FC<AutoSaveIndicatorProps> = ({
  isAutoSaving,
  lastSaved,
  hasUnsavedChanges
}) => {
  const getIndicatorContent = () => {
    if (isAutoSaving) {
      return {
        icon: <Clock className="w-3 h-3 animate-spin" />,
        text: 'Saving...',
        variant: 'secondary' as const,
        className: 'bg-blue-100 text-blue-800'
      };
    }

    if (hasUnsavedChanges) {
      return {
        icon: <AlertCircle className="w-3 h-3" />,
        text: 'Unsaved changes',
        variant: 'secondary' as const,
        className: 'bg-yellow-100 text-yellow-800'
      };
    }

    return {
      icon: <CheckCircle className="w-3 h-3" />,
      text: `Saved ${formatDistanceToNow(lastSaved, { addSuffix: true })}`,
      variant: 'secondary' as const,
      className: 'bg-green-100 text-green-800'
    };
  };

  const indicator = getIndicatorContent();

  return (
    <Badge variant={indicator.variant} className={`${indicator.className} flex items-center space-x-1`}>
      {indicator.icon}
      <span className="text-xs">{indicator.text}</span>
    </Badge>
  );
}; 