import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  MessageSquare, 
  Reply, 
  Eye, 
  EyeOff, 
  Tag, 
  Clock,
  CheckCircle,
  AlertTriangle,
  XCircle,
  ThumbsUp,
  Filter
} from 'lucide-react';
import { DissertationComment } from '@/types/dissertation';
import { formatDistanceToNow } from 'date-fns';

interface CommentPanelProps {
  documentId: string;
  comments: DissertationComment[];
  onAddComment: (comment: DissertationComment) => void;
  showPrivateComments: boolean;
  userRole: 'student' | 'teacher';
}

export const CommentPanel: React.FC<CommentPanelProps> = ({
  documentId,
  comments,
  onAddComment,
  showPrivateComments,
  userRole
}) => {
  const [newComment, setNewComment] = useState('');
  const [commentType, setCommentType] = useState<'inline' | 'page' | 'paragraph'>('inline');
  const [commentVisibility, setCommentVisibility] = useState<'public' | 'private'>('public');
  const [commentTag, setCommentTag] = useState<DissertationComment['tag']>();
  const [filterTag, setFilterTag] = useState<DissertationComment['tag'] | 'all'>('all');
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [replyText, setReplyText] = useState('');

  const handleAddComment = () => {
    if (!newComment.trim()) return;

    const comment: DissertationComment = {
      id: Date.now().toString(),
      document_id: documentId,
      author_id: 'current-user-id', // This should come from auth context
      author_name: 'Current User', // This should come from auth context
      author_role: userRole,
      content: newComment,
      position: {
        page_number: 1, // This should be determined by current page
      },
      type: commentType,
      status: 'active',
      visibility: commentVisibility,
      created_at: new Date(),
      updated_at: new Date(),
      tag: commentTag,
      replies: []
    };

    onAddComment(comment);
    setNewComment('');
    setCommentTag(undefined);
  };

  const handleAddReply = (commentId: string) => {
    if (!replyText.trim()) return;

    // This would typically update the comment with a new reply
    // For now, we'll just reset the reply state
    setReplyingTo(null);
    setReplyText('');
  };

  const getTagColor = (tag?: DissertationComment['tag']) => {
    switch (tag) {
      case 'needs_revision': return 'bg-red-100 text-red-800';
      case 'approved': return 'bg-green-100 text-green-800';
      case 'plagiarized': return 'bg-orange-100 text-orange-800';
      case 'good_work': return 'bg-blue-100 text-blue-800';
      case 'clarification_needed': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTagIcon = (tag?: DissertationComment['tag']) => {
    switch (tag) {
      case 'needs_revision': return <XCircle className="w-3 h-3" />;
      case 'approved': return <CheckCircle className="w-3 h-3" />;
      case 'plagiarized': return <AlertTriangle className="w-3 h-3" />;
      case 'good_work': return <ThumbsUp className="w-3 h-3" />;
      case 'clarification_needed': return <MessageSquare className="w-3 h-3" />;
      default: return null;
    }
  };

  const filteredComments = comments.filter(comment => {
    if (!showPrivateComments && comment.visibility === 'private') return false;
    if (filterTag !== 'all' && comment.tag !== filterTag) return false;
    return true;
  });

  return (
    <div className="comment-panel h-full flex flex-col">
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between mb-4">
          <h3 className="font-semibold flex items-center">
            <MessageSquare className="w-4 h-4 mr-2" />
            Comments ({filteredComments.length})
          </h3>
          
          <div className="flex items-center space-x-2">
            <Select value={filterTag} onValueChange={(value) => setFilterTag(value as any)}>
              <SelectTrigger className="w-32 h-8">
                <Filter className="w-3 h-3 mr-1" />
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="needs_revision">Needs Revision</SelectItem>
                <SelectItem value="approved">Approved</SelectItem>
                <SelectItem value="plagiarized">Plagiarized</SelectItem>
                <SelectItem value="good_work">Good Work</SelectItem>
                <SelectItem value="clarification_needed">Clarification</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Add New Comment */}
        <div className="space-y-3">
          <div className="flex space-x-2">
            <Select value={commentType} onValueChange={(value) => setCommentType(value as any)}>
              <SelectTrigger className="w-24 h-8">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="inline">Inline</SelectItem>
                <SelectItem value="page">Page</SelectItem>
                <SelectItem value="paragraph">Paragraph</SelectItem>
              </SelectContent>
            </Select>

            {userRole === 'teacher' && (
              <Select value={commentVisibility} onValueChange={(value) => setCommentVisibility(value as any)}>
                <SelectTrigger className="w-20 h-8">
                  {commentVisibility === 'private' ? <EyeOff className="w-3 h-3" /> : <Eye className="w-3 h-3" />}
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="public">Public</SelectItem>
                  <SelectItem value="private">Private</SelectItem>
                </SelectContent>
              </Select>
            )}

            {userRole === 'teacher' && (
              <Select value={commentTag || ''} onValueChange={(value) => setCommentTag(value as any || undefined)}>
                <SelectTrigger className="w-32 h-8">
                  <Tag className="w-3 h-3 mr-1" />
                  <SelectValue placeholder="Tag" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">No Tag</SelectItem>
                  <SelectItem value="needs_revision">Needs Revision</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="plagiarized">Plagiarized</SelectItem>
                  <SelectItem value="good_work">Good Work</SelectItem>
                  <SelectItem value="clarification_needed">Clarification</SelectItem>
                </SelectContent>
              </Select>
            )}
          </div>

          <Textarea
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
            placeholder="Add a comment..."
            className="min-h-[80px]"
          />
          
          <Button onClick={handleAddComment} size="sm" className="w-full">
            Add Comment
          </Button>
        </div>
      </div>

      {/* Comments List */}
      <ScrollArea className="flex-1">
        <div className="p-4 space-y-4">
          {filteredComments.map((comment) => (
            <div key={comment.id} className="comment-item border rounded-lg p-3 bg-white">
              {/* Comment Header */}
              <div className="flex items-start justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <span className="font-medium text-sm">{comment.author_name}</span>
                  <Badge variant="outline" className="text-xs">
                    {comment.author_role}
                  </Badge>
                  {comment.visibility === 'private' && (
                    <Badge variant="secondary" className="text-xs">
                      <EyeOff className="w-3 h-3 mr-1" />
                      Private
                    </Badge>
                  )}
                </div>
                
                <div className="flex items-center space-x-2">
                  {comment.tag && (
                    <Badge className={`text-xs ${getTagColor(comment.tag)} flex items-center space-x-1`}>
                      {getTagIcon(comment.tag)}
                      <span>{comment.tag.replace('_', ' ')}</span>
                    </Badge>
                  )}
                  <span className="text-xs text-gray-500 flex items-center">
                    <Clock className="w-3 h-3 mr-1" />
                    {formatDistanceToNow(comment.created_at, { addSuffix: true })}
                  </span>
                </div>
              </div>

              {/* Comment Content */}
              <div className="mb-3">
                <p className="text-sm text-gray-700">{comment.content}</p>
                {comment.highlighted_text && (
                  <div className="mt-2 p-2 bg-yellow-50 border-l-4 border-yellow-400">
                    <p className="text-xs text-gray-600 mb-1">Referenced text:</p>
                    <p className="text-sm italic">"{comment.highlighted_text}"</p>
                  </div>
                )}
              </div>

              {/* Comment Actions */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setReplyingTo(comment.id)}
                    className="h-6 px-2 text-xs"
                  >
                    <Reply className="w-3 h-3 mr-1" />
                    Reply
                  </Button>
                  
                  <Badge variant="outline" className="text-xs">
                    Page {comment.position.page_number}
                  </Badge>
                </div>

                <Badge 
                  variant={comment.status === 'active' ? 'default' : 'secondary'}
                  className="text-xs"
                >
                  {comment.status}
                </Badge>
              </div>

              {/* Replies */}
              {comment.replies && comment.replies.length > 0 && (
                <div className="mt-3 pl-4 border-l-2 border-gray-200 space-y-2">
                  {comment.replies.map((reply) => (
                    <div key={reply.id} className="bg-gray-50 rounded p-2">
                      <div className="flex items-center justify-between mb-1">
                        <div className="flex items-center space-x-2">
                          <span className="font-medium text-xs">{reply.author_name}</span>
                          <Badge variant="outline" className="text-xs">
                            {reply.author_role}
                          </Badge>
                        </div>
                        <span className="text-xs text-gray-500">
                          {formatDistanceToNow(reply.created_at, { addSuffix: true })}
                        </span>
                      </div>
                      <p className="text-xs text-gray-700">{reply.content}</p>
                    </div>
                  ))}
                </div>
              )}

              {/* Reply Form */}
              {replyingTo === comment.id && (
                <div className="mt-3 pl-4 border-l-2 border-blue-200">
                  <Textarea
                    value={replyText}
                    onChange={(e) => setReplyText(e.target.value)}
                    placeholder="Write a reply..."
                    className="min-h-[60px] mb-2"
                  />
                  <div className="flex space-x-2">
                    <Button 
                      size="sm" 
                      onClick={() => handleAddReply(comment.id)}
                      className="h-6 px-3 text-xs"
                    >
                      Reply
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={() => setReplyingTo(null)}
                      className="h-6 px-3 text-xs"
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              )}
            </div>
          ))}

          {filteredComments.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <MessageSquare className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>No comments yet</p>
              <p className="text-sm">Add the first comment to start the discussion</p>
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  );
}; 