import React from 'react';
import { Badge } from '@/components/ui/badge';
import { FileText, Type } from 'lucide-react';

interface WordCountDisplayProps {
  wordCount: number;
  characterCount: number;
  target?: number;
  showProgress?: boolean;
}

export const WordCountDisplay: React.FC<WordCountDisplayProps> = ({
  wordCount,
  characterCount,
  target,
  showProgress = false
}) => {
  const getProgressColor = () => {
    if (!target) return 'text-gray-600';
    const percentage = (wordCount / target) * 100;
    if (percentage < 50) return 'text-red-600';
    if (percentage < 80) return 'text-yellow-600';
    return 'text-green-600';
  };

  const getProgressPercentage = () => {
    if (!target) return 0;
    return Math.min((wordCount / target) * 100, 100);
  };

  return (
    <div className="flex items-center space-x-4">
      <div className="flex items-center space-x-1">
        <FileText className="w-4 h-4 text-gray-500" />
        <span className="text-sm">
          <span className={target ? getProgressColor() : 'text-gray-700'}>
            {wordCount.toLocaleString()}
          </span>
          {target && (
            <span className="text-gray-500">
              /{target.toLocaleString()}
            </span>
          )}
          <span className="text-gray-500 ml-1">words</span>
        </span>
      </div>
      
      <div className="flex items-center space-x-1">
        <Type className="w-4 h-4 text-gray-500" />
        <span className="text-sm text-gray-700">
          {characterCount.toLocaleString()} chars
        </span>
      </div>

      {showProgress && target && (
        <Badge variant="outline" className="text-xs">
          {getProgressPercentage().toFixed(0)}%
        </Badge>
      )}
    </div>
  );
}; 