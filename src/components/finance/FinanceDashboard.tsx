import { Menu } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Routes, Route } from 'react-router-dom';
import { PayFees } from './PayFees';
import { AddTransaction } from './AddTransaction';
import { FinanceOverview } from './overview/FinanceOverview';
import { OutstandingPayments } from './OutstandingPayments';

interface FinanceDashboardProps {
  setSidebarOpen: (open: boolean) => void;
}

const FinanceDashboard = ({ setSidebarOpen }: FinanceDashboardProps) => {
  return (
    <div>
      <div className="lg:hidden p-4">
        <Button
          variant="ghost"
          size="icon"
          onClick={(e) => {
            e.stopPropagation();
            setSidebarOpen(true);
          }}
          className="hover:bg-green-100"
          aria-label="Open sidebar menu"
        >
          <Menu className="h-6 w-6 text-green-700" />
        </Button>
      </div>
      <Routes>
        <Route index element={<FinanceOverview />} />
        <Route path="fees" element={<PayFees />} />
        <Route path="transactions" element={<AddTransaction />} />
        <Route path="outstanding" element={<OutstandingPayments />} />
      </Routes>
    </div>
  );
};

export default FinanceDashboard;
