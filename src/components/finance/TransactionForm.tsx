import { useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import type { Transaction } from '@/types/finance';

const formSchema = z.object({
  date: z.string(),
  description: z.string().min(1, 'Description is required'),
  category: z.enum(['tuition', 'supplies', 'salary', 'maintenance', 'other']),
  type: z.enum(['income', 'expense']),
  amount: z.number().min(0, 'Amount must be positive'),
  status: z.enum(['completed', 'pending', 'cancelled']),
  notes: z.string().optional(),
});

interface TransactionFormProps {
  mode: 'create' | 'edit';
}

export const TransactionForm = ({ mode }: TransactionFormProps) => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      date: new Date().toISOString().split('T')[0],
      description: '',
      category: 'tuition',
      type: 'income',
      amount: 0,
      status: 'pending',
      notes: '',
    },
  });

  useEffect(() => {
    if (mode === 'edit' && id) {
      // Fetch transaction data and set form values
      const mockTransaction: Transaction = {
        id: '1',
        date: new Date().toISOString(),
        description: 'Student Fee Payment',
        category: 'tuition',
        type: 'income',
        amount: 1000,
        status: 'completed',
        notes: 'First semester payment',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      form.reset({
        date: mockTransaction.date.split('T')[0],
        description: mockTransaction.description,
        category: mockTransaction.category,
        type: mockTransaction.type,
        amount: mockTransaction.amount,
        status: mockTransaction.status,
        notes: mockTransaction.notes,
      });
    }
  }, [mode, id, form]);

  const onSubmit = (values: z.infer<typeof formSchema>) => {
    console.log(values);
    toast({
      title: `Transaction ${mode === 'create' ? 'created' : 'updated'}`,
      description: 'The transaction has been successfully saved.',
    });
    navigate('/dashboard/finance');
  };

  return (
    <div className="max-w-2xl mx-auto p-4">
      <h2 className="text-2xl font-bold mb-6">
        {mode === 'create' ? 'Create' : 'Edit'} Transaction
      </h2>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="date"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Date</FormLabel>
                <FormControl>
                  <Input type="date" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Description</FormLabel>
                <FormControl>
                  <Input {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="category"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Category</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="tuition">Tuition</SelectItem>
                    <SelectItem value="supplies">Supplies</SelectItem>
                    <SelectItem value="salary">Salary</SelectItem>
                    <SelectItem value="maintenance">Maintenance</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="type"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Type</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="income">Income</SelectItem>
                    <SelectItem value="expense">Expense</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="amount"
            render={({ field: { value, onChange, ...field }}) => (
              <FormItem>
                <FormLabel>Amount</FormLabel>
                <FormControl>
                  <Input 
                    type="number" 
                    step="0.01"
                    value={value}
                    onChange={e => onChange(parseFloat(e.target.value) || 0)}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="status"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Status</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="notes"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Notes</FormLabel>
                <FormControl>
                  <Textarea {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex gap-4">
            <Button type="submit">
              {mode === 'create' ? 'Create' : 'Update'} Transaction
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={() => navigate('/dashboard/finance')}
            >
              Cancel
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};
