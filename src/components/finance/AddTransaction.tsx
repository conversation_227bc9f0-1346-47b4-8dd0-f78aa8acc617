import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { TransactionType, TransactionCategory, TransactionStatus } from "@/types/finance";
import { createTransaction } from "@/api/transactions";
import { Loader2 } from "lucide-react";

export const AddTransaction = () => {
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [transaction, setTransaction] = useState({
    type: "income" as TransactionType,
    amount: "",
    category: "tuition" as TransactionCategory,
    description: "",
    date: new Date().toISOString().split('T')[0],
    status: "pending" as TransactionStatus,
    notes: ""
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (isSubmitting) return;
    
    setIsSubmitting(true);
    
    try {
      await createTransaction({
        type: transaction.type,
        amount: parseFloat(transaction.amount),
        category: transaction.category,
        description: transaction.description,
        date: transaction.date,
        status: transaction.status,
        notes: transaction.notes || undefined
      });

      toast.success("Transaction added successfully");
      navigate("/dashboard/finance");
    } catch (error) {
      console.error('Error adding transaction:', error);
      toast.error("Failed to add transaction");
    } finally {
      setIsSubmitting(false);
    }
  };

  const transactionTypes: TransactionType[] = ["income", "expense"];
  const transactionCategories: TransactionCategory[] = ["tuition", "supplies", "salary", "maintenance", "other"];
  const transactionStatuses: TransactionStatus[] = ["pending", "completed", "cancelled"];

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold">Add Transaction</h2>
        <p className="text-muted-foreground">Record a new financial transaction</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Transaction Details</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="type">Transaction Type</Label>
              <Select
                value={transaction.type}
                onValueChange={(value: TransactionType) =>
                  setTransaction((prev) => ({ ...prev, type: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select transaction type" />
                </SelectTrigger>
                <SelectContent>
                  {transactionTypes.map((type) => (
                    <SelectItem key={type} value={type}>
                      {type.charAt(0).toUpperCase() + type.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="amount">Amount</Label>
              <Input
                id="amount"
                type="number"
                value={transaction.amount}
                onChange={(e) =>
                  setTransaction((prev) => ({ ...prev, amount: e.target.value }))
                }
                placeholder="Enter amount"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="category">Category</Label>
              <Select
                value={transaction.category}
                onValueChange={(value: TransactionCategory) =>
                  setTransaction((prev) => ({ ...prev, category: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {transactionCategories.map((category) => (
                    <SelectItem key={category} value={category}>
                      {category.charAt(0).toUpperCase() + category.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select
                value={transaction.status}
                onValueChange={(value: TransactionStatus) =>
                  setTransaction((prev) => ({ ...prev, status: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  {transactionStatuses.map((status) => (
                    <SelectItem key={status} value={status}>
                      {status.charAt(0).toUpperCase() + status.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="date">Date</Label>
              <Input
                id="date"
                type="date"
                value={transaction.date}
                onChange={(e) =>
                  setTransaction((prev) => ({ ...prev, date: e.target.value }))
                }
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={transaction.description}
                onChange={(e) =>
                  setTransaction((prev) => ({ ...prev, description: e.target.value }))
                }
                placeholder="Enter transaction description"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                value={transaction.notes}
                onChange={(e) =>
                  setTransaction((prev) => ({ ...prev, notes: e.target.value }))
                }
                placeholder="Enter any additional notes"
              />
            </div>

            <div className="flex gap-4">
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : "Add Transaction"}
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={() => navigate("/dashboard/finance")}
              >
                Cancel
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};
