import { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Search, ArrowUpDown } from "lucide-react";
import { Link } from "react-router-dom";

interface StudentWithFees {
  id: string;
  student_id: string;
  name: string;
  total_fees_due: number;
  total_fees_paid: number;
  course: {
    name: string;
  };
}

export const OutstandingPayments = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");

  const { data: students = [], isLoading } = useQuery({
    queryKey: ["outstanding-payments"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("students")
        .select(`
          id,
          student_id,
          name,
          total_fees_due,
          total_fees_paid,
          course:courses!inner(name)
        `)
        .not("total_fees_paid", "eq", null)
        .not("total_fees_due", "eq", null)
        .lt("total_fees_paid", "total_fees_due")
        .returns<StudentWithFees[]>();

      if (error) throw error;
      return data || [];
    },
  });

  const calculateOutstandingAmount = (totalDue: number, totalPaid: number) => {
    return totalDue - totalPaid;
  };

  const calculatePaymentProgress = (totalDue: number, totalPaid: number) => {
    return ((totalPaid / totalDue) * 100).toFixed(1);
  };

  const sortedStudents = [...students].sort((a, b) => {
    const outstandingA = calculateOutstandingAmount(a.total_fees_due, a.total_fees_paid);
    const outstandingB = calculateOutstandingAmount(b.total_fees_due, b.total_fees_paid);
    return sortOrder === "desc" ? outstandingB - outstandingA : outstandingA - outstandingB;
  });

  const filteredStudents = sortedStudents.filter(
    (student) =>
      student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.student_id.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const totalOutstanding = students.reduce(
    (sum, student) => sum + calculateOutstandingAmount(student.total_fees_due, student.total_fees_paid),
    0
  );

  if (isLoading) return <div>Loading...</div>;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Outstanding Payments</h2>
          <p className="text-muted-foreground">Track and manage pending student payments</p>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Outstanding</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Leones {totalOutstanding.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              From {students.length} students
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="flex gap-4 items-center">
        <div className="relative w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search by name or ID..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setSortOrder(sortOrder === "asc" ? "desc" : "asc")}
          className="gap-2"
        >
          Amount <ArrowUpDown className="h-4 w-4" />
        </Button>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Student ID</TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Course</TableHead>
              <TableHead>Total Due</TableHead>
              <TableHead>Paid Amount</TableHead>
              <TableHead>Outstanding</TableHead>
              <TableHead>Progress</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredStudents.map((student) => {
              const outstandingAmount = calculateOutstandingAmount(
                student.total_fees_due,
                student.total_fees_paid
              );
              const progress = calculatePaymentProgress(
                student.total_fees_due,
                student.total_fees_paid
              );
              return (
                <TableRow key={student.id}>
                  <TableCell>{student.student_id}</TableCell>
                  <TableCell>{student.name}</TableCell>
                  <TableCell>{student.course?.name || "N/A"}</TableCell>
                  <TableCell>Leones {student.total_fees_due.toLocaleString()}</TableCell>
                  <TableCell>Leones {student.total_fees_paid.toLocaleString()}</TableCell>
                  <TableCell>
                    <Badge variant="destructive">
                      Leones {outstandingAmount.toLocaleString()}
                    </Badge>
                  </TableCell>
                  <TableCell>{progress}%</TableCell>
                  <TableCell>
                    <Link to={`/dashboard/enrollment/student/${student.id}`}>
                      <Button variant="outline" size="sm">
                        View Details
                      </Button>
                    </Link>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

export default OutstandingPayments; 