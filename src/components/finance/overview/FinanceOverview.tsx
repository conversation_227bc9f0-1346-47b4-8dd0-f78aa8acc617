import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { FinanceStats } from "./FinanceStats";
import { TransactionList } from "./TransactionList";
import { getTransactions, getTransactionSummary } from "@/api/transactions";
import { getCourses, type Course } from "@/api/courses";
import { getLevels, type Level } from "@/api/levels";
import { Button } from "@/components/ui/button";
import { FileText, Loader2 } from "lucide-react";
import { exportTransactionsToPDF } from "../utils/exportTransactionData";
import { toast } from "sonner";

export const FinanceOverview = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [transactionType, setTransactionType] = useState<string>("all");
  const [courseFilter, setCourseFilter] = useState<string>("all");
  const [levelFilter, setLevelFilter] = useState<string>("all");
  const [courses, setCourses] = useState<Course[]>([]);
  const [levels, setLevels] = useState<Level[]>([]);
  const [filteredLevels, setFilteredLevels] = useState<Level[]>([]);
  const [isExporting, setIsExporting] = useState(false);

  // Fetch all courses
  useEffect(() => {
    const fetchCourses = async () => {
      try {
        const coursesData = await courseModel.getAllCourses();
        setCourses(coursesData);
      } catch (error) {
        console.error("Error fetching courses:", error);
      }
    };

    fetchCourses();
  }, []);

  // Fetch all levels
  useEffect(() => {
    const fetchLevels = async () => {
      try {
        const levelsData = await levelModel.getAllLevels();
        setLevels(levelsData);
      } catch (error) {
        console.error("Error fetching levels:", error);
      }
    };

    fetchLevels();
  }, []);

  // Filter levels based on selected course
  useEffect(() => {
    if (courseFilter === "all") {
      setFilteredLevels(levels);
    } else {
      const filtered = levels.filter(level => level.course_id === courseFilter);
      setFilteredLevels(filtered);
    }
    
    // Reset level filter when course changes
    setLevelFilter("all");
  }, [courseFilter, levels]);

  const { data: transactions = [], isLoading } = useQuery({
    queryKey: ['transactions'],
    queryFn: async () => {
      try {
        const data = await getTransactions();
        return data || [];
      } catch (error) {
        console.error('Error fetching transactions:', error);
        return [];
      }
    }
  });

  // Get filtered transactions based on the current filters
  const getFilteredTransactions = async () => {
    let filtered = [...transactions];
    
    // Apply transaction type filter
    if (transactionType !== "all") {
      filtered = filtered.filter(t => t.type.toLowerCase() === transactionType.toLowerCase());
    }
    
    // If there's a search query, filter by it
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(t => 
        t.description?.toLowerCase().includes(query) || 
        String(t.category).toLowerCase().includes(query)
      );
    }
    
    // Apply course and level filters
    if (courseFilter !== "all" || levelFilter !== "all") {
      // Create a cache for student data to avoid duplicate fetches
      const studentCache: Record<string, any> = {};
      
      // We need to manually filter by student course/level
      // First, create an array of promises for all student data fetching
      const studentPromises = filtered
        .filter(t => t.student_id) // Only include transactions with student_id
        .map(async (transaction) => {
          if (!transaction.student_id) return null;
          
          // Check cache first
          if (studentCache[transaction.student_id]) {
            return {
              transaction,
              student: studentCache[transaction.student_id]
            };
          }
          
          try {
            // Import getStudent dynamically to avoid type errors
            const { getStudent } = await import('@/api/students');
            const student = await getStudent(transaction.student_id);
            
            if (student) {
              // Save to cache
              studentCache[transaction.student_id] = student;
              
              return {
                transaction,
                student
              };
            }
          } catch (error) {
            console.error(`Error fetching student data for ${transaction.student_id}:`, error);
          }
          
          return null;
        });
      
      // Wait for all student data to be fetched
      const studentDataResults = await Promise.all(studentPromises);
      
      // Filter transactions based on course and level
      const matchingTransactions = studentDataResults
        .filter(result => {
          if (!result) return false;
          const { student } = result;
          
          // Check if the student's course matches the selected course filter
          const courseMatches = courseFilter === "all" || student.course_id === courseFilter;
          
          // Check if the student's level matches the selected level filter
          const levelMatches = levelFilter === "all" || student.level_id === levelFilter;
          
          return courseMatches && levelMatches;
        })
        .map(result => result!.transaction);
      
      // Get IDs of matching transactions
      const matchingIds = new Set(matchingTransactions.map(t => t.id));
      
      // Filter the transactions to only include those with matching students or without a student_id
      filtered = filtered.filter(t => !t.student_id || matchingIds.has(t.id));
    }
    
    return filtered;
  };

  // Handle export to PDF
  const handleExport = async () => {
    if (isLoading || isExporting) return;
    
    if (transactions.length === 0) {
      toast.error("No transactions to export");
      return;
    }
    
    setIsExporting(true);
    
    try {
      // Get transactions filtered by type, search query, course, and level
      const filtered = await getFilteredTransactions();
      
      if (filtered.length === 0) {
        toast.error("No transactions match the current filters");
        setIsExporting(false);
        return;
      }
      
      // Set the title based on filters
      let title = "Transactions";
      if (transactionType !== "all") {
        title = `${transactionType.charAt(0).toUpperCase() + transactionType.slice(1)} Transactions`;
      }
      
      if (courseFilter !== "all") {
        const course = courses.find(c => c.id === courseFilter);
        title += ` - ${course?.name || 'Unknown Course'}`;
        
        if (levelFilter !== "all") {
          const level = filteredLevels.find(l => l.id === levelFilter);
          title += ` - ${level?.name || 'Unknown Level'}`;
        }
      }
      
      // Call the export function
      await exportTransactionsToPDF({
        transactions: filtered,
        title
      });
    } catch (error) {
      console.error("Error exporting transactions:", error);
      toast.error("Failed to export transactions");
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Finance Overview</h2>
          <p className="text-muted-foreground">View and manage financial transactions</p>
        </div>
        
        <Button 
          onClick={handleExport}
          disabled={isExporting || isLoading || transactions.length === 0}
          className="bg-green-600 hover:bg-green-700"
        >
          {isExporting ? (
            <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Exporting...</>
          ) : (
            <><FileText className="mr-2 h-4 w-4" /> Export to PDF</>
          )}
        </Button>
      </div>

      <FinanceStats transactions={transactions} />

      <TransactionList
        transactions={transactions}
        isLoading={isLoading}
        searchQuery={searchQuery}
        transactionType={transactionType}
        courseFilter={courseFilter}
        levelFilter={levelFilter}
        courses={courses}
        filteredLevels={filteredLevels}
        onSearchChange={setSearchQuery}
        onTypeChange={setTransactionType}
        onCourseChange={setCourseFilter}
        onLevelChange={setLevelFilter}
      />
    </div>
  );
};
