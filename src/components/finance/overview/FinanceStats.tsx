import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { Transaction } from "@/types/finance";
import { useQuery } from "@tanstack/react-query";
import { getStudents } from '@/api/students';

interface FinanceStatsProps {
  transactions: Transaction[];
}

export const FinanceStats = ({ transactions }: FinanceStatsProps) => {
  const { data: activeStudents = [] } = useQuery({
    queryKey: ['active-students'],
    queryFn: async () => {
      const students = await studentModel.getAllStudents({
        enrollment_status: 'Active'
      });
      return students;
    }
  });

  // Calculate the number of new students this month
  const { data: newStudentsThisMonth = [] } = useQuery({
    queryKey: ['new-students-this-month'],
    queryFn: async () => {
      const today = new Date();
      const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
      const firstDayOfMonthStr = firstDayOfMonth.toISOString().split('T')[0]; // YYYY-MM-DD format
      
      const allStudents = await studentModel.getAllStudents({
        enrollment_status: 'Active'
      });
      
      // Filter students registered this month
      return allStudents.filter(student => {
        if (!student.date_of_registration) return false;
        
        // Compare date strings (YYYY-MM-DD format)
        return student.date_of_registration >= firstDayOfMonthStr;
      });
    }
  });

  return (
    <div>
      <div className="mb-4">
        <h3 className="text-lg font-medium">Financial Overview</h3>
      </div>
      
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              Le {transactions
                .filter(t => t.type?.toLowerCase() === 'income')
                .reduce((sum, t) => sum + (Number(t.amount) || 0), 0)
                .toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">+20.1% from last month</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Expenses</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              Le {transactions
                .filter(t => t.type?.toLowerCase() === 'expense')
                .reduce((sum, t) => sum + (Number(t.amount) || 0), 0)
                .toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">+4% from last month</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Fees</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              Le {transactions
                .filter(t => t.status?.toLowerCase() === 'pending' && t.type?.toLowerCase() === 'income')
                .reduce((sum, t) => sum + (Number(t.amount) || 0), 0)
                .toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              {transactions.filter(t => t.status?.toLowerCase() === 'pending').length} transactions pending
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Students</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activeStudents.length}</div>
            <p className="text-xs text-muted-foreground">
              +{newStudentsThisMonth.length} this month
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
