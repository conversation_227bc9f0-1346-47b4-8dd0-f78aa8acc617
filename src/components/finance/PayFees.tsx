import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useState } from "react";
import { createPayment } from "@/api/payments";
import { toast } from "sonner";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { handleError } from '@/utils/error-handling';

interface FeeDetails {
  studentId: string;
  amount: string;
  feeType: string;
  paymentMethod: string;
  status: 'paid' | 'pending' | 'overdue' | 'partial';
}

export const PayFees = () => {
  const queryClient = useQueryClient();
  const [feeDetails, setFeeDetails] = useState<FeeDetails>({
    studentId: "",
    amount: "",
    feeType: "tuition",
    paymentMethod: "cash",
    status: "paid"
  });

  const paymentMutation = useMutation({
    mutationFn: async (data: FeeDetails) => {
      const payment = {
        student_id: data.studentId,
        amount: parseFloat(data.amount),
        date_paid: new Date().toISOString(),
        payment_due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
        status: data.status,
        notes: `Payment method: ${data.paymentMethod}, Type: ${data.feeType}`
      };

      return createPayment(payment);
    },
    onSuccess: () => {
      toast.success("Payment recorded successfully");
      setFeeDetails({
        studentId: "",
        amount: "",
        feeType: "tuition",
        paymentMethod: "cash",
        status: "paid"
      });
      queryClient.invalidateQueries({ queryKey: ['payments'] });
      queryClient.invalidateQueries({ queryKey: ['transactions'] });
    },
    onError: (error) => {
      console.error('Error recording payment:', error);
      toast.error(handleError(error, "Failed to record payment"));
    }
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!feeDetails.studentId || !feeDetails.amount) {
      toast.error("Please fill in all required fields");
      return;
    }

    if (isNaN(parseFloat(feeDetails.amount)) || parseFloat(feeDetails.amount) <= 0) {
      toast.error("Please enter a valid amount");
      return;
    }

    paymentMutation.mutate(feeDetails);
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold">Pay Fees</h2>
        <p className="text-muted-foreground">Record student fee payments</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Fee Payment Details</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="studentId">Student ID</Label>
              <Input
                id="studentId"
                value={feeDetails.studentId}
                onChange={(e) =>
                  setFeeDetails({ ...feeDetails, studentId: e.target.value })
                }
                placeholder="Enter student ID"
                required
              />
              <p className="text-sm text-muted-foreground">
                Enter the student's ID number
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="amount">Amount (Le)</Label>
              <Input
                id="amount"
                type="number"
                value={feeDetails.amount}
                onChange={(e) =>
                  setFeeDetails({ ...feeDetails, amount: e.target.value })
                }
                placeholder="Enter amount in Leones"
                required
                min="0"
                step="0.01"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="feeType">Fee Type</Label>
              <Select
                value={feeDetails.feeType}
                onValueChange={(value) =>
                  setFeeDetails({ ...feeDetails, feeType: value })
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select fee type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="tuition">Tuition Fee</SelectItem>
                  <SelectItem value="exam">Exam Fee</SelectItem>
                  <SelectItem value="registration">Registration Fee</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="paymentMethod">Payment Method</Label>
              <Select
                value={feeDetails.paymentMethod}
                onValueChange={(value) =>
                  setFeeDetails({ ...feeDetails, paymentMethod: value })
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select payment method" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="cash">Cash</SelectItem>
                  <SelectItem value="card">Card</SelectItem>
                  <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">Payment Status</Label>
              <Select
                value={feeDetails.status}
                onValueChange={(value: 'paid' | 'pending' | 'overdue' | 'partial') =>
                  setFeeDetails({ ...feeDetails, status: value })
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select payment status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="paid">Paid</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="partial">Partial</SelectItem>
                  <SelectItem value="overdue">Overdue</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Button 
              type="submit" 
              className="w-full"
              disabled={paymentMutation.isPending}
            >
              {paymentMutation.isPending ? "Recording Payment..." : "Record Payment"}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};
