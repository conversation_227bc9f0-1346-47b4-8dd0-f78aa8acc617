import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { apiClient } from '@/lib/api-client';

export const useMaintenanceMode = () => {
  const [isMaintenanceMode, setIsMaintenanceMode] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [userProfile, setUserProfile] = useState<any>(null);
  const { user } = useAuth();

  useEffect(() => {
    // Function to fetch user profile
    const fetchUserProfile = async () => {
      try {
        if (user) {
          // User profile is already available from the auth context
          setUserProfile(user);
        }
      } catch (error) {
        console.error('Error in fetchUserProfile:', error);
      }
    };
    
    // Function to fetch maintenance mode setting
    const fetchMaintenanceMode = async () => {
      try {
        // First check localStorage for immediate value
        const storedSettings = localStorage.getItem('generalSettings');
        if (storedSettings) {
          const settings = JSON.parse(storedSettings);
          setIsMaintenanceMode(settings.maintenanceMode || false);
        }

        // Then try to get the latest from API
        try {
          const settings = await apiClient.get<{maintenance_mode: boolean, school_name: string}>('/settings/general');

          if (settings && settings.maintenance_mode !== undefined) {
            setIsMaintenanceMode(settings.maintenance_mode);

            // Update localStorage for consistency
            const localSettings = {
              schoolName: settings.school_name || 'Academic Dashboard',
              maintenanceMode: settings.maintenance_mode
            };
            localStorage.setItem('generalSettings', JSON.stringify(localSettings));
          }
        } catch (apiError) {
          console.error('Error fetching maintenance mode from API:', apiError);
        }
      } catch (error) {
        console.error('Error fetching maintenance mode:', error);
      } finally {
        setIsLoading(false);
      }
    };

    // Run both functions
    fetchUserProfile();
    fetchMaintenanceMode();
    
    // Set up storage listener
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'generalSettings') {
        try {
          const newSettings = JSON.parse(e.newValue || '{}');
          setIsMaintenanceMode(newSettings.maintenanceMode || false);
        } catch (error) {
          console.error('Error parsing settings from localStorage:', error);
        }
      }
    };
    
    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [user]);

  // Determine if user is a super admin
  const isSuperAdmin = userProfile?.role === 'super_admin';
  
  // Only block users with finance_admin and enrollment_admin roles
  const isBlocked = isMaintenanceMode && 
    (userProfile?.role === 'finance_admin' || userProfile?.role === 'enrollment_admin');

  return {
    isMaintenanceMode,
    isLoading,
    isBlocked,
    isSuperAdmin
  };
};
