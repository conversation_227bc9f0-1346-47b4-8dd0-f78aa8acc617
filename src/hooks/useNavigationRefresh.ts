import { useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useLocation } from 'react-router-dom';

export const useNavigationRefresh = () => {
  const queryClient = useQueryClient();
  const location = useLocation();

  useEffect(() => {
    const handlePopState = () => {
      // Get the current path to determine which queries to invalidate
      const path = location.pathname;
      
      // Only invalidate queries related to the current section
      if (path.includes('/dashboard/enrollment')) {
        queryClient.invalidateQueries({ queryKey: ['students'] });
        queryClient.invalidateQueries({ queryKey: ['attendance'] });
      } else if (path.includes('/dashboard/finance')) {
        queryClient.invalidateQueries({ queryKey: ['finances'] });
        queryClient.invalidateQueries({ queryKey: ['transactions'] });
        queryClient.invalidateQueries({ queryKey: ['fees'] });
      } else if (path.includes('/dashboard/courses')) {
        queryClient.invalidateQueries({ queryKey: ['courses'] });
      } else if (path.includes('/dashboard/levels')) {
        queryClient.invalidateQueries({ queryKey: ['levels'] });
      } else if (path.includes('/dashboard/exams')) {
        queryClient.invalidateQueries({ queryKey: ['exams'] });
      } else if (path.includes('/dashboard/settings')) {
        queryClient.invalidateQueries({ queryKey: ['settings'] });
      }
      // No need to invalidate all queries for the dashboard overview
    };

    window.addEventListener('popstate', handlePopState);

    return () => {
      window.removeEventListener('popstate', handlePopState);
    };
  }, [queryClient, location]);
}; 