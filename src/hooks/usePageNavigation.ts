import { useNavigate } from 'react-router-dom';
import { useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';

/**
 * Custom hook for optimized page navigation
 * Prevents full page reloads and preserves component state
 */
export const usePageNavigation = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  
  /**
   * Navigate to a new page without triggering a full reload
   * Only invalidates queries related to the target section
   */
  const navigateTo = useCallback((path: string) => {
    // Determine which section we're navigating to
    const targetSection = path.split('/').filter(Boolean)[1] || 'dashboard';
    
    // Dispatch custom event before navigation to let other components save state
    const beforeNavigateEvent = new CustomEvent('beforeNavigate', {
      detail: { from: window.location.pathname, to: path }
    });
    document.dispatchEvent(beforeNavigateEvent);
    
    // Cache current page state for back navigation
    const currentState = window.history.state;
    sessionStorage.setItem(`page_state_${window.location.pathname}`, JSON.stringify(currentState));
    
    // Clear only the queries related to the section we're navigating to
    // This ensures we don't unnecessarily refetch data for other sections
    if (path.includes('/enrollment')) {
      queryClient.invalidateQueries({ queryKey: ['students'] });
    } else if (path.includes('/finance')) {
      queryClient.invalidateQueries({ queryKey: ['finances'] });
    } else if (path.includes('/courses')) {
      queryClient.invalidateQueries({ queryKey: ['courses'] });
    } else if (path.includes('/levels')) {
      queryClient.invalidateQueries({ queryKey: ['levels'] });
    } else if (path.includes('/exams')) {
      queryClient.invalidateQueries({ queryKey: ['exams'] });
    } else if (path.includes('/settings')) {
      queryClient.invalidateQueries({ queryKey: ['settings'] });
    }
    
    // Use the navigate function from react-router with replace:false 
    // to preserve history and prevent full page reload
    navigate(path, { replace: false });
  }, [navigate, queryClient]);
  
  return { navigateTo };
}; 