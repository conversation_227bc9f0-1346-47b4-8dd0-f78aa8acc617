import { useState, useEffect, useCallback } from 'react';
import {
  DissertationDocument,
  DissertationComment,
  DissertationVersion,
  StudentActivity,
  DissertationSettings,
  SupervisorDashboardData
} from '@/types/dissertation';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';
import { apiClient } from '@/lib/api-client';

interface UseDissertationsReturn {
  // State
  dissertations: DissertationDocument[];
  currentDissertation: DissertationDocument | null;
  comments: DissertationComment[];
  versions: DissertationVersion[];
  activities: StudentActivity[];
  settings: DissertationSettings | null;
  loading: boolean;
  error: string | null;
  
  // Actions
  loadUserDissertations: () => Promise<void>;
  loadDissertations: () => Promise<void>;
  loadDissertation: (id: string) => Promise<void>;
  createDissertation: (title: string, supervisorId?: string) => Promise<string>;
  updateDissertation: (id: string, data: Partial<DissertationDocument>) => Promise<void>;
  deleteDissertation: (id: string) => Promise<void>;
  saveDissertationContent: (id: string, content: string) => Promise<void>;
  
  // Comments
  loadComments: (documentId: string) => Promise<void>;
  addComment: (comment: Omit<DissertationComment, 'id' | 'created_at' | 'updated_at'>) => Promise<void>;
  updateComment: (id: string, data: Partial<DissertationComment>) => Promise<void>;
  deleteComment: (id: string) => Promise<void>;
  
  // Versions
  loadVersions: (documentId: string) => Promise<void>;
  createVersion: (documentId: string, content: string, changeSummary: string) => Promise<void>;
  
  // Activity tracking
  logActivity: (activity: Omit<StudentActivity, 'id' | 'timestamp'>) => Promise<void>;
  loadActivities: (studentId: string, documentId?: string) => Promise<void>;
  
  // Settings
  loadSettings: (documentId: string) => Promise<void>;
  saveSettings: (settings: Omit<DissertationSettings, 'id'>) => Promise<void>;
  
  // Real-time updates
  subscribeToDocument: (id: string) => () => void;
}

export const useDissertations = (): UseDissertationsReturn => {
  const { user } = useAuth();
  const [dissertations, setDissertations] = useState<DissertationDocument[]>([]);
  const [currentDissertation, setCurrentDissertation] = useState<DissertationDocument | null>(null);
  const [comments, setComments] = useState<DissertationComment[]>([]);
  const [versions, setVersions] = useState<DissertationVersion[]>([]);
  const [activities, setActivities] = useState<StudentActivity[]>([]);
  const [settings, setSettings] = useState<DissertationSettings | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load user dissertations
  const loadUserDissertations = useCallback(async () => {
    if (!user?.uid) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const userRole = user.role === 'teacher' ? 'supervisor' : 'student';
      const userDissertations = await dissertationService.getUserDissertations(user.uid, userRole);
      setDissertations(userDissertations);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load dissertations';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [user?.uid, user?.role]);

  // Load all dissertations (for users who are both students and supervisors)
  const loadDissertations = useCallback(async () => {
    if (!user?.uid) return;
    
    setLoading(true);
    setError(null);
    
    try {
      // Get dissertations where user is a student
      const studentDissertations = await dissertationService.getUserDissertations(user.uid, 'student');
      
      // If user is also a teacher/supervisor, get those dissertations too
      let supervisorDissertations: DissertationDocument[] = [];
      if (user.role === 'teacher' || user.role === 'admin') {
        supervisorDissertations = await dissertationService.getUserDissertations(user.uid, 'supervisor');
      }
      
      // Combine the dissertations, removing duplicates
      const allDissertations = [...studentDissertations];
      
      // Add supervisor dissertations if they're not already in the list
      for (const dissertation of supervisorDissertations) {
        if (!allDissertations.some(d => d.id === dissertation.id)) {
          allDissertations.push(dissertation);
        }
      }
      
      setDissertations(allDissertations);
      console.log(`Loaded ${allDissertations.length} dissertations (${studentDissertations.length} as student, ${supervisorDissertations.length} as supervisor)`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load all dissertations';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [user?.uid, user?.role]);

  // Load specific dissertation
  const loadDissertation = useCallback(async (id: string) => {
    setLoading(true);
    setError(null);
    
    try {
      const dissertation = await dissertationService.getDissertationById(id);
      setCurrentDissertation(dissertation);
      
      if (dissertation && user?.uid) {
        // Log activity
        await logActivity({
          student_id: user.uid,
          document_id: id,
          activity_type: 'page_view',
          details: 'Opened dissertation for editing'
        });
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load dissertation';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [user?.uid]);

  // Create new dissertation
  const createDissertation = useCallback(async (title: string, supervisorId?: string): Promise<string> => {
    if (!user?.uid) throw new Error('User not authenticated');
    
    setLoading(true);
    setError(null);
    
    try {
      const dissertationData = {
        title,
        content: '<p>Start writing your dissertation...</p>',
        student_id: user.uid,
        supervisor_id: supervisorId || '',
        last_edited_by: user.uid,
        status: 'draft' as const,
        auto_save_enabled: true
      };
      
      const dissertationId = await dissertationService.createDissertation(dissertationData);
      
      // Log activity
      await logActivity({
        student_id: user.uid,
        document_id: dissertationId,
        activity_type: 'edit',
        details: 'Created new dissertation'
      });
      
      toast.success('Dissertation created successfully');
      await loadDissertations(); // Refresh list with combined dissertations
      
      return dissertationId;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create dissertation';
      setError(errorMessage);
      toast.error(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [user?.uid, loadDissertations]);

  // Update dissertation
  const updateDissertation = useCallback(async (id: string, data: Partial<DissertationDocument>) => {
    if (!user?.uid) return;
    
    try {
      await dissertationService.updateDissertation(id, {
        ...data,
        last_edited_by: user.uid
      });
      
      // Update local state
      if (currentDissertation?.id === id) {
        setCurrentDissertation(prev => prev ? { ...prev, ...data } : null);
      }
      
      setDissertations(prev => prev.map(d => 
        d.id === id ? { ...d, ...data } : d
      ));
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update dissertation';
      setError(errorMessage);
      toast.error(errorMessage);
    }
  }, [user?.uid, currentDissertation?.id]);

  // Delete dissertation
  const deleteDissertation = useCallback(async (id: string) => {
    setLoading(true);
    setError(null);
    
    try {
      console.log('Deleting dissertation with ID:', id);
      
      // First, delete all related resources
      await Promise.all([
        dissertationService.deleteAllComments(id),
        dissertationService.deleteAllVersions(id),
        dissertationService.deleteSettings(id)
      ]);
      
      // Then, delete the dissertation document itself
      await dissertationService.deleteDissertation(id);
      
      // Update local state
      setDissertations(prev => prev.filter(d => d.id !== id));
      
      if (currentDissertation?.id === id) {
        setCurrentDissertation(null);
      }
      
      toast.success('Dissertation deleted successfully');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete dissertation';
      setError(errorMessage);
      toast.error(errorMessage);
      console.error('Error deleting dissertation:', err);
      throw err; // Re-throw to allow handling in the component
    } finally {
      setLoading(false);
    }
  }, [currentDissertation?.id]);

  // Save dissertation content
  const saveDissertationContent = useCallback(async (id: string, content: string) => {
    if (!user?.uid) return;
    
    try {
      // Calculate basic metrics
      const text = content.replace(/<[^>]*>/g, ''); // Strip HTML
      const wordCount = text.trim().split(/\s+/).filter(word => word.length > 0).length;
      const pageCount = Math.max(1, Math.ceil(wordCount / 250)); // Approximate words per page
      
      await dissertationService.updateDissertation(id, {
        content,
        word_count: wordCount,
        page_count: pageCount,
        last_edited_by: user.uid
      });
      
      // Log activity
      await logActivity({
        student_id: user.uid,
        document_id: id,
        activity_type: 'save',
        details: `Saved document with ${wordCount} words`
      });
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to save content';
      setError(errorMessage);
      toast.error(errorMessage);
      throw err;
    }
  }, [user?.uid]);

  // Comment operations
  const loadComments = useCallback(async (documentId: string) => {
    try {
      const documentComments = await commentService.getComments(documentId);
      setComments(documentComments);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load comments';
      setError(errorMessage);
      toast.error(errorMessage);
    }
  }, []);

  const addComment = useCallback(async (comment: Omit<DissertationComment, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      await commentService.addComment(comment);
      await loadComments(comment.document_id); // Refresh comments
      toast.success('Comment added successfully');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to add comment';
      setError(errorMessage);
      toast.error(errorMessage);
    }
  }, [loadComments]);

  const updateComment = useCallback(async (id: string, data: Partial<DissertationComment>) => {
    try {
      await commentService.updateComment(id, data);
      setComments(prev => prev.map(c => c.id === id ? { ...c, ...data } : c));
      toast.success('Comment updated successfully');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update comment';
      setError(errorMessage);
      toast.error(errorMessage);
    }
  }, []);

  const deleteComment = useCallback(async (id: string) => {
    try {
      await commentService.deleteComment(id);
      setComments(prev => prev.filter(c => c.id !== id));
      toast.success('Comment deleted successfully');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete comment';
      setError(errorMessage);
      toast.error(errorMessage);
    }
  }, []);

  // Version operations
  const loadVersions = useCallback(async (documentId: string) => {
    try {
      const documentVersions = await versionService.getVersions(documentId);
      setVersions(documentVersions);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load versions';
      setError(errorMessage);
      toast.error(errorMessage);
    }
  }, []);

  const createVersion = useCallback(async (documentId: string, content: string, changeSummary: string) => {
    if (!user?.uid) return;
    
    try {
      const text = content.replace(/<[^>]*>/g, '');
      const wordCount = text.trim().split(/\s+/).filter(word => word.length > 0).length;
      const pageCount = Math.max(1, Math.ceil(wordCount / 250));
      
      // Get current version number
      const currentDoc = await dissertationService.getDissertationById(documentId);
      const nextVersion = (currentDoc?.version || 0) + 1;
      
      await versionService.createVersion({
        document_id: documentId,
        version_number: nextVersion,
        content,
        created_by: user.uid,
        change_summary: changeSummary,
        word_count: wordCount,
        page_count: pageCount
      });
      
      // Update dissertation version
      await dissertationService.updateDissertation(documentId, {
        version: nextVersion
      });
      
      await loadVersions(documentId); // Refresh versions
      toast.success('New version created successfully');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create version';
      setError(errorMessage);
      toast.error(errorMessage);
    }
  }, [user?.uid, loadVersions]);

  // Activity operations
  const logActivity = useCallback(async (activity: Omit<StudentActivity, 'id' | 'timestamp'>) => {
    try {
      await activityService.logActivity(activity);
    } catch (err) {
      // Don't show errors for activity logging to avoid interrupting user flow
      console.error('Failed to log activity:', err);
    }
  }, []);

  const loadActivities = useCallback(async (studentId: string, documentId?: string) => {
    try {
      const studentActivities = await activityService.getStudentActivities(studentId, documentId);
      setActivities(studentActivities);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load activities';
      setError(errorMessage);
      toast.error(errorMessage);
    }
  }, []);

  // Settings operations
  const loadSettings = useCallback(async (documentId: string) => {
    try {
      const documentSettings = await settingsService.getSettings(documentId);
      setSettings(documentSettings);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load settings';
      setError(errorMessage);
      toast.error(errorMessage);
    }
  }, []);

  const saveSettings = useCallback(async (settingsData: Omit<DissertationSettings, 'id'>) => {
    try {
      await settingsService.saveSettings(settingsData);
      setSettings(prev => ({ ...prev, ...settingsData, id: prev?.id || '' }));
      toast.success('Settings saved successfully');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to save settings';
      setError(errorMessage);
      toast.error(errorMessage);
    }
  }, []);

  // Real-time subscription
  const subscribeToDocument = useCallback((id: string) => {
    return dissertationService.onDissertationChange(id, (dissertation) => {
      if (dissertation) {
        setCurrentDissertation(dissertation);
        setDissertations(prev => {
          const index = prev.findIndex(d => d.id === id);
          if (index >= 0) {
            const updated = [...prev];
            updated[index] = dissertation;
            return updated;
          }
          return prev;
        });
      }
    });
  }, []);

  // Load dissertations on mount
  useEffect(() => {
    if (user?.uid) {
      loadDissertations();
    }
  }, [user?.uid, loadDissertations]);

  return {
    // State
    dissertations,
    currentDissertation,
    comments,
    versions,
    activities,
    settings,
    loading,
    error,
    
    // Actions
    loadUserDissertations,
    loadDissertations,
    loadDissertation,
    createDissertation,
    updateDissertation,
    deleteDissertation,
    saveDissertationContent,
    
    // Comments
    loadComments,
    addComment,
    updateComment,
    deleteComment,
    
    // Versions
    loadVersions,
    createVersion,
    
    // Activity tracking
    logActivity,
    loadActivities,
    
    // Settings
    loadSettings,
    saveSettings,
    
    // Real-time updates
    subscribeToDocument
  };
};

// Hook for supervisor dashboard
export const useSupervisorDashboard = () => {
  const { user } = useAuth();
  const [dashboardData, setDashboardData] = useState<SupervisorDashboardData[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadDashboard = useCallback(async () => {
    if (!user?.uid || user.role !== 'teacher') return;
    
    setLoading(true);
    setError(null);
    
    try {
      // Get all dissertations for this supervisor
      const supervisorDissertations = await dissertationService.getUserDissertations(user.uid, 'supervisor');
      
      // Also get dissertations where the supervisor is also a student
      const studentDissertations = await dissertationService.getUserDissertations(user.uid, 'student');
      
      // Create a map of all unique dissertations
      const allDissertations = new Map();
      
      // Add all supervisor dissertations
      for (const dissertation of supervisorDissertations) {
        allDissertations.set(dissertation.id, dissertation);
      }
      
      // Add student dissertations if not already in the map
      for (const dissertation of studentDissertations) {
        if (!allDissertations.has(dissertation.id)) {
          allDissertations.set(dissertation.id, dissertation);
        }
      }
      
      // Get profiles to map student names
      const studentsCollection = collection(db, 'profiles');
      const studentsSnapshot = await getDocs(studentsCollection);
      const studentProfiles = new Map();
      
      studentsSnapshot.forEach(doc => {
        studentProfiles.set(doc.id, doc.data());
      });
      
      // Process dissertations into dashboard data
      const dashboard: SupervisorDashboardData[] = [];
      
      for (const dissertation of allDissertations.values()) {
        // Calculate dashboard metrics
        const daysSinceUpdate = Math.floor((Date.now() - dissertation.updated_at.getTime()) / (1000 * 60 * 60 * 24));
        const progressPercentage = Math.round((dissertation.pages_reviewed.length / dissertation.page_count) * 100);
        
        // Get pending comments count
        const pendingComments = await dissertationService.getPendingCommentsCount(dissertation.id);
        
        // Get student name from profiles
        const studentProfile = studentProfiles.get(dissertation.student_id);
        const studentName = studentProfile ? studentProfile.displayName : 'Unknown Student';
        
        dashboard.push({
          student_id: dissertation.student_id,
          student_name: studentName,
          document_id: dissertation.id,
          document_title: dissertation.title,
          last_edited: dissertation.updated_at,
          pages_completed: dissertation.pages_reviewed.length,
          total_pages: dissertation.page_count,
          word_count: dissertation.word_count,
          status: dissertation.status,
          days_since_last_update: daysSinceUpdate,
          pending_comments: pendingComments,
          chapters_ready_for_review: dissertation.chapters_ready_for_review?.length || 0,
          progress_percentage: progressPercentage
        });
      }
      
      // Sort by last edited time
      dashboard.sort((a, b) => new Date(b.last_edited).getTime() - new Date(a.last_edited).getTime());
      
      setDashboardData(dashboard);
      console.log(`Loaded ${dashboard.length} dissertations for supervisor dashboard`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load dashboard';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [user?.uid, user?.role]);

  useEffect(() => {
    loadDashboard();
  }, [loadDashboard]);

  return {
    dashboardData,
    loading,
    error,
    refreshDashboard: loadDashboard
  };
}; 