import { useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useLocation } from 'react-router-dom';
import { getStudents } from '@/api/students';

/**
 * A hook that manages section-specific data loading
 * Only loads data for the current section being viewed
 */
export const useSectionLoader = () => {
  const queryClient = useQueryClient();
  const location = useLocation();
  
  useEffect(() => {
    // When the path changes, only load/refresh data for the current section
    const path = location.pathname;
    
    // Pre-fetch data for the current section only
    if (path.includes('/dashboard/enrollment')) {
      // Only load enrollment-related data using Firebase API
      queryClient.prefetchQuery({
        queryKey: ['students'],
        queryFn: getStudents,
        staleTime: 1000 * 60 * 5, // 5 minutes
      });
      
      // Note: Attendance data loading commented out until proper API is implemented
      // if (path.includes('/attendance')) {
      //   queryClient.prefetchQuery({
      //     queryKey: ['attendance'],
      //     queryFn: () => fetch('/api/attendance').then(res => res.json()),
      //     staleTime: 1000 * 60 * 5,
      //   });
      // }
    }
    // Note: Other sections commented out until proper Firebase APIs are implemented
    // else if (path.includes('/dashboard/finance')) {
    //   // Only load finance-related data
    //   const financeSections = ['fees', 'transactions'];
    //   const currentSection = financeSections.find(section => path.includes(section)) || 'overview';
    //   
    //   queryClient.prefetchQuery({
    //     queryKey: ['finances', currentSection],
    //     queryFn: () => fetch(`/api/finances/${currentSection}`).then(res => res.json()),
    //     staleTime: 1000 * 60 * 5,
    //   });
    // } else if (path.includes('/dashboard/courses')) {
    //   queryClient.prefetchQuery({
    //     queryKey: ['courses'],
    //     queryFn: () => fetch('/api/courses').then(res => res.json()),
    //     staleTime: 1000 * 60 * 5,
    //   });
    // } else if (path.includes('/dashboard/levels')) {
    //   queryClient.prefetchQuery({
    //     queryKey: ['levels'],
    //     queryFn: () => fetch('/api/levels').then(res => res.json()),
    //     staleTime: 1000 * 60 * 5,
    //   });
    // } else if (path.includes('/dashboard/exams')) {
    //   queryClient.prefetchQuery({
    //     queryKey: ['exams'],
    //     queryFn: () => fetch('/api/exams').then(res => res.json()),
    //     staleTime: 1000 * 60 * 5,
    //   });
    // } else if (path.includes('/dashboard/settings')) {
    //   queryClient.prefetchQuery({
    //     queryKey: ['settings'],
    //     queryFn: () => fetch('/api/settings').then(res => res.json()),
    //     staleTime: 1000 * 60 * 5,
    //   });
    // }
    // Dashboard overview can load minimal data or have its own specific queries
    
  }, [location.pathname, queryClient]);
}; 