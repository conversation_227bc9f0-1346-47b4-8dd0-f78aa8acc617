import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

/**
 * Custom hook to manage scroll position when navigating between routes
 * Preserves scroll position when navigating back and resets it when navigating to a new page
 */
export const useScrollPosition = () => {
  const { pathname } = useLocation();

  useEffect(() => {
    // Store current scroll positions keyed by path
    const saveScrollPosition = () => {
      const scrollPositions = JSON.parse(sessionStorage.getItem('scrollPositions') || '{}');
      scrollPositions[pathname] = window.scrollY;
      sessionStorage.setItem('scrollPositions', JSON.stringify(scrollPositions));
    };

    // Save scroll position when leaving the page
    window.addEventListener('beforeunload', saveScrollPosition);
    
    // Save position on navigation within the app
    const handleBeforeNavigate = () => {
      saveScrollPosition();
    };
    
    document.addEventListener('beforeNavigate', handleBeforeNavigate);

    // Restore scroll position for this route if it exists, otherwise scroll to top
    const scrollPositions = JSON.parse(sessionStorage.getItem('scrollPositions') || '{}');
    
    // Small delay to ensure the DOM has updated
    setTimeout(() => {
      if (scrollPositions[pathname]) {
        window.scrollTo(0, scrollPositions[pathname]);
      } else {
        window.scrollTo(0, 0);
      }
    }, 100);

    return () => {
      window.removeEventListener('beforeunload', saveScrollPosition);
      document.removeEventListener('beforeNavigate', handleBeforeNavigate);
      
      // Save position when unmounting
      saveScrollPosition();
    };
  }, [pathname]);
}; 