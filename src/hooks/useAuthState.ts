import { useState } from 'react';
import { User } from '@/lib/api-client';

export function useAuthState() {
  const [user, setUser] = useState<User | null>(null);
  const [userProfile, setUserProfile] = useState<User | null>(null);
  // Initialize loading as true since we need to check auth state when the app loads
  const [isLoading, setIsLoading] = useState<boolean>(false);

  return {
    user,
    setUser,
    userProfile,
    setUserProfile,
    isLoading,
    setIsLoading,
  };
}
