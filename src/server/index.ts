import express from 'express';
import cors from 'cors';
import userRoutes from './routes/userRoutes';
import dashboardRoutes from './routes/dashboardRoutes';

const app = express();
const port = process.env.PORT || 3000;

app.use(cors());
app.use(express.json());

// Mount routes
app.use('/api/users', userRoutes);
app.use('/api/dashboard', dashboardRoutes);

// Start server
app.listen(port, () => {
  console.log(`Server is running on port ${port}`);
});