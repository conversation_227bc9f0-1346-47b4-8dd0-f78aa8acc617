
import { Request, Response } from 'express';
import prisma from '../lib/prisma';
import type { Student } from '@/types/student';
import type { Payment } from '@/types/finance';

export const getEnrollmentTrends = async (_req: Request, res: Response) => {
  try {
    console.log('Fetching enrollment trends from database');
    const students = await prisma.student.findMany({
      select: {
        dateOfRegistration: true,
      },
      orderBy: {
        dateOfRegistration: 'asc',
      },
    });

    const monthlyData = students.reduce((acc: any[], student: Student) => {
      const month = new Date(student.date_of_registration).toLocaleString('default', { month: 'long' });
      const existingMonth = acc.find(item => item.month === month);
      
      if (existingMonth) {
        existingMonth.students += 1;
      } else {
        acc.push({ month, students: 1 });
      }
      
      return acc;
    }, []);

    res.json(monthlyData);
  } catch (error) {
    console.error('Error in getEnrollmentTrends:', error);
    res.status(500).json({ error: 'Failed to fetch enrollment trends' });
  }
};

export const getRevenue = async (_req: Request, res: Response) => {
  try {
    console.log('Fetching revenue data from database');
    const payments = await prisma.payment.findMany({
      select: {
        datePaid: true,
        amount: true,
      },
      orderBy: {
        datePaid: 'asc',
      },
    });

    const monthlyData = payments.reduce((acc: any[], payment: Payment) => {
      const month = new Date(payment.datePaid).toLocaleString('default', { month: 'long' });
      const existingMonth = acc.find(item => item.month === month);
      
      if (existingMonth) {
        existingMonth.income += payment.amount;
      } else {
        acc.push({ month, income: payment.amount, expenses: 0 });
      }
      
      return acc;
    }, []);

    res.json(monthlyData);
  } catch (error) {
    console.error('Error in getRevenue:', error);
    res.status(500).json({ error: 'Failed to fetch revenue data' });
  }
};

interface CourseWithStudentCount {
  name: string;
  _count: {
    students: number;
  };
}

export const getCourseDistribution = async (_req: Request, res: Response) => {
  try {
    console.log('Fetching course distribution from database');
    const courses = await prisma.course.findMany({
      include: {
        _count: {
          select: { students: true },
        },
      },
    });

    const distribution = courses.map((course: CourseWithStudentCount) => ({
      name: course.name,
      students: course._count.students,
    }));

    res.json(distribution);
  } catch (error) {
    console.error('Error in getCourseDistribution:', error);
    res.status(500).json({ error: 'Failed to fetch course distribution' });
  }
};
