@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;

    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;

    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Dissertation Editor Styles */
.dissertation-editor {
  font-family: 'Times New Roman', serif;
}

.dissertation-paper {
  font-family: 'Times New Roman', serif;
  font-size: 12pt;
  line-height: 1.5;
  color: #000;
  background: white;
}

/* Typography Styles */
.dissertation-content h1 {
  font-size: 18pt !important;
  font-weight: bold !important;
  margin: 24pt 0 12pt 0 !important;
  text-align: center !important;
  font-family: 'Times New Roman', serif !important;
}

.dissertation-content h2 {
  font-size: 16pt !important;
  font-weight: bold !important;
  margin: 18pt 0 6pt 0 !important;
  font-family: 'Times New Roman', serif !important;
}

.dissertation-content h3 {
  font-size: 14pt !important;
  font-weight: bold !important;
  margin: 12pt 0 6pt 0 !important;
  font-family: 'Times New Roman', serif !important;
}

.dissertation-content h4 {
  font-size: 13pt !important;
  font-weight: bold !important;
  margin: 12pt 0 6pt 0 !important;
  font-family: 'Times New Roman', serif !important;
}

.dissertation-content h5 {
  font-size: 12pt !important;
  font-weight: bold !important;
  margin: 12pt 0 6pt 0 !important;
  font-family: 'Times New Roman', serif !important;
}

.dissertation-content h6 {
  font-size: 11pt !important;
  font-weight: bold !important;
  margin: 12pt 0 6pt 0 !important;
  font-family: 'Times New Roman', serif !important;
}

.dissertation-content p {
  margin: 0 0 12pt 0 !important;
  text-align: justify !important;
  font-family: 'Times New Roman', serif !important;
  font-size: 12pt !important;
  line-height: 1.5 !important;
}

/* Text Style Support */
.dissertation-content .dissertation-text-style {
  font-family: inherit;
}

.dissertation-content [style*="font-size"] {
  line-height: 1.4 !important;
}

/* Table Styles */
.dissertation-content .dissertation-table {
  width: 100% !important;
  border-collapse: collapse !important;
  margin: 12pt 0 !important;
  font-size: 11pt !important;
  font-family: 'Times New Roman', serif !important;
  table-layout: fixed !important;
  display: table !important;
  border: 1px solid #000 !important;
}

.dissertation-content .dissertation-table-row {
  display: table-row !important;
}

.dissertation-content .dissertation-table-cell,
.dissertation-content .dissertation-table-header {
  border: 1px solid #000 !important;
  padding: 6pt 8pt !important;
  text-align: left !important;
  vertical-align: top !important;
  position: relative !important;
  min-width: 50px !important;
  word-wrap: break-word !important;
  display: table-cell !important;
}

.dissertation-content .dissertation-table-header {
  background-color: #f0f0f0 !important;
  font-weight: bold !important;
}

.dissertation-content .dissertation-table-row:nth-child(even) .dissertation-table-cell {
  background-color: #f9f9f9 !important;
}

.dissertation-content .dissertation-table-cell:hover,
.dissertation-content .dissertation-table-header:hover {
  background-color: #e6f3ff !important;
  cursor: text !important;
}

/* Table Selection and Editing */
.dissertation-content .dissertation-table .selectedCell {
  background-color: #b3d9ff !important;
}

.dissertation-content .dissertation-table .column-resize-handle {
  position: absolute;
  right: -2px;
  top: 0;
  bottom: 0;
  width: 4px;
  cursor: col-resize;
  background-color: transparent;
}

.dissertation-content .dissertation-table .column-resize-handle:hover {
  background-color: #007acc;
}

/* List Styles */
.dissertation-content ul, 
.dissertation-content ol,
.dissertation-content .dissertation-bullet-list,
.dissertation-content .dissertation-ordered-list {
  margin: 12pt 0 !important;
  padding-left: 24pt !important;
  font-family: 'Times New Roman', serif !important;
}

.dissertation-content li,
.dissertation-content .dissertation-list-item {
  margin: 6pt 0 !important;
  font-size: 12pt !important;
  line-height: 1.5 !important;
  list-style-position: outside !important;
}

.dissertation-content ul li,
.dissertation-content .dissertation-bullet-list .dissertation-list-item {
  list-style-type: disc !important;
}

.dissertation-content ol li,
.dissertation-content .dissertation-ordered-list .dissertation-list-item {
  list-style-type: decimal !important;
}

/* Nested lists */
.dissertation-content ul ul li,
.dissertation-content .dissertation-bullet-list .dissertation-bullet-list .dissertation-list-item {
  list-style-type: circle !important;
}

.dissertation-content ul ul ul li,
.dissertation-content .dissertation-bullet-list .dissertation-bullet-list .dissertation-bullet-list .dissertation-list-item {
  list-style-type: square !important;
}

/* Text Alignment */
.dissertation-content [style*="text-align: left"],
.dissertation-content .text-align-left {
  text-align: left !important;
}

.dissertation-content [style*="text-align: center"],
.dissertation-content .text-align-center {
  text-align: center !important;
}

.dissertation-content [style*="text-align: right"],
.dissertation-content .text-align-right {
  text-align: right !important;
}

.dissertation-content [style*="text-align: justify"],
.dissertation-content .text-align-justify {
  text-align: justify !important;
}

/* Image Styles */
.dissertation-content .dissertation-image {
  max-width: 100% !important;
  height: auto !important;
  display: block !important;
  margin: 12pt auto !important;
  border: 1px solid #ddd !important;
  border-radius: 4px !important;
  padding: 4px !important;
  background-color: #fff !important;
  object-fit: contain !important;
  position: relative !important;
  z-index: 1 !important;
}

.dissertation-content .dissertation-image:hover {
  border-color: #007acc !important;
  cursor: pointer !important;
}

/* Blockquote Styles */
.dissertation-content blockquote {
  margin: 12pt 24pt !important;
  padding: 12pt !important;
  border-left: 3px solid #ccc !important;
  background-color: #f9f9f9 !important;
  font-style: italic !important;
  font-family: 'Times New Roman', serif !important;
}

/* Code Styles */
.dissertation-content code {
  font-family: 'Courier New', monospace !important;
  font-size: 11pt !important;
  background-color: #f5f5f5 !important;
  padding: 2pt 4pt !important;
  border-radius: 2px !important;
  border: 1px solid #ddd !important;
}

.dissertation-content pre {
  font-family: 'Courier New', monospace !important;
  font-size: 10pt !important;
  background-color: #f5f5f5 !important;
  padding: 12pt !important;
  border-radius: 4px !important;
  border: 1px solid #ddd !important;
  margin: 12pt 0 !important;
  overflow-x: auto !important;
}

/* Link Styles */
.dissertation-content a {
  color: #0066cc !important;
  text-decoration: underline !important;
}

.dissertation-content a:hover {
  color: #004499 !important;
}

/* Highlight Styles */
.dissertation-content mark,
.dissertation-content .dissertation-highlight {
  background-color: #ffff00 !important;
  padding: 0 2px !important;
}

/* Page Break Styles */
.dissertation-content .page-break {
  page-break-before: always !important;
  page-break-after: auto !important;
  height: 1px !important;
  margin: 20px 0 !important;
  border-top: 1px dashed #ccc !important;
  position: relative !important;
  width: 100% !important;
}

.dissertation-content .page-break::after {
  content: "Page Break" !important;
  position: absolute !important;
  top: -10px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  background: white !important;
  padding: 0 8px !important;
  font-size: 10px !important;
  color: #666 !important;
  font-family: Arial, sans-serif !important;
}

/* Toolbar Styles */
.dissertation-toolbar {
  background: #f8f9fa !important;
  border-bottom: 1px solid #dee2e6 !important;
}

.dissertation-toolbar .btn-active {
  background-color: #e9ecef !important;
  border-color: #dee2e6 !important;
}

/* Focus and Selection Styles */
.dissertation-content:focus {
  outline: none !important;
}

.dissertation-content .ProseMirror-focused {
  outline: none !important;
}

.dissertation-content .ProseMirror-selectednode {
  outline: 2px solid #007acc !important;
  outline-offset: 2px !important;
}

/* Print Styles */
@media print {
  .dissertation-paper {
    box-shadow: none !important;
    margin: 0 !important;
    width: 100% !important;
    min-height: auto !important;
    padding: 1in !important;
  }
  
  .dissertation-content .page-break {
    page-break-before: always !important;
    height: 0 !important;
    margin: 0 !important;
    border: none !important;
  }
  
  .dissertation-content .page-break::after {
    display: none !important;
  }
  
  .dissertation-toolbar,
  .dissertation-sidebar {
    display: none !important;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .dissertation-paper {
    width: 100% !important;
    margin: 0 !important;
    padding: 20px !important;
    box-shadow: none !important;
  }
  
  .dissertation-content h1 {
    font-size: 16pt !important;
  }
  
  .dissertation-content h2 {
    font-size: 14pt !important;
  }
  
  .dissertation-content h3 {
    font-size: 13pt !important;
  }
  
  .dissertation-content .dissertation-table {
    font-size: 10pt !important;
  }
  
  .dissertation-content .dissertation-table-cell,
  .dissertation-content .dissertation-table-header {
    padding: 4pt 6pt !important;
  }
}

/* Animation for auto-save indicator */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.auto-save-pulse {
  animation: pulse 1s ease-in-out infinite;
}

/* Custom scrollbar for editor */
.dissertation-content::-webkit-scrollbar {
  width: 8px;
}

.dissertation-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.dissertation-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.dissertation-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Dissertation Editor Container with Pagination */
.dissertation-editor-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
  overflow: auto;
}

/* Page styling */
.dissertation-content {
  position: relative;
  background: white;
  width: 210mm; /* A4 width */
  min-height: 297mm; /* A4 height */
  height: auto;
  margin: 0 auto;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  font-family: "Times New Roman", serif;
  line-height: 1.8;
  font-size: 12pt;
  padding: 25mm 25mm 25mm 25mm; /* Standard margins */
  box-sizing: border-box;
  overflow: visible;
  page-break-after: always;
}

/* Page break styling */
.dissertation-content .page-break {
  page-break-before: always !important;
  page-break-after: auto !important;
  height: 1px !important;
  margin: 20px 0 !important;
  border-top: 1px dashed #ccc !important;
  position: relative !important;
  width: 100% !important;
}

.dissertation-content .page-break::after {
  content: "Page Break" !important;
  position: absolute !important;
  top: -10px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  background: white !important;
  padding: 0 8px !important;
  font-size: 10px !important;
  color: #666 !important;
  font-family: Arial, sans-serif !important;
}

/* Print styles for proper pagination */
@media print {
  .dissertation-content {
    box-shadow: none !important;
    margin: 0 !important;
    padding: 0.5in !important;
    page-break-after: always !important;
  }
  
  .dissertation-content .page-break {
    display: none !important;
  }
}

.ProseMirror {
  overflow: visible !important;
}

.ProseMirror table {
  overflow: visible !important;
  position: relative !important;
  z-index: 1 !important;
}

.ProseMirror img {
  max-width: 100% !important;
  height: auto !important;
}

/* Enhanced Resizable and Draggable Elements */
.resizable-image-wrapper {
  display: inline-block;
  position: relative;
  margin: 10px 0;
  transition: all 0.2s ease;
}

.resizable-image-container {
  position: relative;
  display: inline-block;
  box-shadow: 0 0 0 1px transparent;
  transition: box-shadow 0.2s ease;
}

.resizable-image-container:hover {
  box-shadow: 0 0 0 2px rgba(0, 119, 204, 0.3);
}

.resizable-image-container:hover .resize-handle {
  opacity: 1;
}

.resizable-image-container .resize-handle {
  opacity: 0;
  transition: opacity 0.2s ease, transform 0.2s ease;
}

.resizable-image-container .resize-handle:hover {
  transform: scale(1.2);
  background-color: #0088ee;
}

.resizable-image-container.dragging {
  opacity: 0.7;
  cursor: grabbing;
}

.resizable-image-container.resizing {
  opacity: 0.7;
}

.draggable-table-wrapper {
  position: relative;
  margin: 30px 0 10px 0;
  transition: all 0.2s ease;
}

.draggable-table-container {
  position: relative;
  box-shadow: 0 0 0 1px transparent;
  transition: box-shadow 0.2s ease;
}

.draggable-table-container:hover {
  box-shadow: 0 0 0 2px rgba(0, 119, 204, 0.3);
}

.draggable-table-container:hover .table-drag-handle,
.draggable-table-container:hover .resize-handle-right,
.draggable-table-container:hover .resize-handle-corner {
  opacity: 1;
}

.draggable-table-container .table-drag-handle,
.draggable-table-container .resize-handle-right,
.draggable-table-container .resize-handle-corner {
  opacity: 0;
  transition: opacity 0.2s ease, transform 0.2s ease, background-color 0.2s ease;
}

.draggable-table-container .table-drag-handle:hover {
  background-color: #e0e0e0;
}

.draggable-table-container.dragging {
  opacity: 0.7;
}

.draggable-table-container.resizing {
  opacity: 0.7;
}

.resize-handle-right:hover {
  background-color: rgba(0, 119, 204, 0.3);
}

.resize-handle-corner:hover {
  transform: scale(1.2);
  background-color: #0088ee;
}

/* Selection styles */
.ProseMirror-selectednode {
  outline: 2px solid #0077cc !important;
  border-radius: 2px;
}