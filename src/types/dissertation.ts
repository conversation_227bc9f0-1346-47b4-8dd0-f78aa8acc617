export interface DissertationDocument {
  id: string;
  title: string;
  content: string; // JSON content from TipTap editor
  student_id: string;
  supervisor_id: string; // lecturer/teacher ID
  created_at: Date;
  updated_at: Date;
  last_edited_by: string;
  version: number;
  status: 'draft' | 'ready_for_review' | 'under_review' | 'approved' | 'needs_revision';
  word_count: number;
  page_count: number;
  auto_save_enabled: boolean;
  submission_deadline?: Date;
  
  // Metadata
  abstract?: string;
  keywords?: string[];
  bibliography?: string;
  table_of_contents?: TableOfContentsItem[];
  
  // Collaboration
  active_collaborators: string[]; // User IDs currently editing
  last_viewed_by_supervisor?: Date;
  
  // Review tracking
  pages_reviewed: number[];
  chapters_ready_for_review: string[];
}

export interface TableOfContentsItem {
  id: string;
  title: string;
  level: number; // 1 for chapter, 2 for section, etc.
  page_number: number;
  anchor_id: string;
}

export interface DissertationComment {
  id: string;
  document_id: string;
  author_id: string;
  author_name: string;
  author_role: 'student' | 'supervisor';
  content: string;
  position: CommentPosition;
  type: 'inline' | 'page' | 'paragraph';
  status: 'active' | 'resolved' | 'archived';
  visibility: 'public' | 'private'; // private comments only visible to supervisor
  created_at: Date;
  updated_at: Date;
  replies?: DissertationCommentReply[];
  
  // Highlighting
  highlighted_text?: string;
  highlight_color?: string;
  
  // Review tags
  tag?: 'needs_revision' | 'approved' | 'plagiarized' | 'good_work' | 'clarification_needed';
}

export interface CommentPosition {
  page_number: number;
  paragraph_id?: string;
  text_selection?: {
    from: number;
    to: number;
    text: string;
  };
  coordinates?: {
    x: number;
    y: number;
  };
}

export interface DissertationCommentReply {
  id: string;
  comment_id: string;
  author_id: string;
  author_name: string;
  author_role: 'student' | 'supervisor';
  content: string;
  created_at: Date;
}

export interface DissertationVersion {
  id: string;
  document_id: string;
  version_number: number;
  content: string;
  created_at: Date;
  created_by: string;
  change_summary: string;
  word_count: number;
  page_count: number;
}

export interface SupervisorDashboardData {
  student_id: string;
  student_name: string;
  document_id: string;
  document_title: string;
  last_edited: Date;
  pages_completed: number;
  total_pages: number;
  word_count: number;
  status: DissertationDocument['status'];
  days_since_last_update: number;
  pending_comments: number;
  chapters_ready_for_review: number;
  progress_percentage: number;
}

export interface StudentActivity {
  id: string;
  student_id: string;
  document_id: string;
  activity_type: 'page_view' | 'edit' | 'save' | 'comment' | 'submission' | 'lecture_access';
  details: string;
  timestamp: Date;
  page_number?: number;
  duration_minutes?: number;
}

export interface CollaborationCursor {
  user_id: string;
  user_name: string;
  user_role: 'student' | 'supervisor';
  position: number;
  selection?: {
    from: number;
    to: number;
  };
  color: string;
  last_updated: Date;
}

export interface DissertationSettings {
  id: string;
  document_id: string;
  auto_save_interval: number; // in seconds
  page_margins: {
    top: number;
    bottom: number;
    left: number;
    right: number;
  };
  font_family: string;
  font_size: number;
  line_spacing: number;
  page_orientation: 'portrait' | 'landscape';
  citation_style: 'apa' | 'mla' | 'chicago' | 'harvard';
  show_word_count: boolean;
  show_page_numbers: boolean;
  enable_spell_check: boolean;
  enable_grammar_check: boolean;
}

export interface DissertationNotification {
  id: string;
  recipient_id: string;
  sender_id: string;
  document_id: string;
  type: 'comment_added' | 'document_updated' | 'review_requested' | 'status_changed' | 'deadline_reminder';
  title: string;
  message: string;
  read: boolean;
  created_at: Date;
  action_url?: string;
}

// Editor state interfaces
export interface EditorState {
  isEditing: boolean;
  currentPage: number;
  totalPages: number;
  wordCount: number;
  characterCount: number;
  selectedText: string;
  cursorPosition: number;
  isAutoSaving: boolean;
  lastSaved: Date;
  hasUnsavedChanges: boolean;
}

export interface ReviewState {
  isReviewMode: boolean;
  selectedComment?: DissertationComment;
  activeComments: DissertationComment[];
  showPrivateComments: boolean;
  filterByTag?: DissertationComment['tag'];
  highlightedText?: {
    from: number;
    to: number;
    color: string;
  };
} 