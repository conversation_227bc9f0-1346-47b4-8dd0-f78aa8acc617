export type ActivityType = 
  | "login"
  | "logout"
  | "student_created"
  | "student_updated"
  | "student_deleted"
  | "student_image_uploaded"
  | "payment_created"
  | "payment_updated"
  | "course_created"
  | "course_updated"
  | "course_deleted"
  | "course_fee_updated"
  | "level_created"
  | "level_updated"
  | "level_deleted"
  | "transaction_created"
  | "transaction_updated"
  | "transaction_deleted"
  | "settings_updated"
  | "attendance_created"
  | "attendance_updated"
  | "attendance_bulk_created"
  | "exam_created"
  | "exam_updated"
  | "exam_deleted"
  | "exam_result_created"
  | "exam_result_updated"
  | "exam_results_bulk_created"
  | "subject_created"
  | "subject_updated"
  | "subject_deleted";

export interface ActivityLog {
  id: string;
  user_id?: string;
  activity_type: ActivityType;
  description: string;
  metadata?: Record<string, any>;
  created_at: string;
  ip_address?: string;
  user_agent?: string;
}
