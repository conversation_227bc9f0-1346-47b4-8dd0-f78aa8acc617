
import { ProfileTables } from './database/auth';
import { SettingsTables } from './database/settings';
import { CourseTables } from './database/courses';
import { ExamTables } from './database/exams';
import { StudentTables } from './database/students';
import { AttendanceTables } from './database/attendance';
import { ActivityTables } from './database/activity';
import { AcademicTables } from './database/academic';

export type Database = {
  public: {
    Tables: ProfileTables & 
            SettingsTables & 
            CourseTables & 
            ExamTables & 
            StudentTables & 
            AttendanceTables &
            ActivityTables &
            AcademicTables
  }
};

export type { Profile } from './database/auth';
export type { GeneralSettings } from './database/settings';
export type { Course } from './database/courses';
export type { Exam } from './database/exams';
export type { Student } from './database/students';
export type { AttendanceRecord } from './database/attendance';
export type { ActivityLog, ActivityType } from './database/activity';
export type { AcademicRecord } from './database/academic';
