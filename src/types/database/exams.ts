
import { TableBase } from './utils'

export interface Exam extends TableBase {
  id: string
  name: string
  subject: string
  course_id: string | null
  level_id: string | null
  class: string
  type: string
  date: string
  time: string
  status: string
}

export interface ExamTables {
  exams: {
    Row: Exam
    Insert: Omit<Exam, 'created_at' | 'updated_at'> & Partial<TableBase>
    Update: Partial<Exam>
    Relationships: [
      {
        foreignKeyName: "exams_course_id_fkey"
        columns: ["course_id"]
        isOneToOne: false
        referencedRelation: "courses"
        referencedColumns: ["id"]
      },
      {
        foreignKeyName: "exams_level_id_fkey"
        columns: ["level_id"]
        isOneToOne: false
        referencedRelation: "levels"
        referencedColumns: ["id"]
      }
    ]
  }
}
