
import { TableBase } from './utils'

export interface Course extends TableBase {
  id: string
  name: string
  code: string
  description: string | null
  course_type: string | null
  parent_course_id: string | null
  start_date: string | null
  end_date: string | null
  duration_months: number | null
  auto_renewal: boolean | null
  renewal_period_months: number | null
  section_order: number | null
  created_by: string | null
  updated_by: string | null
}

export interface CourseTables {
  courses: {
    Row: Course
    Insert: Omit<Course, 'created_at' | 'updated_at'> & Partial<TableBase>
    Update: Partial<Course>
    Relationships: [
      {
        foreignKeyName: "courses_parent_course_id_fkey"
        columns: ["parent_course_id"]
        isOneToOne: false
        referencedRelation: "courses"
        referencedColumns: ["id"]
      }
    ]
  }
}
