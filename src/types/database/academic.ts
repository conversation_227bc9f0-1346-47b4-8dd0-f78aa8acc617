
import { TableBase } from './utils';

export interface AcademicRecord extends TableBase {
  id: string;
  student_id: string;
  subject: string;
  grade: string;
  term: string;
  status: string;
  notes?: string;
}

export interface AcademicTables {
  academic_records: {
    Row: AcademicRecord;
    Insert: Omit<AcademicRecord, 'created_at' | 'updated_at'> & Partial<TableBase>;
    Update: Partial<AcademicRecord>;
  }
}
