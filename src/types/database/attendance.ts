
import { TableBase } from './utils';

export type AttendanceStatus = 'present' | 'absent';

export interface AttendanceRecord extends TableBase {
  id: string;
  student_id: string;
  date: string;
  time_in: string | null;
  time_out: string | null;
  status: AttendanceStatus;
  notes: string | null;
  marked_by: string | null;
}

export interface AttendanceTables {
  attendance_records: {
    Row: AttendanceRecord;
    Insert: Omit<AttendanceRecord, 'created_at' | 'updated_at'> & Partial<TableBase>;
    Update: Partial<AttendanceRecord>;
  }
}
