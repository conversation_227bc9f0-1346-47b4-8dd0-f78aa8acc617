
import { TableBase } from './utils'

export interface ExamResult extends TableBase {
  id: string
  exam_id: string
  student_id: string
  marks: number
  grade: string | null
  remarks: string | null
}

export interface ExamResultTables {
  exam_results: {
    Row: ExamResult
    Insert: Omit<ExamResult, 'created_at' | 'updated_at'> & Partial<TableBase>
    Update: Partial<ExamResult>
    Relationships: [
      {
        foreignKeyName: "exam_results_exam_id_fkey"
        columns: ["exam_id"]
        isOneToOne: false
        referencedRelation: "exams"
        referencedColumns: ["id"]
      },
      {
        foreignKeyName: "exam_results_student_id_fkey"
        columns: ["student_id"]
        isOneToOne: false
        referencedRelation: "students"
        referencedColumns: ["id"]
      }
    ]
  }
}
