
import { TableBase } from './utils';

export interface Student extends TableBase {
  id: string;
  student_id: string;
  name: string;
  date_of_birth: string;
  date_of_registration: string;
  nationality: string;
  gender: string;
  address: string;
  mobile_number: string;
  whatsapp_number: string;
  parent_name: string;
  parent_mobile: string;
  parent_whatsapp: string;
  parent_email: string;
  parent_occupation: string;
  passport_picture: string | null;
  course_id: string;
  level_id: string;
  enrollment_status: string;
  payment_status?: string;
}

export interface StudentTables {
  students: {
    Row: Student;
    Insert: Omit<Student, 'created_at' | 'updated_at'> & Partial<TableBase>;
    Update: Partial<Student>;
  }
}
