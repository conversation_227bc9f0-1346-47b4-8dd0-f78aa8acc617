
import { TableBase } from './utils';

export type ActivityType = 
  | "login"
  | "logout"
  | "student_created"
  | "student_updated"
  | "student_deleted"
  | "payment_created"
  | "payment_updated"
  | "course_created"
  | "course_updated"
  | "level_created"
  | "level_updated"
  | "transaction_created"
  | "transaction_updated"
  | "settings_updated";

export interface ActivityLog extends TableBase {
  id: string;
  user_id?: string;
  activity_type: ActivityType;
  description: string;
  metadata?: Record<string, any>;
  ip_address?: string;
  user_agent?: string;
}

export interface ActivityTables {
  activity_logs: {
    Row: ActivityLog;
    Insert: Omit<ActivityLog, 'created_at'> & Partial<Pick<ActivityLog, 'created_at'>>;
    Update: Partial<ActivityLog>;
  }
}
