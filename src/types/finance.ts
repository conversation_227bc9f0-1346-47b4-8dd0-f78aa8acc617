export interface Transaction {
  id: string;
  date: string;
  description: string;
  category: string | TransactionCategory;
  type: TransactionType;
  amount: number;
  status: TransactionStatus;
  notes?: string;
  created_at?: string;
  updated_at?: string;
  payment_id?: string;
  student_id?: string;
}

export type TransactionCategory = 'tuition' | 'supplies' | 'salary' | 'maintenance' | 'other';
export type TransactionType = 'income' | 'expense';
export type TransactionStatus = 'completed' | 'pending' | 'cancelled';

export interface Payment {
  id: string;
  amount: number;
  datePaid: string;
  payment_due_date: string;
  status: string;
  student_id: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}
