export type UserRole = 'super_admin' | 'enrollment_admin' | 'finance_admin' | 'teacher';

export interface User {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  password?: string;
  created_at?: string;
  updated_at?: string;
}

export interface Teacher extends User {
  role: 'teacher';
  assigned_courses?: string[]; // Array of course IDs
  assigned_levels?: string[]; // Array of level IDs
  specialization?: string;
  bio?: string;
  contact_number?: string;
}
