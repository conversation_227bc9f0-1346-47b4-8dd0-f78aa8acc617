import React, { createContext, useContext, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { User } from '@/lib/api-client';
import { useAuthState } from '@/hooks/useAuthState';
import { toast } from 'sonner';
import { AuthService } from '@/lib/auth';
import { UserRole } from '@/types/user';
import { handleError } from '@/utils/error-handling';

interface AuthContextType {
  user: User | null;
  userProfile: User | null; // Using User type from API client instead of ProfileDocument
  isLoading: boolean;
  logout: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const {
    user,
    setUser,
    userProfile,
    setUserProfile,
    isLoading,
    setIsLoading
  } = useAuthState();
  const navigate = useNavigate();
  const location = useLocation();

  // Initialize auth state
  useEffect(() => {
    let mounted = true;
    console.log('AuthProvider initializing...');

    // Set up auth state change listener
    const unsubscribe = AuthService.onAuthStateChanged(async (user) => {
      console.log('Auth state changed:', user ? 'SIGNED_IN' : 'SIGNED_OUT');

      if (!mounted) return;

      setIsLoading(true);

      try {
        if (user) {
          setUser(user);
          setUserProfile(user); // User object already contains profile data
          console.log('User set:', user);

          // Only navigate if we're on the login page
          if (location.pathname === '/login') {
            // Determine where to redirect based on user role
            let redirectPath = '/dashboard';

            // If user is a student, redirect to student dashboard
            if (user.role === 'student') {
              redirectPath = '/dashboard/student';
            }

            const from = (location.state as any)?.from || redirectPath;
            navigate(from, { replace: true });
          }
        } else {
          setUser(null);
          setUserProfile(null);

          if (location.pathname !== '/login') {
            navigate('/login', { replace: true });
          }
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        if (mounted) {
          toast.error(handleError(error, 'Authentication error occurred'));
          navigate('/login', { replace: true });
        }
      } finally {
        if (mounted) {
          setIsLoading(false);
        }
      }
    });

    // Cleanup subscription on unmount
    return () => {
      console.log('AuthProvider cleanup...');
      mounted = false;
      unsubscribe();
    };
  }, [navigate, location.pathname, setUser, setUserProfile, setIsLoading]);

  const logout = async () => {
    try {
      await AuthService.signOut();
      setUser(null);
      setUserProfile(null);
      navigate('/login', { replace: true });
      toast.success('Logged out successfully');
    } catch (error) {
      console.error('Logout error:', error);
      toast.error(handleError(error, 'Error logging out'));
    }
  };

  const value = {
    user,
    userProfile,
    isLoading,
    logout
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
