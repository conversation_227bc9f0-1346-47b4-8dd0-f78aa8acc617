import { useState, useEffect, Suspense, lazy, memo } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useMaintenanceMode } from '@/hooks/use-maintenance-mode';
import { Routes, Route, useLocation, useNavigate, Navigate } from 'react-router-dom';
import { TopNavigation } from '@/components/layout/TopNavigation';
import { Sidebar } from '@/components/layout/Sidebar';
import { MaintenanceMode } from '@/components/layout/MaintenanceMode';
import { DashboardOverview } from '@/components/dashboard/DashboardOverview';
import { useSectionLoader } from '@/hooks/useSectionLoader';
import { useScrollPosition } from '@/hooks/useScrollPosition';

// Lazy load section components to improve performance
const FinanceDashboard = lazy(() => import('@/components/finance/FinanceDashboard'));
const EnrollmentDashboard = lazy(() => import('@/components/enrollment/EnrollmentDashboard'));
const Settings = lazy(() => import('@/components/admin/Settings'));
const CourseManagement = lazy(() => import('@/components/admin/settings/CourseManagement'));
const LevelManagement = lazy(() => import('@/components/admin/settings/LevelManagement'));
const SubjectManagement = lazy(() => import('@/components/admin/settings/SubjectManagement'));
const ExamDashboard = lazy(() => import('@/components/exam/ExamDashboard'));
const TeacherDashboard = lazy(() => import('@/components/dashboard/TeacherDashboard'));
const StudentDashboard = lazy(() => import('@/components/dashboard/StudentDashboard'));
const AnnouncementManagement = lazy(() => import('@/components/admin/settings/AnnouncementManagement'));
const EventCalendarManagement = lazy(() => import('@/components/admin/settings/EventCalendarManagement'));
const TimetableManagement = lazy(() => import('@/components/admin/settings/TimetableManagement'));
const DissertationManagement = lazy(() => import('@/components/admin/DissertationManagement'));

// Memoize route components to prevent unnecessary re-renders
const MemoizedDashboardOverview = memo(DashboardOverview);
const MemoizedFinanceDashboard = memo((props: any) => <FinanceDashboard {...props} />);
const MemoizedEnrollmentDashboard = memo((props: any) => <EnrollmentDashboard {...props} />);
const MemoizedSettings = memo(Settings);
const MemoizedCourseManagement = memo(CourseManagement);
const MemoizedLevelManagement = memo(LevelManagement);
const MemoizedSubjectManagement = memo(SubjectManagement);
const MemoizedExamDashboard = memo((props: any) => <ExamDashboard {...props} />);
const MemoizedTeacherDashboard = memo((props: any) => <TeacherDashboard {...props} />);
const MemoizedStudentDashboard = memo((props: any) => <StudentDashboard {...props} />);
const MemoizedAnnouncementManagement = memo(AnnouncementManagement);
const MemoizedEventCalendarManagement = memo(EventCalendarManagement);
const MemoizedTimetableManagement = memo(TimetableManagement);
const MemoizedDissertationManagement = memo(DissertationManagement);

const Dashboard = () => {
  const { user, userProfile, logout } = useAuth();
  const navigate = useNavigate();
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [schoolName, setSchoolName] = useState('PTECH');
  const { isBlocked, isLoading } = useMaintenanceMode();
  const location = useLocation();
  
  // Use our custom hooks to load only section-specific data and preserve scroll position
  useSectionLoader();
  useScrollPosition();

  useEffect(() => {
    const storedSettings = localStorage.getItem('generalSettings');
    if (storedSettings) {
      const settings = JSON.parse(storedSettings);
      if (settings.schoolName && settings.schoolName !== 'Student Portal') {
        setSchoolName(settings.schoolName);
      }
    }
    
    // Listen for storage events to update school name in real-time
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'generalSettings') {
        try {
          const newSettings = JSON.parse(e.newValue || '{}');
          if (newSettings.schoolName && newSettings.schoolName !== 'Student Portal') {
            setSchoolName(newSettings.schoolName);
          }
        } catch (error) {
          console.error('Error parsing settings from localStorage:', error);
        }
      }
    };
    
    window.addEventListener('storage', handleStorageChange);
    
    // Add event listener for sidebar toggle
    const toggleSidebarHandler = () => {
      setSidebarOpen(prev => !prev);
    };
    
    document.addEventListener('toggle-sidebar', toggleSidebarHandler);
    
    return () => {
      window.removeEventListener('storage', handleStorageChange);
      document.removeEventListener('toggle-sidebar', toggleSidebarHandler);
    };
  }, []);

  // Handle role-based redirects in a useEffect to avoid updating during render
  useEffect(() => {
    if (!userProfile) return;
    
    const userRole = userProfile.role || 'user';
    const isEnrollmentAdmin = userRole === 'enrollment_admin';
    const isFinanceAdmin = userRole === 'finance_admin';
    const isTeacher = userRole === 'teacher';
    
    // Redirect enrollment admins to enrollment section
    if (isEnrollmentAdmin && !location.pathname.includes('/dashboard/enrollment')) {
      navigate('/dashboard/enrollment');
    }
    
    // Redirect finance admins to finance section
    if (isFinanceAdmin && !location.pathname.includes('/dashboard/finance')) {
      navigate('/dashboard/finance');
    }

    // Redirect teachers to teacher dashboard
    if (isTeacher && !location.pathname.includes('/dashboard/teacher')) {
      navigate('/dashboard/teacher');
    }
  }, [userProfile, location.pathname, navigate]);

  // Add isStudent check
  const isStudent = userProfile?.role === 'student';
  
  // Redirect students to student dashboard if they're on the root dashboard
  useEffect(() => {
    if (isStudent && location.pathname === '/dashboard') {
      navigate('/dashboard/student', { replace: true });
    }
  }, [isStudent, location.pathname, navigate]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-yellow-50 to-green-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-700" />
      </div>
    );
  }

  if (isBlocked) {
    return <MaintenanceMode logout={logout} />;
  }

  if (!user || !userProfile) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-yellow-50 to-green-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-700" />
      </div>
    );
  }

  // Default to regular user if role is not set
  const userRole = userProfile?.role || 'user';
  
  // Set permissions based on role
  const isSuperAdmin = userRole === 'super_admin';
  const showEnrollmentSection = userRole === 'enrollment_admin' || isSuperAdmin || userRole === 'admin';
  const showFinanceSection = userRole === 'finance_admin' || isSuperAdmin || userRole === 'admin';
  const showSettings = isSuperAdmin || userRole === 'admin';
  const isEnrollmentAdmin = userRole === 'enrollment_admin';
  const isFinanceAdmin = userRole === 'finance_admin';
  const isTeacher = userRole === 'teacher';

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-yellow-50 to-green-50">
      <TopNavigation
        setSidebarOpen={setSidebarOpen}
        sidebarOpen={sidebarOpen}
        schoolName={schoolName}
      />

      <Sidebar
        sidebarOpen={sidebarOpen}
        isSuperAdmin={isSuperAdmin}
        showEnrollmentSection={showEnrollmentSection}
        showFinanceSection={showFinanceSection}
        showSettings={showSettings}
        isTeacher={isTeacher}
        isStudent={isStudent}
        logout={logout}
      />

      <main
        className={`pt-16 min-h-screen transition-all duration-200 ${
          sidebarOpen ? 'lg:pl-64' : ''
        }`}
      >
        <div className="p-4">
          <Suspense fallback={
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-700" />
            </div>
          }>
            <Routes>
              {/* Student Dashboard */}
              {isStudent && (
                <Route path="student/*" element={<MemoizedStudentDashboard setSidebarOpen={setSidebarOpen} />} />
              )}
              
              {/* Redirect students to student dashboard if they try to access other routes */}
              {isStudent && <Route path="*" element={<Navigate to="/dashboard/student" replace />} />}
              
              {/* Only show dashboard overview for admins and super admins, not finance admins */}
              {(isSuperAdmin || userRole === 'admin') && <Route path="" element={<MemoizedDashboardOverview />} />}
              {showEnrollmentSection && (
                <>
                  <Route 
                    path="enrollment/*" 
                    element={<MemoizedEnrollmentDashboard setSidebarOpen={setSidebarOpen} />} 
                  />
                </>
              )}
              {showFinanceSection && (
                <Route 
                  path="finance/*" 
                  element={<MemoizedFinanceDashboard setSidebarOpen={setSidebarOpen} />} 
                />
              )}
              {showSettings && (
                <>
                  <Route path="settings/*" element={<MemoizedSettings />} />
                  <Route path="courses" element={<MemoizedCourseManagement />} />
                  <Route path="levels" element={<MemoizedLevelManagement />} />
                  <Route path="subjects" element={<MemoizedSubjectManagement />} />
                  <Route path="exams/*" element={<MemoizedExamDashboard />} />
                </>
              )}
              
              {/* Direct routes for announcements and events */}
              {(isSuperAdmin || userRole === 'admin') && (
                <>
                  <Route path="announcements" element={<MemoizedAnnouncementManagement />} />
                  <Route path="events" element={<MemoizedEventCalendarManagement />} />
                  <Route path="timetable" element={<MemoizedTimetableManagement />} />
                  <Route path="dissertations" element={<MemoizedDissertationManagement />} />
                </>
              )}
              
              {/* Teacher Dashboard Route */}
              {isTeacher && (
                <Route 
                  path="teacher/*" 
                  element={<MemoizedTeacherDashboard setSidebarOpen={setSidebarOpen} />} 
                />
              )}
              {/* Default route for admins and super admins */}
              {(isSuperAdmin || userRole === 'admin') && <Route path="*" element={<MemoizedDashboardOverview />} />}
              {/* Fallback for finance and enrollment admins if they somehow reach an invalid route */}
              {isFinanceAdmin && <Route path="*" element={<Navigate to="/dashboard/finance" replace />} />}
              {isEnrollmentAdmin && <Route path="*" element={<Navigate to="/dashboard/enrollment" replace />} />}
              {/* Fallback for teachers if they somehow reach an invalid route */}
              {isTeacher && <Route path="*" element={<Navigate to="/dashboard/teacher" replace />} />}
            </Routes>
          </Suspense>
        </div>
      </main>
    </div>
  );
};

export default Dashboard;
