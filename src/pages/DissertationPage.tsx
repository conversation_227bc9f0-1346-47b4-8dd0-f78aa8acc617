import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import DissertationEditor from '@/components/dissertation/DissertationEditor';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';

const DissertationPage: React.FC = () => {
  const { documentId } = useParams<{ documentId: string }>();
  const navigate = useNavigate();
  const { userProfile } = useAuth();

  const handleGoBack = () => {
    const userRole = userProfile?.role;
    if (userRole === 'student') {
      navigate('/dashboard/student/dissertations');
    } else if (userRole === 'teacher') {
      navigate('/dashboard/teacher/dissertations');
    } else {
      navigate('/dashboard');
    }
  };

  if (!documentId) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600 mb-4">Invalid dissertation ID</p>
          <Button onClick={handleGoBack}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" onClick={handleGoBack}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Dissertations
            </Button>
          </div>
          
          <div className="text-sm text-gray-600">
            {userProfile?.role === 'teacher' ? 'Supervisor View' : 'Student View'}
          </div>
        </div>
      </div>

      {/* Editor */}
      <div className="h-[calc(100vh-64px)]">
        <DissertationEditor />
      </div>
    </div>
  );
};

export default DissertationPage; 