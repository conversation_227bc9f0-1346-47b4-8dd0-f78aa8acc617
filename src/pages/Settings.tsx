import React from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '@/contexts/LanguageContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Separator } from '@/components/ui/separator';

const Settings: React.FC = () => {
  const { t } = useTranslation();
  const { language, setLanguage } = useLanguage();

  const handleLanguageChange = (value: string) => {
    setLanguage(value as 'en' | 'fr' | 'ar');
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-8">{t('app.settings')}</h1>
      
      <div className="grid gap-8">
        <Card>
          <CardHeader>
            <CardTitle>{t('language.select')}</CardTitle>
            <CardDescription>
              {t('language.name')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <RadioGroup 
              defaultValue={language} 
              onValueChange={handleLanguageChange}
              className="grid gap-4"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="en" id="en" />
                <Label htmlFor="en">{t('language.english')}</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="fr" id="fr" />
                <Label htmlFor="fr">{t('language.french')}</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="ar" id="ar" />
                <Label htmlFor="ar">{t('language.arabic')}</Label>
              </div>
            </RadioGroup>
          </CardContent>
        </Card>

        <Separator />

        {/* Additional settings sections can be added here */}
      </div>
    </div>
  );
};

export default Settings; 