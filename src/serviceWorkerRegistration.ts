// This optional code is used to register a service worker.
// register() is not called by default.

// This lets the app load faster on subsequent visits in production, and gives
// it offline capabilities. However, it also means that developers (and users)
// will only see deployed updates on subsequent visits to a page, after all the
// existing tabs open on the page have been closed, since previously cached
// resources are updated in the background.

import { toast } from 'sonner';

const isLocalhost = Boolean(
  window.location.hostname === 'localhost' ||
    // [::1] is the IPv6 localhost address.
    window.location.hostname === '[::1]' ||
    // *********/8 are considered localhost for IPv4.
    window.location.hostname.match(/^127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}$/)
);

type Config = {
  onSuccess?: (registration: ServiceWorkerRegistration) => void;
  onUpdate?: (registration: ServiceWorkerRegistration) => void;
};

// Store the registration globally so we can access it from the app
let swRegistration: ServiceWorkerRegistration | null = null;

export function getRegistration(): ServiceWorkerRegistration | null {
  return swRegistration;
}

export function register(config?: Config): void {
  console.log('Service Worker Registration: Starting registration process');
  console.log('Environment:', import.meta.env.PROD ? 'Production' : 'Development');
  console.log('Service Worker Support:', 'serviceWorker' in navigator ? 'Yes' : 'No');

  if (import.meta.env.PROD && 'serviceWorker' in navigator) {
    // The URL constructor is available in all browsers that support SW.
    const publicUrl = new URL(import.meta.env.BASE_URL, window.location.href);
    console.log('Public URL:', publicUrl.toString());
    console.log('Window Location:', window.location.href);
    
    if (publicUrl.origin !== window.location.origin) {
      // Our service worker won't work if BASE_URL is on a different origin
      // from what our page is served on. This might happen if a CDN is used to
      // serve assets; see https://github.com/facebook/create-react-app/issues/2374
      console.error('Service Worker Registration: Origins do not match', {
        publicUrlOrigin: publicUrl.origin,
        windowLocationOrigin: window.location.origin
      });
      return;
    }

    window.addEventListener('load', () => {
      const swUrl = `${import.meta.env.BASE_URL}service-worker.js`;
      console.log('Service Worker URL:', swUrl);

      if (isLocalhost) {
        // This is running on localhost. Let's check if a service worker still exists or not.
        console.log('Service Worker Registration: Running on localhost');
        checkValidServiceWorker(swUrl, config);

        // Add some additional logging to localhost, pointing developers to the
        // service worker/PWA documentation.
        navigator.serviceWorker.ready.then(() => {
          console.log(
            'This web app is being served cache-first by a service ' +
              'worker. To learn more, visit https://cra.link/PWA'
          );
        });
      } else {
        // Is not localhost. Just register service worker
        console.log('Service Worker Registration: Not on localhost, registering service worker');
        registerValidSW(swUrl, config);
      }
    });

    // Add event listener for the beforeinstallprompt event
    window.addEventListener('beforeinstallprompt', (e) => {
      // Prevent the mini-infobar from appearing on mobile
      e.preventDefault();
      // Store the event so it can be triggered later
      const deferredPrompt = e;
      // Show a custom install button
      setTimeout(() => {
        toast.message(
          "Install App",
          {
            description: "Install this app on your device for offline use.",
            action: {
              label: "Install",
              onClick: () => {
                // Show the install prompt
                deferredPrompt.prompt();
                // Wait for the user to respond to the prompt
                deferredPrompt.userChoice.then((choiceResult) => {
                  if (choiceResult.outcome === 'accepted') {
                    console.log('User accepted the install prompt');
                    toast.success('App installed successfully!');
                  } else {
                    console.log('User dismissed the install prompt');
                  }
                });
              }
            },
            duration: 10000
          }
        );
      }, 3000);
    });
  } else {
    console.log('Service Worker Registration: Conditions not met for registration', {
      isProd: import.meta.env.PROD,
      hasServiceWorker: 'serviceWorker' in navigator
    });
  }
}

function registerValidSW(swUrl: string, config?: Config): void {
  console.log('Service Worker Registration: Attempting to register', swUrl);
  
  navigator.serviceWorker
    .register(swUrl)
    .then((registration) => {
      console.log('Service Worker Registration: Successfully registered', registration);
      swRegistration = registration;
      
      registration.onupdatefound = () => {
        const installingWorker = registration.installing;
        if (installingWorker == null) {
          console.log('Service Worker Registration: No installing worker found');
          return;
        }
        
        console.log('Service Worker Registration: Installing worker found', installingWorker);
        
        installingWorker.onstatechange = () => {
          console.log('Service Worker State Change:', installingWorker.state);
          
          if (installingWorker.state === 'installed') {
            if (navigator.serviceWorker.controller) {
              // At this point, the updated precached content has been fetched,
              // but the previous service worker will still serve the older
              // content until all client tabs are closed.
              console.log(
                'New content is available and will be used when all ' +
                  'tabs for this page are closed. See https://cra.link/PWA.'
              );

              // Execute callback
              if (config && config.onUpdate) {
                config.onUpdate(registration);
              }
              
              // Show update notification
              toast.message(
                "New version available!",
                {
                  description: "A new version of the app is available.",
                  action: {
                    label: "Update",
                    onClick: () => {
                      if (registration.waiting) {
                        registration.waiting.postMessage({ type: 'SKIP_WAITING' });
                        window.location.reload();
                      }
                    }
                  },
                  duration: Infinity
                }
              );
            } else {
              // At this point, everything has been precached.
              // It's the perfect time to display a
              // "Content is cached for offline use." message.
              console.log('Content is cached for offline use.');
              
              toast.success('App is now available offline!');

              // Execute callback
              if (config && config.onSuccess) {
                config.onSuccess(registration);
              }
            }
          }
        };
      };
    })
    .catch((error) => {
      console.error('Error during service worker registration:', error);
    });
}

function checkValidServiceWorker(swUrl: string, config?: Config): void {
  // Check if the service worker can be found. If it can't reload the page.
  fetch(swUrl, {
    headers: { 'Service-Worker': 'script' },
  })
    .then((response) => {
      // Ensure service worker exists, and that we really are getting a JS file.
      const contentType = response.headers.get('content-type');
      if (
        response.status === 404 ||
        (contentType != null && contentType.indexOf('javascript') === -1)
      ) {
        // No service worker found. Probably a different app. Reload the page.
        navigator.serviceWorker.ready.then((registration) => {
          registration.unregister().then(() => {
            window.location.reload();
          });
        });
      } else {
        // Service worker found. Proceed as normal.
        registerValidSW(swUrl, config);
      }
    })
    .catch(() => {
      console.log('No internet connection found. App is running in offline mode.');
    });
}

export function unregister(): void {
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.ready
      .then((registration) => {
        registration.unregister();
      })
      .catch((error) => {
        console.error(error.message);
      });
  }
} 