import { apiClient } from './api-client';

// Define folder paths
export const FOLDERS = {
  PROFILE_PHOTOS: 'profile_photos',
  MATERIALS: 'materials',
  SUBMISSIONS: 'submissions',
  STUDENTS: 'students',
};

// Interface for file metadata
export interface FileMetadata {
  id: string;
  name: string;
  contentType: string;
  downloadUrl: string;
  fullPath: string;
  size?: number;
  createdTime?: string;
  updatedTime?: string;
}

// Interface for upload response
export interface UploadResponse {
  success: boolean;
  message: string;
  file_path: string;
  file_name: string;
  file_size: number;
}

/**
 * File upload service to replace Firebase Storage
 */
export class FileUploadService {
  /**
   * Upload a file to the PHP backend
   */
  static async uploadFile(
    file: File,
    fileName: string,
    folderPath: string,
    contentType?: string
  ): Promise<FileMetadata> {
    try {
      // Upload file using the API client
      const response = await apiClient.uploadFile<UploadResponse>('/upload', file, {
        folder: folderPath,
        filename: fileName
      });

      // Convert response to FileMetadata format
      return {
        id: response.file_name,
        name: fileName,
        contentType: contentType || file.type,
        downloadUrl: response.file_path,
        fullPath: response.file_path,
        size: response.file_size,
        createdTime: new Date().toISOString(),
        updatedTime: new Date().toISOString()
      };
    } catch (error) {
      console.error(`Error uploading file ${fileName}:`, error);
      throw new Error(`Failed to upload file: ${fileName}`);
    }
  }

  /**
   * Upload a student photo
   */
  static async uploadStudentPhoto(
    studentId: string,
    file: File
  ): Promise<FileMetadata> {
    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `student_${studentId}_${Date.now()}.${fileExt}`;
      
      const response = await apiClient.uploadFile<UploadResponse>(`/students/${studentId}/upload-photo`, file);

      return {
        id: response.file_name,
        name: fileName,
        contentType: file.type,
        downloadUrl: response.file_path,
        fullPath: response.file_path,
        size: response.file_size,
        createdTime: new Date().toISOString(),
        updatedTime: new Date().toISOString()
      };
    } catch (error) {
      console.error(`Error uploading student photo:`, error);
      throw new Error(`Failed to upload student photo`);
    }
  }

  /**
   * Delete a file (placeholder - implement if backend supports it)
   */
  static async deleteFile(filePath: string): Promise<void> {
    try {
      // Note: This would need to be implemented in the PHP backend
      console.warn('File deletion not implemented in PHP backend yet');
      // await apiClient.delete(`/files?path=${encodeURIComponent(filePath)}`);
    } catch (error) {
      console.error(`Error deleting file ${filePath}:`, error);
      throw new Error(`Failed to delete file: ${filePath}`);
    }
  }

  /**
   * Get file metadata (placeholder - implement if backend supports it)
   */
  static async getFileMetadata(filePath: string): Promise<FileMetadata> {
    try {
      // Note: This would need to be implemented in the PHP backend
      throw new Error('File metadata retrieval not implemented in PHP backend yet');
    } catch (error) {
      console.error(`Error getting file metadata for ${filePath}:`, error);
      throw new Error(`Failed to get file metadata: ${filePath}`);
    }
  }

  /**
   * List files in a folder (placeholder - implement if backend supports it)
   */
  static async listFiles(folderPath: string): Promise<FileMetadata[]> {
    try {
      // Note: This would need to be implemented in the PHP backend
      throw new Error('File listing not implemented in PHP backend yet');
    } catch (error) {
      console.error(`Error listing files in folder ${folderPath}:`, error);
      throw new Error(`Failed to list files in folder: ${folderPath}`);
    }
  }

  /**
   * Get download URL for a file (in our case, it's just the file path)
   */
  static async getFileUrl(filePath: string): Promise<string> {
    try {
      // For PHP backend, the file path is already the download URL
      return filePath;
    } catch (error) {
      console.error(`Error getting download URL for ${filePath}:`, error);
      throw new Error(`Failed to get download URL: ${filePath}`);
    }
  }

  /**
   * Generate storage path
   */
  static getStoragePath(folderPath: string, fileName?: string): string {
    if (fileName) {
      return `${folderPath}/${fileName}`;
    }
    return folderPath;
  }
}

// Export individual functions for compatibility with existing Firebase Storage usage
export const uploadFile = FileUploadService.uploadFile;
export const deleteFile = FileUploadService.deleteFile;
export const getFileMetadata = FileUploadService.getFileMetadata;
export const listFiles = FileUploadService.listFiles;
export const getFileUrl = FileUploadService.getFileUrl;
export const getStoragePath = FileUploadService.getStoragePath;

// Export default
export default FileUploadService;
