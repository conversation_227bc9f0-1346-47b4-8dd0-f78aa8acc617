import type { Teacher } from '@/types/user';
import { apiClient } from '@/lib/api-client';
import { createUser, updateUserRole } from './users';

// Extended ProfileDocument for teachers
interface TeacherProfileDocument extends ProfileDocument {
  assigned_courses?: string[];
  assigned_levels?: string[];
  specialization?: string;
  bio?: string;
  contact_number?: string;
}

interface GetTeachersOptions {
  id?: string;
  sortBy?: 'name' | 'email' | 'created_at';
  sortDirection?: 'asc' | 'desc';
  limit?: number;
  searchTerm?: string;
  courseId?: string;
  levelId?: string;
}

/**
 * Fetches teachers from Firebase with various filter and sort options
 * @param options Optional parameters to filter and sort teachers
 * @returns Promise with an array of Teacher objects
 */
export const getTeachers = async (options: GetTeachersOptions = {}) => {
  try {
    console.log('Fetching teachers from Firebase with options:', options);
    
    // If ID is provided, fetch single teacher
    if (options.id) {
      console.log(`Fetching single teacher with ID: ${options.id}`);
      
      try {
        const profile = await getById<TeacherProfileDocument>('profiles', options.id);
        
        if (!profile) {
          console.log(`No teacher found with ID: ${options.id}`);
          return [];
        }
        
        if (profile.role !== 'teacher') {
          console.log(`Profile with ID: ${options.id} is not a teacher`);
          return [];
        }
        
        return [{
          id: profile.id,
          name: profile.displayName,
          email: profile.email,
          role: 'teacher' as const,
          assigned_courses: profile.assigned_courses || [],
          assigned_levels: profile.assigned_levels || [],
          specialization: profile.specialization || '',
          bio: profile.bio || '',
          contact_number: profile.contact_number || '',
          created_at: profile.created_at
        }];
      } catch (error) {
        console.error(`Error fetching teacher with ID ${options.id}:`, error);
        return [];
      }
    }
    
    try {
      // First try with the full query including sorting
      // Create query constraints
      const constraints: QueryConstraint[] = [
        whereEqual('role', 'teacher')
      ];
      
      // Add sorting if specified
      if (options.sortBy) {
        const sortField = options.sortBy === 'name' ? 'displayName' : options.sortBy;
        constraints.push(orderByField(sortField, options.sortDirection || 'asc'));
      }
      
      // Add limit if specified
      if (options.limit && options.limit > 0) {
        constraints.push(limitTo(options.limit));
      }
      
      // Get teachers with constraints
      console.log('Fetching teachers with constraints:', constraints);
      let profiles = await getAll<TeacherProfileDocument>('profiles', constraints);
      
      // Process and return the results
      return processTeacherProfiles(profiles, options);
    } catch (error: any) {
      // If we get an index error, fall back to a simpler query
      if (error.message && error.message.includes('requires an index')) {
        console.log('Index error detected, falling back to simpler query');
        
        // Just query for teachers without sorting
        const constraints: QueryConstraint[] = [
          whereEqual('role', 'teacher')
        ];
        
        let profiles = await getAll<TeacherProfileDocument>('profiles', constraints);
        
        // Do client-side sorting instead
        if (options.sortBy) {
          const sortField = options.sortBy === 'name' ? 'displayName' : options.sortBy;
          const direction = options.sortDirection || 'asc';
          
          profiles.sort((a, b) => {
            const valueA = a[sortField as keyof TeacherProfileDocument] || '';
            const valueB = b[sortField as keyof TeacherProfileDocument] || '';
            
            if (typeof valueA === 'string' && typeof valueB === 'string') {
              return direction === 'asc' 
                ? valueA.localeCompare(valueB) 
                : valueB.localeCompare(valueA);
            }
            
            return 0;
          });
        }
        
        // Apply limit if needed
        if (options.limit && options.limit > 0 && profiles.length > options.limit) {
          profiles = profiles.slice(0, options.limit);
        }
        
        return processTeacherProfiles(profiles, options);
      }
      
      // For other errors, just log and return empty array
      console.error('Failed to fetch teachers from Firebase:', error);
      return [];
    }
  } catch (error) {
    console.error('Failed to fetch teachers from Firebase:', error);
    // Return empty array in case of failure
    return [];
  }
};

// Helper function to process teacher profiles
function processTeacherProfiles(profiles: TeacherProfileDocument[], options: GetTeachersOptions = {}) {
  // Filter by search term if provided (client-side filtering)
  if (options.searchTerm && options.searchTerm.trim() !== '') {
    const searchTerm = options.searchTerm.toLowerCase();
    profiles = profiles.filter(profile => 
      profile.displayName?.toLowerCase().includes(searchTerm) ||
      profile.email?.toLowerCase().includes(searchTerm) ||
      profile.specialization?.toLowerCase().includes(searchTerm)
    );
    console.log(`Filtered results by search term: ${options.searchTerm}`);
  }
  
  // Filter by course if provided
  if (options.courseId) {
    profiles = profiles.filter(profile => 
      profile.assigned_courses?.includes(options.courseId!)
    );
    console.log(`Filtered results by course ID: ${options.courseId}`);
  }
  
  // Filter by level if provided
  if (options.levelId) {
    profiles = profiles.filter(profile => 
      profile.assigned_levels?.includes(options.levelId!)
    );
    console.log(`Filtered results by level ID: ${options.levelId}`);
  }
  
  console.log(`Fetched ${profiles?.length || 0} teachers from Firebase`);
  
  // Map Firebase profiles to our Teacher type
  return profiles.map(profile => ({
    id: profile.id,
    name: profile.displayName,
    email: profile.email,
    role: 'teacher' as const,
    assigned_courses: profile.assigned_courses || [],
    assigned_levels: profile.assigned_levels || [],
    specialization: profile.specialization || '',
    bio: profile.bio || '',
    contact_number: profile.contact_number || '',
    created_at: profile.created_at
  }));
}

// Get a single teacher by ID - convenient helper function
export const getTeacherById = async (id: string): Promise<Teacher | null> => {
  try {
    const teachers = await getTeachers({ id });
    return teachers.length > 0 ? teachers[0] : null;
  } catch (error) {
    console.error(`Error fetching teacher with ID ${id}:`, error);
    return null;
  }
};

// Get all teachers that are assigned to a specific course
export const getTeachersByCourse = async (courseId: string): Promise<Teacher[]> => {
  try {
    return await getTeachers({ courseId });
  } catch (error) {
    console.error(`Error fetching teachers for course ${courseId}:`, error);
    return [];
  }
};

// Get all teachers that are assigned to a specific level
export const getTeachersByLevel = async (levelId: string): Promise<Teacher[]> => {
  try {
    return await getTeachers({ levelId });
  } catch (error) {
    console.error(`Error fetching teachers for level ${levelId}:`, error);
    return [];
  }
};

export const createTeacher = async (teacherData: Omit<Teacher, 'id' | 'created_at' | 'updated_at'>) => {
  try {
    console.log('Creating teacher with data:', teacherData);
    
    // First create the user with the teacher role
    const user = await createUser({
      name: teacherData.name,
      email: teacherData.email,
      password: teacherData.password!,
      role: 'teacher'
    });
    
    console.log('User created successfully:', user);
    
    if (!user || !user.id) {
      throw new Error('Failed to create user: No user ID returned');
    }
    
    // Then update the profile with teacher-specific fields
    const teacherFields = {
      assigned_courses: teacherData.assigned_courses || [],
      assigned_levels: teacherData.assigned_levels || [],
      specialization: teacherData.specialization || '',
      bio: teacherData.bio || '',
      contact_number: teacherData.contact_number || ''
    };
    
    console.log('Updating profile with teacher fields:', teacherFields);
    await update<Partial<TeacherProfileDocument>>('profiles', user.id, teacherFields);
    console.log('Teacher profile updated successfully');
    
    // Verify the teacher was created by fetching it back
    try {
      const createdTeacher = await getTeacherById(user.id);
      if (createdTeacher) {
        console.log('Verified teacher was created successfully:', createdTeacher);
        return createdTeacher;
      }
    } catch (verifyError) {
      console.error('Error verifying teacher creation:', verifyError);
      // Continue with the fallback return even if verification fails
    }
    
    // Return the complete teacher object as a fallback
    const teacher = {
      ...user,
      role: 'teacher' as const,
      assigned_courses: teacherData.assigned_courses || [],
      assigned_levels: teacherData.assigned_levels || [],
      specialization: teacherData.specialization || '',
      bio: teacherData.bio || '',
      contact_number: teacherData.contact_number || ''
    };
    
    console.log('Teacher created successfully, returning:', teacher);
    return teacher;
  } catch (error: any) {
    console.error('Error creating teacher:', error);
    
    // Handle Firebase auth errors
    if (error.code === 'auth/email-already-in-use' || 
        error.message?.includes('auth/email-already-in-use')) {
      throw new Error('This email address is already in use. Please use a different email.');
    }
    
    throw error;
  }
};

export const updateTeacher = async (teacherId: string, teacherData: Partial<Teacher>) => {
  try {
    // Update the teacher profile in Firebase
    await update<Partial<TeacherProfileDocument>>('profiles', teacherId, {
      displayName: teacherData.name,
      assigned_courses: teacherData.assigned_courses,
      assigned_levels: teacherData.assigned_levels,
      specialization: teacherData.specialization,
      bio: teacherData.bio,
      contact_number: teacherData.contact_number
    });
    
    return true;
  } catch (error) {
    console.error('Error updating teacher:', error);
    throw error;
  }
};

export const deleteTeacher = async (id: string) => {
  // Use the existing deleteUser function since the underlying logic is the same
  return await remove('profiles', id);
};

export const assignCourse = async (teacherId: string, courseId: string) => {
  try {
    console.log(`Assigning course ${courseId} to teacher ${teacherId}`);
    
    // Get the current teacher profile using getById instead of whereEqual
    const profile = await getById<TeacherProfileDocument>('profiles', teacherId);
    
    if (!profile) {
      console.error(`Teacher profile not found for ID: ${teacherId}`);
      throw new Error('Teacher not found');
    }
    
    // Initialize assigned_courses if it doesn't exist
    const assignedCourses = profile.assigned_courses || [];
    
    // Check if course is already assigned
    if (assignedCourses.includes(courseId)) {
      console.log(`Course ${courseId} already assigned to teacher ${teacherId}`);
      return true; // Already assigned, no need to update
    }
    
    // Add the course to the assigned courses
    const updatedCourses = [...assignedCourses, courseId];
    console.log(`Updating teacher ${teacherId} with courses:`, updatedCourses);
    
    // Update the profile
    await update<Partial<TeacherProfileDocument>>('profiles', teacherId, {
      assigned_courses: updatedCourses
    });
    
    console.log(`Successfully assigned course ${courseId} to teacher ${teacherId}`);
    return true;
  } catch (error) {
    console.error('Error assigning course to teacher:', error);
    throw error;
  }
};

export const unassignCourse = async (teacherId: string, courseId: string) => {
  try {
    console.log(`Unassigning course ${courseId} from teacher ${teacherId}`);
    
    // Get the current teacher profile using getById
    const profile = await getById<TeacherProfileDocument>('profiles', teacherId);
    
    if (!profile) {
      console.error(`Teacher profile not found for ID: ${teacherId}`);
      throw new Error('Teacher not found');
    }
    
    // Initialize assigned_courses if it doesn't exist
    const assignedCourses = profile.assigned_courses || [];
    
    // Remove the course from the assigned courses
    const updatedCourses = assignedCourses.filter(id => id !== courseId);
    console.log(`Updating teacher ${teacherId} with courses:`, updatedCourses);
    
    // Update the profile
    await update<Partial<TeacherProfileDocument>>('profiles', teacherId, {
      assigned_courses: updatedCourses
    });
    
    console.log(`Successfully unassigned course ${courseId} from teacher ${teacherId}`);
    return true;
  } catch (error) {
    console.error('Error unassigning course from teacher:', error);
    throw error;
  }
};

export const assignLevel = async (teacherId: string, levelId: string) => {
  try {
    console.log(`Assigning level ${levelId} to teacher ${teacherId}`);
    
    // Get the current teacher profile using getById
    const profile = await getById<TeacherProfileDocument>('profiles', teacherId);
    
    if (!profile) {
      console.error(`Teacher profile not found for ID: ${teacherId}`);
      throw new Error('Teacher not found');
    }
    
    // Initialize assigned_levels if it doesn't exist
    const assignedLevels = profile.assigned_levels || [];
    
    // Check if level is already assigned
    if (assignedLevels.includes(levelId)) {
      console.log(`Level ${levelId} already assigned to teacher ${teacherId}`);
      return true; // Already assigned, no need to update
    }
    
    // Add the level to the assigned levels
    const updatedLevels = [...assignedLevels, levelId];
    console.log(`Updating teacher ${teacherId} with levels:`, updatedLevels);
    
    // Update the profile
    await update<Partial<TeacherProfileDocument>>('profiles', teacherId, {
      assigned_levels: updatedLevels
    });
    
    console.log(`Successfully assigned level ${levelId} to teacher ${teacherId}`);
    return true;
  } catch (error) {
    console.error('Error assigning level to teacher:', error);
    throw error;
  }
};

export const unassignLevel = async (teacherId: string, levelId: string) => {
  try {
    console.log(`Unassigning level ${levelId} from teacher ${teacherId}`);
    
    // Get the current teacher profile using getById
    const profile = await getById<TeacherProfileDocument>('profiles', teacherId);
    
    if (!profile) {
      console.error(`Teacher profile not found for ID: ${teacherId}`);
      throw new Error('Teacher not found');
    }
    
    // Initialize assigned_levels if it doesn't exist
    const assignedLevels = profile.assigned_levels || [];
    
    // Remove the level from the assigned levels
    const updatedLevels = assignedLevels.filter(id => id !== levelId);
    console.log(`Updating teacher ${teacherId} with levels:`, updatedLevels);
    
    // Update the profile
    await update<Partial<TeacherProfileDocument>>('profiles', teacherId, {
      assigned_levels: updatedLevels
    });
    
    console.log(`Successfully unassigned level ${levelId} from teacher ${teacherId}`);
    return true;
  } catch (error) {
    console.error('Error unassigning level from teacher:', error);
    throw error;
  }
};

// Add a debugging function to directly check if a teacher exists
export const debugCheckTeacherExists = async (teacherId: string): Promise<boolean> => {
  try {
    console.log(`Debugging: Checking if teacher with ID ${teacherId} exists`);
    
    // First try to get the profile directly
    const profile = await getById<TeacherProfileDocument>('profiles', teacherId);
    
    if (profile) {
      console.log(`Found teacher profile:`, profile);
      return true;
    }
    
    // If not found directly, try querying by ID
    const profiles = await whereEqual<TeacherProfileDocument>('profiles', 'id', teacherId);
    
    if (profiles && profiles.length > 0) {
      console.log(`Found teacher profile through query:`, profiles[0]);
      return true;
    }
    
    // If still not found, check if any profile exists with this ID regardless of role
    const anyProfiles = await getById<ProfileDocument>('profiles', teacherId);
    
    if (anyProfiles) {
      console.log(`Found a profile with this ID but it's not a teacher:`, anyProfiles);
      return false;
    }
    
    console.log(`No profile found with ID ${teacherId}`);
    return false;
  } catch (error) {
    console.error(`Error checking if teacher exists:`, error);
    return false;
  }
};

// Add a debugging function to get all profiles regardless of role
export const debugGetAllProfiles = async (): Promise<ProfileDocument[]> => {
  try {
    console.log('Debugging: Getting all profiles from Firebase');
    
    try {
      // First try using the getAll helper
      const profiles = await getAll<ProfileDocument>('profiles');
      console.log(`Found ${profiles.length} total profiles using getAll`);
      return profiles;
    } catch (error: any) {
      console.error('Error using getAll for profiles:', error);
      
      // If we get an index error or any other error, fall back to direct Firestore query
      console.log('Falling back to direct Firestore query');
      
      try {
        // Use direct Firestore query as fallback
        const profilesRef = collection(db, 'profiles');
        const querySnapshot = await getDocs(query(profilesRef));
        
        const profiles: ProfileDocument[] = [];
        querySnapshot.forEach((doc) => {
          profiles.push({ id: doc.id, ...doc.data() } as ProfileDocument);
        });
        
        console.log(`Found ${profiles.length} total profiles using direct query`);
        return profiles;
      } catch (directError) {
        console.error('Error with direct Firestore query:', directError);
        throw directError; // Re-throw to be caught by outer catch
      }
    }
  } catch (error) {
    console.error('Error getting all profiles:', error);
    return [];
  }
};

// Add a new function to get a single profile by ID with direct Firestore access
export const debugGetProfileById = async (profileId: string): Promise<ProfileDocument | null> => {
  try {
    console.log(`Debugging: Getting profile with ID ${profileId} directly from Firestore`);
    
    // First try the standard method
    try {
      const profile = await getById<ProfileDocument>('profiles', profileId);
      if (profile) {
        console.log('Found profile using getById:', profile);
        return profile;
      }
    } catch (error) {
      console.error('Error using getById:', error);
    }
    
    // If that fails, try a direct Firestore query
    try {
      const profileDoc = await getDocs(query(
        collection(db, 'profiles'),
        whereEqual('id', profileId)
      ));
      
      if (!profileDoc.empty) {
        const profile = { id: profileDoc.docs[0].id, ...profileDoc.docs[0].data() } as ProfileDocument;
        console.log('Found profile using direct query:', profile);
        return profile;
      }
      
      console.log(`No profile found with ID ${profileId}`);
      return null;
    } catch (directError) {
      console.error('Error with direct Firestore query:', directError);
      return null;
    }
  } catch (error) {
    console.error(`Error getting profile with ID ${profileId}:`, error);
    return null;
  }
};

/**
 * Gets teachers without any sorting to avoid index requirements
 * This is a simpler version of getTeachers that doesn't require a composite index
 */
export const getTeachersWithoutSorting = async (): Promise<Teacher[]> => {
  try {
    console.log('Getting teachers without sorting (no index required)');
    
    // Just query for teachers without any sorting
    const constraints: QueryConstraint[] = [
      whereEqual('role', 'teacher')
    ];
    
    const profiles = await getAll<TeacherProfileDocument>('profiles', constraints);
    console.log(`Found ${profiles.length} teachers without sorting`);
    
    // Map Firebase profiles to our Teacher type
    return profiles.map(profile => ({
      id: profile.id,
      name: profile.displayName,
      email: profile.email,
      role: 'teacher' as const,
      assigned_courses: profile.assigned_courses || [],
      assigned_levels: profile.assigned_levels || [],
      specialization: profile.specialization || '',
      bio: profile.bio || '',
      contact_number: profile.contact_number || '',
      created_at: profile.created_at
    }));
  } catch (error) {
    console.error('Error getting teachers without sorting:', error);
    
    // If even this fails, try a direct Firestore query
    try {
      console.log('Falling back to direct Firestore query');
      const profilesRef = collection(db, 'profiles');
      const q = query(profilesRef, whereEqual('role', 'teacher'));
      const querySnapshot = await getDocs(q);
      
      const teachers: Teacher[] = [];
      querySnapshot.forEach((doc) => {
        const data = doc.data() as TeacherProfileDocument;
        teachers.push({
          id: doc.id,
          name: data.displayName,
          email: data.email,
          role: 'teacher',
          assigned_courses: data.assigned_courses || [],
          assigned_levels: data.assigned_levels || [],
          specialization: data.specialization || '',
          bio: data.bio || '',
          contact_number: data.contact_number || '',
          created_at: data.created_at
        });
      });
      
      console.log(`Found ${teachers.length} teachers using direct query`);
      return teachers;
    } catch (directError) {
      console.error('Error with direct Firestore query:', directError);
      return [];
    }
  }
}; 