import { apiClient } from '@/lib/api-client';
import { AuthService } from '@/lib/auth';
import { logActivity } from '../utils/activity-logger';
import { toast } from 'sonner';

export type AnnouncementFormData = {
  title: string;
  content: string;
  target_levels: string[];
  priority: string;
  expires_at: string;
  is_general?: boolean;
};

export const getAnnouncements = async () => {
  try {
    console.log('Fetching announcements from API');
    const announcements = await apiClient.get('/announcements');

    // Sort announcements by creation date (newest first)
    return announcements.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
  } catch (error) {
    console.error('Error fetching announcements:', error);
    throw error;
  }
};

export const getAnnouncementById = async (id: string) => {
  try {
    console.log(`Fetching announcement with ID: ${id}`);
    const announcement = await apiClient.get(`/announcements/${id}`);

    if (!announcement) {
      throw new Error('Announcement not found');
    }

    return announcement;
  } catch (error) {
    console.error('Error fetching announcement:', error);
    throw error;
  }
};

export const getAnnouncementsByLevel = async (levelId: string) => {
  try {
    console.log(`Fetching announcements for level: ${levelId}`);
    const announcements = await apiClient.get(`/announcements?level_id=${levelId}`);

    // Sort announcements by creation date (newest first)
    return announcements.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
  } catch (error) {
    console.error('Error fetching announcements by level:', error);
    throw error;
  }
};

export const getActiveAnnouncements = async () => {
  try {
    console.log('Fetching active announcements');
    const announcements = await apiClient.get('/announcements?active=true');

    // Sort announcements by creation date (newest first)
    return announcements.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
  } catch (error) {
    console.error('Error fetching active announcements:', error);
    throw error;
  }
};

export const createAnnouncement = async (announcementData: AnnouncementFormData) => {
  try {
    console.log('Creating new announcement:', announcementData);

    const user = await AuthService.getCurrentUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    // If it's a general announcement, we don't need target levels
    const finalData = {
      ...announcementData,
      // If it's a general announcement and target_levels is empty, set it to an empty array
      target_levels: announcementData.is_general ? [] : announcementData.target_levels,
      created_by: user.id,
      created_at: new Date().toISOString(),
      author: user.name || 'Teacher'
    };

    const result = await apiClient.post('/announcements', finalData);

    await logActivity('announcement_created', {
      announcementId: result.id,
      title: announcementData.title,
      targetLevels: announcementData.target_levels,
      isGeneral: announcementData.is_general
    });

    toast.success('Announcement created successfully');

    return result;
  } catch (error: any) {
    console.error('Error creating announcement:', error);
    toast.error(error.message || 'Failed to create announcement');
    throw error;
  }
};

export const updateAnnouncement = async (id: string, announcementData: Partial<AnnouncementFormData>) => {
  try {
    console.log('Updating announcement:', id, announcementData);

    const user = await AuthService.getCurrentUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    // If it's a general announcement, we don't need target levels
    const finalData = {
      ...announcementData,
      // If it's being updated to a general announcement, clear target_levels
      ...(announcementData.is_general ? { target_levels: [] } : {}),
      updated_by: user.id,
      updated_at: new Date().toISOString()
    };

    const result = await apiClient.put(`/announcements/${id}`, finalData);

    await logActivity('announcement_updated', {
      announcementId: id,
      updates: announcementData,
      isGeneral: announcementData.is_general
    });

    toast.success('Announcement updated successfully');

    return result;
  } catch (error: any) {
    console.error('Error updating announcement:', error);
    toast.error(error.message || 'Failed to update announcement');
    throw error;
  }
};

export const deleteAnnouncement = async (id: string) => {
  try {
    console.log('Deleting announcement:', id);

    const user = await AuthService.getCurrentUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    await apiClient.delete(`/announcements/${id}`);

    await logActivity('announcement_deleted', {
      announcementId: id
    });

    toast.success('Announcement deleted successfully');

    return { success: true };
  } catch (error: any) {
    console.error('Error deleting announcement:', error);
    toast.error(error.message || 'Failed to delete announcement');
    throw error;
  }
};