import { apiClient } from '@/lib/api-client';
import { AuthService } from '@/lib/auth';

// Live class types - to be defined based on PHP backend structure
interface LiveClass {
  id: string;
  title: string;
  description?: string;
  date: string;
  time: string;
  status: string;
}

interface LiveClassInput {
  title: string;
  description?: string;
  date: string;
  time: string;
}

interface Comment {
  id: string;
  content: string;
  author: string;
  created_at: string;
}

interface CommentInput {
  content: string;
  live_class_id: string;
}

export const createLiveClass = async (data: Omit<LiveClassInput, 'teacherId'>): Promise<LiveClass> => {
  try {
    const user = auth.currentUser;
    
    if (!user) {
      throw new Error('User not authenticated');
    }
    
    const liveClass = await liveClassModel.createLiveClass({
      ...data,
      teacherId: user.uid
    });
    
    return liveClass;
  } catch (error) {
    console.error('Error creating live class:', error);
    throw error;
  }
};

export const updateLiveClass = async (id: string, data: Partial<LiveClassInput>): Promise<void> => {
  try {
    await liveClassModel.updateLiveClass(id, data);
  } catch (error) {
    console.error('Error updating live class:', error);
    throw error;
  }
};

export const deleteLiveClass = async (id: string): Promise<void> => {
  try {
    await liveClassModel.deleteLiveClass(id);
  } catch (error) {
    console.error('Error deleting live class:', error);
    throw error;
  }
};

export const getLiveClasses = async (options?: {
  teacherId?: string;
  courseId?: string;
  levelId?: string;
  active?: boolean;
}): Promise<LiveClass[]> => {
  try {
    return await liveClassModel.getLiveClasses(options);
  } catch (error) {
    console.error('Error fetching live classes:', error);
    throw error;
  }
};

export const getActiveLiveClasses = async (options?: {
  teacherId?: string;
  courseId?: string;
  levelId?: string;
}): Promise<LiveClass[]> => {
  try {
    return await liveClassModel.getLiveClasses({
      ...options,
      active: true
    });
  } catch (error) {
    console.error('Error fetching active live classes:', error);
    throw error;
  }
};

export const subscribeLiveClasses = (
  options: {
    teacherId?: string;
    courseId?: string;
    levelId?: string;
    active?: boolean;
  },
  callback: (liveClasses: LiveClass[]) => void
) => {
  return liveClassModel.subscribeLiveClasses(options, callback);
};

// Comment functions
export const createComment = async (
  liveClassId: string,
  content: string,
  userName: string,
  userRole: string
): Promise<Comment> => {
  try {
    const user = auth.currentUser;
    
    if (!user) {
      throw new Error('User not authenticated');
    }
    
    const commentData: CommentInput = {
      content,
      userId: user.uid,
      userName,
      userRole,
      liveClassId
    };
    
    return await commentModel.createComment(commentData);
  } catch (error) {
    console.error('Error creating comment:', error);
    throw error;
  }
};

export const updateComment = async (id: string, content: string): Promise<void> => {
  try {
    await commentModel.updateComment(id, content);
  } catch (error) {
    console.error('Error updating comment:', error);
    throw error;
  }
};

export const deleteComment = async (id: string): Promise<void> => {
  try {
    await commentModel.deleteComment(id);
  } catch (error) {
    console.error('Error deleting comment:', error);
    throw error;
  }
};

export const hardDeleteComment = async (id: string): Promise<void> => {
  try {
    await commentModel.hardDeleteComment(id);
  } catch (error) {
    console.error('Error hard deleting comment:', error);
    throw error;
  }
};

export const getComments = async (
  liveClassId: string,
  options: {
    limit?: number;
    includeDeleted?: boolean;
  } = {}
): Promise<Comment[]> => {
  try {
    return await commentModel.getComments(liveClassId, options);
  } catch (error) {
    console.error('Error fetching comments:', error);
    throw error;
  }
};

export const subscribeComments = (
  liveClassId: string,
  options: {
    limit?: number;
    includeDeleted?: boolean;
  } = {},
  callback: (comments: Comment[]) => void
) => {
  return commentModel.subscribeComments(liveClassId, options, callback);
}; 