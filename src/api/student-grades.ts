import { apiClient } from '@/lib/api-client';
import { AuthService } from '@/lib/auth';
import { toast } from 'sonner';

// Interface for a grade item (could be from an assignment or exam)
export interface GradeItem {
  id: string;
  title: string;
  type: 'assignment' | 'exam';
  date: string;
  max_points: number;
  grade: number;
  percentage: number;
  feedback?: string;
  status: string;
  course_name?: string;
}

// Get all grades for a student
export const getStudentGrades = async (studentId: string): Promise<GradeItem[]> => {
  try {
    console.log(`Fetching grades for student: ${studentId}`);
    
    // Get all graded submissions for this student
    const submissions = await submissionModel.getSubmissionsByStudentId(studentId);
    const gradedSubmissions = submissions.filter(sub => sub.status === 'graded');
    
    // Get all exam results for this student
    const examResults = await examResultModel.getExamResultsByStudentId(studentId);
    
    // Process assignment submissions
    const assignmentGrades = await Promise.all(
      gradedSubmissions.map(async (submission) => {
        // Get assignment details
        const assignment = await assignmentModel.getAssignmentById(submission.assignment_id);
        
        if (!assignment) {
          return null;
        }
        
        return {
          id: submission.id,
          title: assignment.title,
          type: 'assignment' as const,
          date: submission.graded_at || submission.submitted_at,
          max_points: assignment.points,
          grade: submission.grade || 0,
          percentage: ((submission.grade || 0) / assignment.points) * 100,
          feedback: submission.feedback,
          status: submission.status,
          course_name: assignment.course_name
        };
      })
    );
    
    // Process exam grades
    const examGrades = await Promise.all(
      examResults.map(async (result) => {
        // Get exam details
        const exam = await examModel.getExamById(result.exam_id);
        
        if (!exam) {
          return null;
        }
        
        return {
          id: result.id,
          title: exam.name,
          type: 'exam' as const,
          date: result.date_recorded || exam.date,
          max_points: 100, // Assuming max score is 100 for exams
          grade: result.marks || 0,
          percentage: result.marks || 0, // Assuming marks are already in percentage
          feedback: result.remarks,
          status: 'graded',
          course_name: exam.subject
        };
      })
    );
    
    // Combine and sort by date (newest first)
    const allGrades = [...assignmentGrades.filter(Boolean), ...examGrades.filter(Boolean)]
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
    
    return allGrades;
  } catch (error) {
    console.error('Error fetching student grades:', error);
    toast.error('Failed to load grades. Please try again.');
    throw error;
  }
};

// Get grade statistics for a student
export const getStudentGradeStats = async (studentId: string) => {
  try {
    const grades = await getStudentGrades(studentId);
    
    if (!grades.length) {
      return {
        average: 0,
        highest: 0,
        lowest: 0,
        totalItems: 0,
        assignmentAverage: 0,
        examAverage: 0,
        recentGrades: []
      };
    }
    
    // Calculate statistics
    const percentages = grades.map(grade => grade.percentage);
    const average = percentages.reduce((sum, val) => sum + val, 0) / percentages.length;
    
    const assignmentGrades = grades.filter(grade => grade.type === 'assignment');
    const examGrades = grades.filter(grade => grade.type === 'exam');
    
    const assignmentAverage = assignmentGrades.length 
      ? assignmentGrades.reduce((sum, grade) => sum + grade.percentage, 0) / assignmentGrades.length 
      : 0;
      
    const examAverage = examGrades.length 
      ? examGrades.reduce((sum, grade) => sum + grade.percentage, 0) / examGrades.length 
      : 0;
    
    return {
      average: Math.round(average * 10) / 10,
      highest: Math.round(Math.max(...percentages) * 100) / 100,
      lowest: Math.round(Math.min(...percentages) * 100) / 100,
      totalItems: grades.length,
      assignmentAverage: Math.round(assignmentAverage * 10) / 10,
      examAverage: Math.round(examAverage * 10) / 10,
      recentGrades: grades.slice(0, 5) // Get 5 most recent grades
    };
  } catch (error) {
    console.error('Error calculating grade statistics:', error);
    throw error;
  }
}; 