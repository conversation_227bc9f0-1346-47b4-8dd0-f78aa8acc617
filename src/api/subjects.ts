import { apiClient } from '@/lib/api-client';
import { toast } from 'sonner';

export const getSubjects = async () => {
  try {
    const subjects = await subjectModel.getAllSubjects();
    return subjects;
  } catch (error) {
    console.error('Error fetching subjects:', error);
    throw error;
  }
};

export const getSubjectsByCourse = async (courseId: string) => {
  try {
    const subjects = await subjectModel.getSubjectsByCourseId(courseId);
    return subjects;
  } catch (error) {
    console.error('Error fetching subjects by course:', error);
    throw error;
  }
};

export const getSubject = async (id: string) => {
  try {
    const subject = await subjectModel.getSubjectById(id);
    
    if (!subject) {
      throw new Error('Subject not found');
    }
    
    return subject;
  } catch (error) {
    console.error('Error fetching subject:', error);
    throw error;
  }
};

export const createSubject = async (data: Omit<subjectModel.Subject, 'id' | 'created_at' | 'updated_at'>) => {
  try {
    const subjectId = await subjectModel.createSubject(data);
    const newSubject = await subjectModel.getSubjectById(subjectId);
    
    toast.success('Subject created successfully');
    return newSubject;
  } catch (error: any) {
    console.error('Error creating subject:', error);
    toast.error(error.message || 'Failed to create subject');
    throw error;
  }
};

export const updateSubject = async (id: string, data: Partial<subjectModel.Subject>) => {
  try {
    await subjectModel.updateSubject(id, data);
    
    toast.success('Subject updated successfully');
    return true;
  } catch (error: any) {
    console.error('Error updating subject:', error);
    toast.error(error.message || 'Failed to update subject');
    throw error;
  }
};

export const deleteSubject = async (id: string) => {
  try {
    const subject = await subjectModel.getSubjectById(id);
    
    if (!subject) {
      toast.error('Subject not found');
      return false;
    }
    
    await subjectModel.deleteSubject(id);
    
    toast.success(`Successfully deleted subject ${subject.name} (${subject.code})`);
    return true;
  } catch (error: any) {
    console.error('Error deleting subject:', error);
    toast.error(error.message || 'Failed to delete subject');
    throw error;
  }
}; 