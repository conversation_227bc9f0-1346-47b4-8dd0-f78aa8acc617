import { apiClient } from '@/lib/api-client';
import { logActivity } from '../utils/activity-logger';
import { toast } from 'sonner';
import { uploadFile, deleteFile, FOLDERS } from '@/lib/file-upload';

// Material type definition
export interface Material {
  id: string;
  title: string;
  description: string;
  level_id: string;
  type: string;
  file_url?: string;
  file_name?: string;
  file_size?: number;
  created_at?: string;
  updated_at?: string;
}

export type MaterialFormData = {
  title: string;
  description: string;
  level_id: string;
  type: string;
  file?: File;
};

export const getMaterials = async () => {
  try {
    return await apiClient.get<Material[]>('/materials');
  } catch (error: any) {
    console.error('Error fetching materials:', error);
    toast.error(error.message || 'Failed to fetch materials');
    throw error;
  }
};

export const getMaterialsByLevel = async (levelId: string) => {
  try {
    return await apiClient.get<Material[]>(`/materials?level_id=${levelId}`);
  } catch (error: any) {
    console.error('Error fetching materials by level:', error);
    toast.error(error.message || 'Failed to fetch materials');
    throw error;
  }
};

export const getMaterialById = async (id: string) => {
  try {
    return await apiClient.get<Material>(`/materials/${id}`);
  } catch (error: any) {
    console.error('Error fetching material:', error);
    toast.error(error.message || 'Failed to fetch material');
    throw error;
  }
};

export const getMaterialsByType = async (type: string) => {
  try {
    console.log(`Fetching materials of type: ${type}`);
    const materials = await apiClient.get<Material[]>(`/materials?type=${type}`);

    // Sort materials by creation date (newest first)
    return materials.sort((a, b) => new Date(b.created_at || '').getTime() - new Date(a.created_at || '').getTime());
  } catch (error) {
    console.error('Error fetching materials by type:', error);
    throw error;
  }
};

export const createMaterial = async (materialData: MaterialFormData) => {
  try {
    console.log('Creating new material:', materialData);

    let fileUrl = '';
    let fileName = '';
    let fileSize = 0;

    // Upload file if provided
    if (materialData.file) {
      const file = materialData.file;
      fileName = `${Date.now()}_${file.name}`;
      fileSize = file.size;

      // Upload file to PHP backend
      const folderPath = FOLDERS.MATERIALS;
      const uploadedFile = await uploadFile(file, fileName, folderPath, file.type);

      // Get the file URL
      fileUrl = uploadedFile.downloadUrl;
    }

    // Create the material record via API
    const newMaterial = await apiClient.post<Material>('/materials', {
      title: materialData.title,
      description: materialData.description,
      level_id: materialData.level_id,
      type: materialData.type,
      file_url: fileUrl,
      file_name: fileName,
      file_size: fileSize
    });

    await logActivity('material_created', {
      materialId: newMaterial.id,
      title: materialData.title,
      levelId: materialData.level_id,
      type: materialData.type
    });

    toast.success('Material uploaded successfully');
    return newMaterial;
  } catch (error: any) {
    console.error('Error creating material:', error);
    toast.error(error.message || 'Failed to upload material');
    throw error;
  }
};

export const updateMaterial = async (id: string, materialData: Partial<MaterialFormData>) => {
  try {
    console.log('Updating material:', id, materialData);

    // Get the existing material
    const existingMaterial = await getMaterialById(id);

    let fileUrl = existingMaterial.file_url || '';
    let fileName = existingMaterial.file_name || '';
    let fileSize = existingMaterial.file_size || 0;

    // Upload new file if provided
    if (materialData.file) {
      const file = materialData.file;
      fileName = `${Date.now()}_${file.name}`;
      fileSize = file.size;

      // Upload new file to PHP backend
      const folderPath = FOLDERS.MATERIALS;
      const uploadedFile = await uploadFile(file, fileName, folderPath, file.type);

      // Get the file URL
      fileUrl = uploadedFile.downloadUrl;
    }

    // Update the material record via API
    const updateData: any = {
      ...materialData,
      file_url: fileUrl,
      file_name: fileName,
      file_size: fileSize
    };

    // Remove the file property as it's not stored in database
    delete updateData.file;

    await apiClient.put(`/materials/${id}`, updateData);

    await logActivity('material_updated', {
      materialId: id,
      title: materialData.title || existingMaterial.title
    });

    toast.success('Material updated successfully');

    return {
      id,
      ...existingMaterial,
      ...updateData,
    };
  } catch (error: any) {
    console.error('Error updating material:', error);
    toast.error(error.message || 'Failed to update material');
    throw error;
  }
};

export const deleteMaterial = async (id: string) => {
  try {
    console.log('Deleting material:', id);

    // Get the material to delete
    const material = await getMaterialById(id);

    // Delete the material record via API
    await apiClient.delete(`/materials/${id}`);

    await logActivity('material_deleted', {
      materialId: id,
      title: material.title
    });

    toast.success('Material deleted successfully');

    return { success: true };
  } catch (error: any) {
    console.error('Error deleting material:', error);
    toast.error(error.message || 'Failed to delete material');
    throw error;
  }
};