import { apiClient } from '@/lib/api-client';
import type { User } from '@/types/user';
import { AuthService } from '@/lib/auth';

export const getUsers = async (): Promise<User[]> => {
  try {
    console.log('Fetching users from API');
    const users = await apiClient.get<User[]>('/users');
    console.log(`Fetched ${users.length} users from API`);
    return users;
  } catch (error) {
    console.error('Failed to fetch users from API:', error);
    return [];
  }
};

export const createUser = async ({
  name,
  email,
  password,
  role = 'student'
}: {
  name: string;
  email: string;
  password: string;
  role?: 'admin' | 'teacher' | 'student' | 'parent';
}): Promise<User> => {
  try {
    console.log(`Creating user via API: ${name} (${email}) with role ${role}`);

    // Create the user via API
    const response = await AuthService.signUp(name, email, password, role);

    console.log(`Created user with ID: ${response.user.id}`);

    // Return the user object
    return response.user;
  } catch (error: any) {
    console.error('Error creating user via API:', error);

    // Handle specific auth errors
    if (error.message?.includes('email-already-in-use') || error.message?.includes('already exists')) {
      throw new Error('auth/email-already-in-use');
    }

    throw error;
  }
};

export const updateUserRole = async (userId: string, role: 'admin' | 'teacher' | 'student' | 'parent'): Promise<boolean> => {
  try {
    await apiClient.put(`/users/${userId}`, { role });
    return true;
  } catch (error) {
    console.error('Error updating user role via API:', error);
    throw error;
  }
};

export const deleteUser = async (id: string): Promise<boolean> => {
  try {
    await apiClient.delete(`/users/${id}`);
    return true;
  } catch (error) {
    console.error('Error deleting user via API:', error);
    throw error;
  }
};
