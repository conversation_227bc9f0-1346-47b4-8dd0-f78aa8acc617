import { apiClient } from '@/lib/api-client';
import { getStudents } from '@/api/students';
import { AuthService } from '@/lib/auth';
import { logActivity } from '../utils/activity-logger';
import { toast } from 'sonner';

export type AssignmentFormData = {
  title: string;
  description: string;
  level_id: string;
  due_date: string;
  points: number;
  status?: string;
};

export const getAssignments = async () => {
  try {
    console.log('Fetching assignments from Firebase');
    const assignments = await assignmentModel.getAllAssignments();
    
    // Sort assignments by due date (closest first)
    return assignments.sort((a, b) => new Date(a.due_date).getTime() - new Date(b.due_date).getTime());
  } catch (error) {
    console.error('Error fetching assignments:', error);
    throw error;
  }
};

export const getAssignmentById = async (id: string) => {
  try {
    console.log(`Fetching assignment with ID: ${id}`);
    const assignment = await assignmentModel.getAssignmentById(id);
    
    if (!assignment) {
      throw new Error('Assignment not found');
    }
    
    return assignment;
  } catch (error) {
    console.error('Error fetching assignment:', error);
    throw error;
  }
};

export const getAssignmentsByLevel = async (levelId: string) => {
  try {
    console.log(`Fetching assignments for level: ${levelId}`);
    const assignments = await assignmentModel.getAssignmentsByLevelId(levelId);
    
    // Sort assignments by due date (closest first)
    return assignments.sort((a, b) => new Date(a.due_date).getTime() - new Date(b.due_date).getTime());
  } catch (error) {
    console.error('Error fetching assignments by level:', error);
    throw error;
  }
};

export const createAssignment = async (assignmentData: AssignmentFormData) => {
  try {
    console.log('Creating new assignment:', assignmentData);
    
    const user = auth.currentUser;
    if (!user) {
      throw new Error('User not authenticated');
    }
    
    // Set default status if not provided
    const status = assignmentData.status || 'active';
    
    const assignmentId = await assignmentModel.createAssignment({
      ...assignmentData,
      status,
      created_by: user.uid,
      created_at: new Date().toISOString()
    });
    
    await logActivity('assignment_created', { 
      assignmentId,
      title: assignmentData.title,
      levelId: assignmentData.level_id
    });
    
    toast.success('Assignment created successfully');
    
    return {
      id: assignmentId,
      ...assignmentData,
      status,
      created_by: user.uid,
      created_at: new Date().toISOString()
    };
  } catch (error: any) {
    console.error('Error creating assignment:', error);
    toast.error(error.message || 'Failed to create assignment');
    throw error;
  }
};

export const updateAssignment = async (id: string, assignmentData: Partial<AssignmentFormData>) => {
  try {
    console.log('Updating assignment:', id, assignmentData);
    
    const user = auth.currentUser;
    if (!user) {
      throw new Error('User not authenticated');
    }
    
    await assignmentModel.updateAssignment(id, {
      ...assignmentData,
      updated_by: user.uid,
      updated_at: new Date().toISOString()
    });
    
    await logActivity('assignment_updated', {
      assignmentId: id,
      updates: assignmentData
    });
    
    toast.success('Assignment updated successfully');
    
    return {
      id,
      ...assignmentData
    };
  } catch (error: any) {
    console.error('Error updating assignment:', error);
    toast.error(error.message || 'Failed to update assignment');
    throw error;
  }
};

export const deleteAssignment = async (id: string) => {
  try {
    console.log('Deleting assignment:', id);
    
    const user = auth.currentUser;
    if (!user) {
      throw new Error('User not authenticated');
    }
    
    await assignmentModel.deleteAssignment(id);
    
    await logActivity('assignment_deleted', {
      assignmentId: id
    });
    
    toast.success('Assignment deleted successfully');
    
    return { success: true };
  } catch (error: any) {
    console.error('Error deleting assignment:', error);
    toast.error(error.message || 'Failed to delete assignment');
    throw error;
  }
};

export const getStudentAssignments = async (studentId: string) => {
  try {
    console.log(`Fetching assignments for student: ${studentId}`);
    
    // Get student details to find their level
    const student = await studentModel.getStudentById(studentId);
    
    if (!student || !student.level_id) {
      throw new Error('Student not found or not assigned to a level');
    }
    
    // Get assignments for the student's level
    const assignments = await assignmentModel.getAssignmentsByLevelId(student.level_id);
    
    // Sort assignments by due date (closest first)
    return assignments.sort((a, b) => new Date(a.due_date).getTime() - new Date(b.due_date).getTime());
  } catch (error) {
    console.error('Error fetching student assignments:', error);
    throw error;
  }
};

// Get all submissions for a specific assignment with student details
export const getAssignmentSubmissions = async (assignmentId: string) => {
  try {
    console.log(`Fetching submissions for assignment: ${assignmentId}`);
    
    // Get all submissions for this assignment
    const submissions = await submissionModel.getSubmissionsByAssignmentId(assignmentId);
    
    if (!submissions.length) {
      return [];
    }
    
    // Get student details for each submission
    const submissionsWithStudentDetails = await Promise.all(
      submissions.map(async (submission) => {
        const student = await studentModel.getStudentById(submission.student_id);
        return {
          ...submission,
          student: student || { name: 'Unknown Student', email: 'N/A' }
        };
      })
    );
    
    // Sort submissions by submitted date (newest first)
    return submissionsWithStudentDetails.sort((a, b) => 
      new Date(b.submitted_at).getTime() - new Date(a.submitted_at).getTime()
    );
  } catch (error) {
    console.error('Error fetching assignment submissions:', error);
    throw error;
  }
};

// Grade a submission
export const gradeSubmission = async (submissionId: string, grade: number, feedback?: string) => {
  try {
    console.log(`Grading submission: ${submissionId} with grade: ${grade}`);
    
    const user = auth.currentUser;
    if (!user) {
      throw new Error('User not authenticated');
    }
    
    await submissionModel.updateSubmission(submissionId, {
      grade,
      feedback,
      status: 'graded',
      graded_at: new Date().toISOString(),
      graded_by: user.uid
    });
    
    await logActivity('submission_graded', {
      submissionId,
      grade,
      gradedBy: user.uid
    });
    
    toast.success('Submission graded successfully');
    
    return { success: true };
  } catch (error: any) {
    console.error('Error grading submission:', error);
    toast.error(error.message || 'Failed to grade submission');
    throw error;
  }
}; 