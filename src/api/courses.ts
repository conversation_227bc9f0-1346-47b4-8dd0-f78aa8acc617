import { apiClient } from '@/lib/api-client';
import { toast } from 'sonner';

// Course type definition
export interface Course {
  id: string;
  name: string;
  code: string;
  description?: string;
  duration?: string;
  fee?: number;
  created_at?: string;
  updated_at?: string;
}

export const getCourses = async () => {
  try {
    const courses = await apiClient.get<Course[]>('/courses');
    return courses;
  } catch (error) {
    console.error('Error fetching courses:', error);
    throw error;
  }
};

export const getCourse = async (id: string) => {
  try {
    const course = await apiClient.get<Course>(`/courses/${id}`);
    return course;
  } catch (error) {
    console.error('Error fetching course:', error);
    throw error;
  }
};

export const createCourse = async (data: Omit<Course, 'id' | 'created_at' | 'updated_at'>) => {
  try {
    const newCourse = await apiClient.post<Course>('/courses', data);
    toast.success('Course created successfully');
    return newCourse;
  } catch (error: any) {
    console.error('Error creating course:', error);
    toast.error(error.message || 'Failed to create course');
    throw error;
  }
};

export const updateCourse = async (id: string, data: Partial<Course>) => {
  try {
    await apiClient.put(`/courses/${id}`, data);
    toast.success('Course updated successfully');
    return true;
  } catch (error: any) {
    console.error('Error updating course:', error);
    toast.error(error.message || 'Failed to update course');
    throw error;
  }
};

export const deleteCourse = async (id: string) => {
  try {
    const course = await getCourse(id);
    await apiClient.delete(`/courses/${id}`);
    toast.success(`Successfully deleted course ${course.name} (${course.code})`);
    return true;
  } catch (error: any) {
    console.error('Error deleting course:', error);
    toast.error(error.message || 'Failed to delete course');
    throw error;
  }
};
