export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      _typescript_config: {
        Row: {
          id: number
          updated_at: string | null
        }
        Insert: {
          id?: number
          updated_at?: string | null
        }
        Update: {
          id?: number
          updated_at?: string | null
        }
        Relationships: []
      }
      academic_records: {
        Row: {
          created_at: string
          grade: string
          id: string
          notes: string | null
          status: string
          student_id: string
          subject: string
          term: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          grade: string
          id?: string
          notes?: string | null
          status?: string
          student_id: string
          subject: string
          term: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          grade?: string
          id?: string
          notes?: string | null
          status?: string
          student_id?: string
          subject?: string
          term?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "academic_records_student_id_fkey"
            columns: ["student_id"]
            isOneToOne: false
            referencedRelation: "students"
            referencedColumns: ["id"]
          },
        ]
      }
      activity_logs: {
        Row: {
          activity_type: string
          created_at: string
          description: string
          id: string
          ip_address: string | null
          metadata: Json | null
          user_agent: string | null
          user_id: string | null
        }
        Insert: {
          activity_type: string
          created_at?: string
          description: string
          id?: string
          ip_address?: string | null
          metadata?: Json | null
          user_agent?: string | null
          user_id?: string | null
        }
        Update: {
          activity_type?: string
          created_at?: string
          description?: string
          id?: string
          ip_address?: string | null
          metadata?: Json | null
          user_agent?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "activity_logs_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      attendance_records: {
        Row: {
          created_at: string
          date: string
          id: string
          marked_by: string | null
          notes: string | null
          semester: string | null
          status: string | null
          student_id: string | null
          time_in: string | null
          time_out: string | null
          total_days: number | null
          updated_at: string
        }
        Insert: {
          created_at?: string
          date: string
          id?: string
          marked_by?: string | null
          notes?: string | null
          semester?: string | null
          status?: string | null
          student_id?: string | null
          time_in?: string | null
          time_out?: string | null
          total_days?: number | null
          updated_at?: string
        }
        Update: {
          created_at?: string
          date?: string
          id?: string
          marked_by?: string | null
          notes?: string | null
          semester?: string | null
          status?: string | null
          student_id?: string | null
          time_in?: string | null
          time_out?: string | null
          total_days?: number | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "attendance_records_student_id_fkey"
            columns: ["student_id"]
            isOneToOne: false
            referencedRelation: "students"
            referencedColumns: ["id"]
          },
        ]
      }
      course_notifications: {
        Row: {
          course_id: string | null
          created_at: string
          id: string
          message: string
          notification_type: string | null
          sent_at: string
          sent_to: string | null
          status: string | null
        }
        Insert: {
          course_id?: string | null
          created_at?: string
          id?: string
          message: string
          notification_type?: string | null
          sent_at?: string
          sent_to?: string | null
          status?: string | null
        }
        Update: {
          course_id?: string | null
          created_at?: string
          id?: string
          message?: string
          notification_type?: string | null
          sent_at?: string
          sent_to?: string | null
          status?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "course_notifications_course_id_fkey"
            columns: ["course_id"]
            isOneToOne: false
            referencedRelation: "courses"
            referencedColumns: ["id"]
          },
        ]
      }
      course_renewals: {
        Row: {
          course_id: string | null
          created_at: string
          id: string
          new_end_date: string
          previous_end_date: string
          renewal_type: string | null
          renewed_by: string | null
        }
        Insert: {
          course_id?: string | null
          created_at?: string
          id?: string
          new_end_date: string
          previous_end_date: string
          renewal_type?: string | null
          renewed_by?: string | null
        }
        Update: {
          course_id?: string | null
          created_at?: string
          id?: string
          new_end_date?: string
          previous_end_date?: string
          renewal_type?: string | null
          renewed_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "course_renewals_course_id_fkey"
            columns: ["course_id"]
            isOneToOne: false
            referencedRelation: "courses"
            referencedColumns: ["id"]
          },
        ]
      }
      courses: {
        Row: {
          auto_renewal: boolean | null
          code: string
          course_type: string | null
          created_at: string
          created_by: string | null
          deleted_at: string | null
          description: string | null
          duration_months: number | null
          end_date: string | null
          id: string
          name: string
          parent_course_id: string | null
          price: number | null
          renewal_period_months: number | null
          section_order: number | null
          start_date: string | null
          updated_at: string
          updated_by: string | null
        }
        Insert: {
          auto_renewal?: boolean | null
          code: string
          course_type?: string | null
          created_at?: string
          created_by?: string | null
          deleted_at?: string | null
          description?: string | null
          duration_months?: number | null
          end_date?: string | null
          id?: string
          name: string
          parent_course_id?: string | null
          price?: number | null
          renewal_period_months?: number | null
          section_order?: number | null
          start_date?: string | null
          updated_at?: string
          updated_by?: string | null
        }
        Update: {
          auto_renewal?: boolean | null
          code?: string
          course_type?: string | null
          created_at?: string
          created_by?: string | null
          deleted_at?: string | null
          description?: string | null
          duration_months?: number | null
          end_date?: string | null
          id?: string
          name?: string
          parent_course_id?: string | null
          price?: number | null
          renewal_period_months?: number | null
          section_order?: number | null
          start_date?: string | null
          updated_at?: string
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "courses_parent_course_id_fkey"
            columns: ["parent_course_id"]
            isOneToOne: false
            referencedRelation: "courses"
            referencedColumns: ["id"]
          },
        ]
      }
      exam_results: {
        Row: {
          created_at: string
          exam_id: string
          grade: string | null
          id: string
          marks: number
          remarks: string | null
          student_id: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          exam_id: string
          grade?: string | null
          id?: string
          marks: number
          remarks?: string | null
          student_id: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          exam_id?: string
          grade?: string | null
          id?: string
          marks?: number
          remarks?: string | null
          student_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "exam_results_exam_id_fkey"
            columns: ["exam_id"]
            isOneToOne: false
            referencedRelation: "exams"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "exam_results_student_id_fkey"
            columns: ["student_id"]
            isOneToOne: false
            referencedRelation: "students"
            referencedColumns: ["id"]
          },
        ]
      }
      exams: {
        Row: {
          class: string
          course_id: string | null
          created_at: string
          date: string
          id: string
          level_id: string | null
          name: string
          status: string | null
          subject: string
          time: string
          total_marks: number
          type: string
          updated_at: string
        }
        Insert: {
          class: string
          course_id?: string | null
          created_at?: string
          date: string
          id?: string
          level_id?: string | null
          name: string
          status?: string | null
          subject: string
          time: string
          total_marks?: number
          type: string
          updated_at?: string
        }
        Update: {
          class?: string
          course_id?: string | null
          created_at?: string
          date?: string
          id?: string
          level_id?: string | null
          name?: string
          status?: string | null
          subject?: string
          time?: string
          total_marks?: number
          type?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "exams_course_id_fkey"
            columns: ["course_id"]
            isOneToOne: false
            referencedRelation: "courses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "exams_level_id_fkey"
            columns: ["level_id"]
            isOneToOne: false
            referencedRelation: "levels"
            referencedColumns: ["id"]
          },
        ]
      }
      general_settings: {
        Row: {
          created_at: string
          id: string
          maintenance_mode: boolean
          school_name: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: string
          maintenance_mode?: boolean
          school_name?: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          maintenance_mode?: boolean
          school_name?: string
          updated_at?: string
        }
        Relationships: []
      }
      levels: {
        Row: {
          code: string | null
          course_id: string | null
          created_at: string
          description: string | null
          id: string
          name: string
          updated_at: string
        }
        Insert: {
          code?: string | null
          course_id?: string | null
          created_at?: string
          description?: string | null
          id?: string
          name: string
          updated_at?: string
        }
        Update: {
          code?: string | null
          course_id?: string | null
          created_at?: string
          description?: string | null
          id?: string
          name?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "levels_course_id_fkey"
            columns: ["course_id"]
            isOneToOne: false
            referencedRelation: "courses"
            referencedColumns: ["id"]
          },
        ]
      }
      payment_types: {
        Row: {
          created_at: string | null
          description: string | null
          id: string
          name: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: string
          name: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: string
          name?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      payments: {
        Row: {
          amount: number
          created_at: string
          date_paid: string
          id: string
          notes: string | null
          payment_due_date: string
          payment_type_id: string | null
          status: string
          student_id: string
          updated_at: string
        }
        Insert: {
          amount: number
          created_at?: string
          date_paid: string
          id?: string
          notes?: string | null
          payment_due_date: string
          payment_type_id?: string | null
          status: string
          student_id: string
          updated_at?: string
        }
        Update: {
          amount?: number
          created_at?: string
          date_paid?: string
          id?: string
          notes?: string | null
          payment_due_date?: string
          payment_type_id?: string | null
          status?: string
          student_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "payments_payment_type_id_fkey"
            columns: ["payment_type_id"]
            isOneToOne: false
            referencedRelation: "payment_types"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payments_student_id_fkey"
            columns: ["student_id"]
            isOneToOne: false
            referencedRelation: "students"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          avatar_url: string | null
          created_at: string
          full_name: string | null
          id: string
          role: Database["public"]["Enums"]["user_role"] | null
          updated_at: string
          username: string | null
        }
        Insert: {
          avatar_url?: string | null
          created_at?: string
          full_name?: string | null
          id: string
          role?: Database["public"]["Enums"]["user_role"] | null
          updated_at?: string
          username?: string | null
        }
        Update: {
          avatar_url?: string | null
          created_at?: string
          full_name?: string | null
          id?: string
          role?: Database["public"]["Enums"]["user_role"] | null
          updated_at?: string
          username?: string | null
        }
        Relationships: []
      }
      student_courses: {
        Row: {
          course_id: string | null
          created_at: string | null
          enrollment_date: string | null
          id: string
          status: string | null
          student_id: string | null
          updated_at: string | null
        }
        Insert: {
          course_id?: string | null
          created_at?: string | null
          enrollment_date?: string | null
          id?: string
          status?: string | null
          student_id?: string | null
          updated_at?: string | null
        }
        Update: {
          course_id?: string | null
          created_at?: string | null
          enrollment_date?: string | null
          id?: string
          status?: string | null
          student_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "student_courses_course_id_fkey"
            columns: ["course_id"]
            isOneToOne: false
            referencedRelation: "courses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "student_courses_student_id_fkey"
            columns: ["student_id"]
            isOneToOne: false
            referencedRelation: "students"
            referencedColumns: ["id"]
          },
        ]
      }
      students: {
        Row: {
          address: string | null
          course_id: string
          created_at: string
          date_of_birth: string
          date_of_registration: string | null
          enrollment_status: string | null
          gender: string
          id: string
          level_id: string
          mobile_number: string | null
          name: string
          parent_email: string | null
          parent_mobile: string | null
          parent_name: string | null
          parent_occupation: string | null
          parent_whatsapp: string | null
          passport_picture: string | null
          payment_status: string | null
          student_id: string
          total_fees_due: number | null
          total_fees_paid: number | null
          updated_at: string
          whatsapp_number: string | null
        }
        Insert: {
          address?: string | null
          course_id: string
          created_at?: string
          date_of_birth: string
          date_of_registration?: string | null
          enrollment_status?: string | null
          gender: string
          id?: string
          level_id: string
          mobile_number?: string | null
          name: string
          parent_email?: string | null
          parent_mobile?: string | null
          parent_name?: string | null
          parent_occupation?: string | null
          parent_whatsapp?: string | null
          passport_picture?: string | null
          payment_status?: string | null
          student_id: string
          total_fees_due?: number | null
          total_fees_paid?: number | null
          updated_at?: string
          whatsapp_number?: string | null
        }
        Update: {
          address?: string | null
          course_id?: string
          created_at?: string
          date_of_birth?: string
          date_of_registration?: string | null
          enrollment_status?: string | null
          gender?: string
          id?: string
          level_id?: string
          mobile_number?: string | null
          name?: string
          parent_email?: string | null
          parent_mobile?: string | null
          parent_name?: string | null
          parent_occupation?: string | null
          parent_whatsapp?: string | null
          passport_picture?: string | null
          payment_status?: string | null
          student_id?: string
          total_fees_due?: number | null
          total_fees_paid?: number | null
          updated_at?: string
          whatsapp_number?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "students_course_id_fkey"
            columns: ["course_id"]
            isOneToOne: false
            referencedRelation: "courses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "students_level_id_fkey"
            columns: ["level_id"]
            isOneToOne: false
            referencedRelation: "levels"
            referencedColumns: ["id"]
          },
        ]
      }
      transactions: {
        Row: {
          amount: number
          category: string
          created_at: string | null
          date: string
          description: string
          id: string
          notes: string | null
          status: string
          student_id: string | null
          type: string
          updated_at: string | null
        }
        Insert: {
          amount: number
          category: string
          created_at?: string | null
          date?: string
          description: string
          id?: string
          notes?: string | null
          status: string
          student_id?: string | null
          type: string
          updated_at?: string | null
        }
        Update: {
          amount?: number
          category?: string
          created_at?: string | null
          date?: string
          description?: string
          id?: string
          notes?: string | null
          status?: string
          student_id?: string | null
          type?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "transactions_student_id_fkey"
            columns: ["student_id"]
            isOneToOne: false
            referencedRelation: "students"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      check_expiring_courses: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      enable_typescript_emit: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      export_attendance_records: {
        Args: {
          start_date: string
          end_date: string
        }
        Returns: {
          date: string
          student_name: string
          student_id: string
          status: string
          time_in: string
          time_out: string
          notes: string
        }[]
      }
      get_student_full_info: {
        Args: {
          p_student_id: string
        }
        Returns: Json
      }
    }
    Enums: {
      activity_type:
        | "login"
        | "logout"
        | "student_created"
        | "student_updated"
        | "student_deleted"
        | "payment_created"
        | "payment_updated"
        | "course_created"
        | "course_updated"
        | "level_created"
        | "level_updated"
        | "transaction_created"
        | "transaction_updated"
        | "settings_updated"
      app_role: "super_admin" | "enrollment_admin" | "finance_admin"
      user_role: "super_admin" | "enrollment_admin" | "finance_admin"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type PublicSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  PublicTableNameOrOptions extends
    | keyof (PublicSchema["Tables"] & PublicSchema["Views"])
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
        Database[PublicTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
      Database[PublicTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : PublicTableNameOrOptions extends keyof (PublicSchema["Tables"] &
        PublicSchema["Views"])
    ? (PublicSchema["Tables"] &
        PublicSchema["Views"])[PublicTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  PublicEnumNameOrOptions extends
    | keyof PublicSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends PublicEnumNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = PublicEnumNameOrOptions extends { schema: keyof Database }
  ? Database[PublicEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : PublicEnumNameOrOptions extends keyof PublicSchema["Enums"]
    ? PublicSchema["Enums"][PublicEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof PublicSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof PublicSchema["CompositeTypes"]
    ? PublicSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never
