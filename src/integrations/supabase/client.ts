// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from '@/types/supabase';

const SUPABASE_URL = "https://ikxubhvftplosmchdzfn.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlreHViaHZmdHBsb3NtY2hkemZuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg3NjQyNjUsImV4cCI6MjA1NDM0MDI2NX0.oYaiIwJpus64bBoEpb3lXHiZ__aCp43XgLfO2PeGeMM";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);