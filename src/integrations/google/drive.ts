import { google } from 'googleapis';
import { Readable } from 'stream';
import { logActivity } from '@/utils/activity-logger';

// Define folder paths
export const FOLDERS = {
  PROFILE_PHOTOS: 'profile_photos',
  MATERIALS: 'materials',
  SUBMISSIONS: 'submissions',
};

// Interface for file metadata
export interface DriveFileMetadata {
  id: string;
  name: string;
  mimeType: string;
  webViewLink: string;
  webContentLink?: string;
  size?: string;
  createdTime?: string;
}

// Initialize the Google Drive API client
const initializeDriveClient = () => {
  try {
    // Service account authentication
    const auth = new google.auth.GoogleAuth({
      credentials: {
        client_email: import.meta.env.VITE_GOOGLE_SERVICE_ACCOUNT_EMAIL,
        private_key: import.meta.env.VITE_GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      },
      scopes: ['https://www.googleapis.com/auth/drive'],
    });

    // Create the drive client
    return google.drive({ version: 'v3', auth });
  } catch (error) {
    console.error('Error initializing Google Drive client:', error);
    throw new Error('Failed to initialize Google Drive client');
  }
};

// Get or create a folder in Google Drive
export const getOrCreateFolder = async (folderName: string, parentFolderId?: string): Promise<string> => {
  try {
    const drive = initializeDriveClient();
    
    // Check if folder already exists
    const query = parentFolderId 
      ? `name='${folderName}' and mimeType='application/vnd.google-apps.folder' and '${parentFolderId}' in parents and trashed=false`
      : `name='${folderName}' and mimeType='application/vnd.google-apps.folder' and trashed=false`;
    
    const response = await drive.files.list({
      q: query,
      fields: 'files(id, name)',
      spaces: 'drive',
    });

    // If folder exists, return its ID
    if (response.data.files && response.data.files.length > 0) {
      return response.data.files[0].id!;
    }

    // If folder doesn't exist, create it
    const fileMetadata = {
      name: folderName,
      mimeType: 'application/vnd.google-apps.folder',
      parents: parentFolderId ? [parentFolderId] : undefined,
    };

    const folder = await drive.files.create({
      requestBody: fileMetadata,
      fields: 'id',
    });

    if (!folder.data.id) {
      throw new Error('Failed to create folder');
    }

    // Make the folder publicly accessible for viewing
    await drive.permissions.create({
      fileId: folder.data.id,
      requestBody: {
        role: 'reader',
        type: 'anyone',
      },
    });

    return folder.data.id;
  } catch (error) {
    console.error(`Error getting or creating folder ${folderName}:`, error);
    throw new Error(`Failed to get or create folder: ${folderName}`);
  }
};

// Upload a file to Google Drive
export const uploadFile = async (
  file: File | Blob | Buffer,
  fileName: string,
  folderPath: string,
  mimeType?: string
): Promise<DriveFileMetadata> => {
  try {
    const drive = initializeDriveClient();
    
    // Split folder path and ensure all folders exist
    const folderNames = folderPath.split('/').filter(name => name.trim() !== '');
    let parentId: string | undefined = undefined;
    
    // Create folder hierarchy if needed
    for (const folderName of folderNames) {
      parentId = await getOrCreateFolder(folderName, parentId);
    }
    
    // Convert file to buffer if it's a File or Blob
    let fileBuffer: Buffer;
    if (file instanceof File || file instanceof Blob) {
      const arrayBuffer = await file.arrayBuffer();
      fileBuffer = Buffer.from(arrayBuffer);
    } else {
      fileBuffer = file;
    }
    
    // Create a readable stream from the buffer
    const fileStream = new Readable();
    fileStream.push(fileBuffer);
    fileStream.push(null);
    
    // Upload the file
    const fileMetadata = {
      name: fileName,
      parents: parentId ? [parentId] : undefined,
    };
    
    const media = {
      mimeType: mimeType || 'application/octet-stream',
      body: fileStream,
    };
    
    const uploadedFile = await drive.files.create({
      requestBody: fileMetadata,
      media: media,
      fields: 'id, name, mimeType, webViewLink, webContentLink, size, createdTime',
    });
    
    if (!uploadedFile.data.id) {
      throw new Error('Failed to upload file');
    }
    
    // Make the file publicly accessible for viewing
    await drive.permissions.create({
      fileId: uploadedFile.data.id,
      requestBody: {
        role: 'reader',
        type: 'anyone',
      },
    });
    
    await logActivity('file_uploaded_to_drive', {
      fileName,
      folderId: parentId,
      fileId: uploadedFile.data.id,
    });
    
    return uploadedFile.data as DriveFileMetadata;
  } catch (error) {
    console.error(`Error uploading file ${fileName}:`, error);
    throw new Error(`Failed to upload file: ${fileName}`);
  }
};

// Delete a file from Google Drive
export const deleteFile = async (fileId: string): Promise<void> => {
  try {
    const drive = initializeDriveClient();
    await drive.files.delete({ fileId });
    
    await logActivity('file_deleted_from_drive', {
      fileId,
    });
  } catch (error) {
    console.error(`Error deleting file ${fileId}:`, error);
    throw new Error(`Failed to delete file: ${fileId}`);
  }
};

// Get file metadata from Google Drive
export const getFileMetadata = async (fileId: string): Promise<DriveFileMetadata> => {
  try {
    const drive = initializeDriveClient();
    const response = await drive.files.get({
      fileId,
      fields: 'id, name, mimeType, webViewLink, webContentLink, size, createdTime',
    });
    
    return response.data as DriveFileMetadata;
  } catch (error) {
    console.error(`Error getting file metadata for ${fileId}:`, error);
    throw new Error(`Failed to get file metadata: ${fileId}`);
  }
};

// List files in a folder
export const listFiles = async (folderPath: string): Promise<DriveFileMetadata[]> => {
  try {
    const drive = initializeDriveClient();
    
    // Split folder path and ensure all folders exist
    const folderNames = folderPath.split('/').filter(name => name.trim() !== '');
    let parentId: string | undefined = undefined;
    
    // Navigate to the target folder
    for (const folderName of folderNames) {
      parentId = await getOrCreateFolder(folderName, parentId);
    }
    
    if (!parentId) {
      throw new Error(`Folder not found: ${folderPath}`);
    }
    
    // List files in the folder
    const response = await drive.files.list({
      q: `'${parentId}' in parents and trashed=false`,
      fields: 'files(id, name, mimeType, webViewLink, webContentLink, size, createdTime)',
      spaces: 'drive',
    });
    
    return response.data.files as DriveFileMetadata[];
  } catch (error) {
    console.error(`Error listing files in folder ${folderPath}:`, error);
    throw new Error(`Failed to list files in folder: ${folderPath}`);
  }
}; 